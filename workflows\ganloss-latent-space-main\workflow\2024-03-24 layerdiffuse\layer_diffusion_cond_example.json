{"last_node_id": 35, "last_link_id": 52, "nodes": [{"id": 4, "type": "CheckpointLoaderSimple", "pos": [5, 479], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [38], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [22, 49], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_v8Rundiffusion.safetensors"]}, {"id": 29, "type": "VAEEncode", "pos": [212, -22], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 51}, {"name": "vae", "type": "VAE", "link": 49, "slot_index": 1}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [47], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 30, "type": "LoadImage", "pos": [-363, 209], "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [50], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["309219693-e7e2d80e-ffbe-4724-812a-5139a88027e3.png", "image"]}, {"id": 20, "type": "PreviewImage", "pos": [1556, 138], "size": {"0": 611.2340087890625, "1": 633.9354858398438}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 29}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [39], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["old man sitting, high quality\n\n"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [40], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 34, "type": "PreviewImage", "pos": [213, -346], "size": {"0": 210, "1": 246}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 52}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 14, "type": "VAEDecode", "pos": [1275, 198], "size": {"0": 210, "1": 46}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 21}, {"name": "vae", "type": "VAE", "link": 22, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [913, 181], "size": {"0": 315, "1": 262}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 41}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 45}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [21], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [100676796092754, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [475, 666], "size": {"0": 315, "1": 106}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 33, "type": "ImageResize+", "pos": [-146, -16], "size": {"0": 315, "1": 170}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 50}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [51, 52], "shape": 3, "slot_index": 0}, {"name": "width", "type": "INT", "links": null, "shape": 3}, {"name": "height", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1024, 1024, "nearest", false]}, {"id": 28, "type": "LayeredDiffusionCondApply", "pos": [465, -26], "size": {"0": 315, "1": 142}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 38}, {"name": "cond", "type": "CONDITIONING", "link": 39}, {"name": "uncond", "type": "CONDITIONING", "link": 40}, {"name": "latent", "type": "LATENT", "link": 47}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [41], "shape": 3, "slot_index": 0}, {"name": "CONDITIONING", "type": "CONDITIONING", "links": [46], "shape": 3, "slot_index": 1}, {"name": "CONDITIONING", "type": "CONDITIONING", "links": [45], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "LayeredDiffusionCondApply"}, "widgets_values": ["SDXL, Background", 1], "color": "#232", "bgcolor": "#353"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [5, 4, 1, 7, 0, "CLIP"], [21, 3, 0, 14, 0, "LATENT"], [22, 4, 2, 14, 1, "VAE"], [29, 14, 0, 20, 0, "IMAGE"], [38, 4, 0, 28, 0, "MODEL"], [39, 6, 0, 28, 1, "CONDITIONING"], [40, 7, 0, 28, 2, "CONDITIONING"], [41, 28, 0, 3, 0, "MODEL"], [45, 28, 2, 3, 2, "CONDITIONING"], [46, 28, 1, 3, 1, "CONDITIONING"], [47, 29, 0, 28, 3, "LATENT"], [49, 4, 2, 29, 1, "VAE"], [50, 30, 0, 33, 0, "IMAGE"], [51, 33, 0, 29, 0, "IMAGE"], [52, 33, 0, 34, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}