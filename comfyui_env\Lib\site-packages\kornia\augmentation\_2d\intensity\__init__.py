# LICENSE HEADER MANAGED BY add-license-header
#
# Copyright 2018 Kornia Team
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

from kornia.augmentation._2d.intensity.auto_contrast import RandomAutoContrast
from kornia.augmentation._2d.intensity.box_blur import RandomBoxBlur
from kornia.augmentation._2d.intensity.brightness import RandomBrightness
from kornia.augmentation._2d.intensity.channel_dropout import RandomChannelDropout
from kornia.augmentation._2d.intensity.channel_shuffle import RandomChannelShuffle
from kornia.augmentation._2d.intensity.clahe import <PERSON><PERSON>lahe
from kornia.augmentation._2d.intensity.color_jiggle import ColorJiggle
from kornia.augmentation._2d.intensity.color_jitter import ColorJitter
from kornia.augmentation._2d.intensity.contrast import RandomContrast
from kornia.augmentation._2d.intensity.denormalize import Denormalize
from kornia.augmentation._2d.intensity.dissolving import RandomDissolving
from kornia.augmentation._2d.intensity.equalize import RandomEqualize
from kornia.augmentation._2d.intensity.erasing import RandomErasing
from kornia.augmentation._2d.intensity.gamma import RandomGamma
from kornia.augmentation._2d.intensity.gaussian_blur import RandomGaussianBlur
from kornia.augmentation._2d.intensity.gaussian_illumination import RandomGaussianIllumination
from kornia.augmentation._2d.intensity.gaussian_noise import RandomGaussianNoise
from kornia.augmentation._2d.intensity.grayscale import RandomGrayscale
from kornia.augmentation._2d.intensity.hue import RandomHue
from kornia.augmentation._2d.intensity.invert import RandomInvert
from kornia.augmentation._2d.intensity.jpeg import RandomJPEG
from kornia.augmentation._2d.intensity.linear_illumination import (
    RandomLinearCornerIllumination,
    RandomLinearIllumination,
)
from kornia.augmentation._2d.intensity.median_blur import RandomMedianBlur
from kornia.augmentation._2d.intensity.motion_blur import RandomMotionBlur
from kornia.augmentation._2d.intensity.normalize import Normalize
from kornia.augmentation._2d.intensity.planckian_jitter import RandomPlanckianJitter
from kornia.augmentation._2d.intensity.plasma import RandomPlasmaBrightness, RandomPlasmaContrast, RandomPlasmaShadow
from kornia.augmentation._2d.intensity.posterize import RandomPosterize
from kornia.augmentation._2d.intensity.random_rain import RandomRain
from kornia.augmentation._2d.intensity.random_rgb_shift import RandomRGBShift
from kornia.augmentation._2d.intensity.random_snow import RandomSnow
from kornia.augmentation._2d.intensity.salt_pepper_noise import RandomSaltAndPepperNoise
from kornia.augmentation._2d.intensity.saturation import RandomSaturation
from kornia.augmentation._2d.intensity.sharpness import RandomSharpness
from kornia.augmentation._2d.intensity.solarize import RandomSolarize
