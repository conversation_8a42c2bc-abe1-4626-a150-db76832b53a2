#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API vander {
  using schema = at::Tensor (const at::Tensor &, ::std::optional<int64_t>, bool);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::vander";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "vander(Tensor x, int? N=None, bool increasing=False) -> Tensor";
  static at::Tensor call(const at::Tensor & x, ::std::optional<int64_t> N, bool increasing);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & x, ::std::optional<int64_t> N, bool increasing);
};

}} // namespace at::_ops
