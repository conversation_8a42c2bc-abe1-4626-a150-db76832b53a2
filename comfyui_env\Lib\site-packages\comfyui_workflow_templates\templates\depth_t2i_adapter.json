{"last_node_id": 47, "last_link_id": 66, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [920, 90], "size": [315, 474], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 46}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 62}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 63}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1008774331380475, "randomize", 20, 7, "euler_ancestral", "normal", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [549.7071533203125, 260.3621520996094], "size": [315, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [84.3577651977539, 309.1323547363281], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 47}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [61]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cluttered, dark lighting, oversaturated colors, unrealistic, cartoon, anime, sketch, low quality, blurry, noise, grainy, deformed furniture, cropped image, out of frame, extra limbs, poorly drawn, bad proportions, disfigured, bad anatomy"]}, {"id": 8, "type": "VAEDecode", "pos": [1270, 90], "size": [210, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 49}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1272, 184], "size": [570, 660], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 20, "type": "LoadImage", "pos": [110.91259765625, 713.4210815429688], "size": [320, 420], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [65]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["depth-t2i-adapter_input.png", "image", ""]}, {"id": 24, "type": "CLIPTextEncode", "pos": [80, 90], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 48}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [60]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["modern minimalist living room, pristine white sofa, wooden coffee table, large windows, city view with trees, cream color palette, beige carpet, wall mounted TV, white TV cabinet, arc floor lamp, indoor plants, natural lighting, spacious interior, clean lines, luxury apartment, professional photography, architectural digest, interior design magazine photo\n\nmasterpiece, best quality, photorealistic, 8k resolution, detailed texture, soft cinematic lighting"]}, {"id": 31, "type": "ControlNetLoader", "pos": [99.87442016601562, 602.945068359375], "size": [340, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [64]}], "properties": {"Node name for S&R": "ControlNetLoader", "models": [{"name": "controlnetT2IAdapter_t2iAdapterDepth.safetensors", "url": "https://civitai.com/api/download/models/20335?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "controlnet"}]}, "widgets_values": ["controlnetT2IAdapter_t2iAdapterDepth.safetensors"]}, {"id": 33, "type": "CheckpointLoaderSimple", "pos": [-350, 100], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [46]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [47, 48]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [49, 66]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "interiordesignsuperm_v2.safetensors", "url": "https://civitai.com/api/download/models/93152?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["interiordesignsuperm_v2.safetensors"]}, {"id": 45, "type": "ControlNetApplyAdvanced", "pos": [487.5998840332031, 608.9225463867188], "size": [315, 186], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 60}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 61}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 64}, {"label": "image", "name": "image", "type": "IMAGE", "link": 65}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": 66}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [62]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [63]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 47, "type": "<PERSON>downNote", "pos": [-352, 248], "size": [336, 152], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ControlNet - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets) — Overview\n> \n> [Depth T2I Adapter Usage - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/depth-t2i-adapter) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [46, 33, 0, 3, 0, "MODEL"], [47, 33, 1, 7, 0, "CLIP"], [48, 33, 1, 24, 0, "CLIP"], [49, 33, 2, 8, 1, "VAE"], [60, 24, 0, 45, 0, "CONDITIONING"], [61, 7, 0, 45, 1, "CONDITIONING"], [62, 45, 0, 3, 1, "CONDITIONING"], [63, 45, 1, 3, 2, "CONDITIONING"], [64, 31, 0, 45, 2, "CONTROL_NET"], [65, 20, 0, 45, 3, "IMAGE"], [66, 33, 2, 45, 4, "VAE"]], "groups": [{"id": 2, "title": "Apply Depth T2I-Adapter", "bounding": [88, 520, 721.6871948242188, 623.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"node_versions": {"comfy-core": "0.3.14"}}, "version": 0.4}