#pragma once

// Common
#include <torch/nn/modules/common.h>

// Containers
#include <torch/nn/modules/container/any.h>
#include <torch/nn/modules/container/functional.h>
#include <torch/nn/modules/container/moduledict.h>
#include <torch/nn/modules/container/modulelist.h>
#include <torch/nn/modules/container/named_any.h>
#include <torch/nn/modules/container/parameterdict.h>
#include <torch/nn/modules/container/parameterlist.h>
#include <torch/nn/modules/container/sequential.h>

// Layers
#include <torch/nn/modules/activation.h>
#include <torch/nn/modules/adaptive.h>
#include <torch/nn/modules/batchnorm.h>
#include <torch/nn/modules/conv.h>
#include <torch/nn/modules/distance.h>
#include <torch/nn/modules/dropout.h>
#include <torch/nn/modules/embedding.h>
#include <torch/nn/modules/fold.h>
#include <torch/nn/modules/instancenorm.h>
#include <torch/nn/modules/linear.h>
#include <torch/nn/modules/loss.h>
#include <torch/nn/modules/normalization.h>
#include <torch/nn/modules/padding.h>
#include <torch/nn/modules/pixelshuffle.h>
#include <torch/nn/modules/pooling.h>
#include <torch/nn/modules/rnn.h>
#include <torch/nn/modules/transformer.h>
#include <torch/nn/modules/transformercoder.h>
#include <torch/nn/modules/transformerlayer.h>
#include <torch/nn/modules/upsampling.h>
