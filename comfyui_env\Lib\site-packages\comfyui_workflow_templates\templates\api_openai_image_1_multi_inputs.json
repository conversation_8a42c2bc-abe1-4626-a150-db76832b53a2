{"id": "d68d25b9-7b7a-4aee-a962-7db6da034203", "revision": 0, "last_node_id": 9, "last_link_id": 5, "nodes": [{"id": 2, "type": "LoadImage", "pos": [4650, -720], "size": [342.5999755859375, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["hat.webp", "image"]}, {"id": 6, "type": "SaveImage", "pos": [5710, -890], "size": [315, 270], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 5}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 3, "type": "ImageBatch", "pos": [5030, -890], "size": [210, 46], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 3}, {"name": "image2", "type": "IMAGE", "link": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 4, "type": "LoadImage", "pos": [4650, -1080], "size": [342.5999755859375, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.webp", "image"]}, {"id": 1, "type": "OpenAIGPTImage1", "pos": [5278.32177734375, -894.1099243164062], "size": [400, 252], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 2}, {"name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5]}], "properties": {"Node name for S&R": "OpenAIGPTImage1"}, "widgets_values": ["A cute cat sitting on a boat, holding a glass of champagne, with the vast ocean in the background, wearing a red hat and red - framed sunglasses, in the style of Studio Ghibli", 1384371958, "randomize", "high", "opaque", "auto", 1], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "<PERSON>downNote", "pos": [4270, -1070], "size": [343.3236083984375, 101.29678344726562], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/openai/gpt-image-1) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/gpt-image-1)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "<PERSON>downNote", "pos": [4276.142578125, -923.7203979492188], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 2, 0, 3, 1, "IMAGE"], [2, 3, 0, 1, 0, "IMAGE"], [3, 4, 0, 3, 0, "IMAGE"], [5, 1, 0, 6, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6115909044841917, "offset": [-3379.7226093317395, 1314.564411047733]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}