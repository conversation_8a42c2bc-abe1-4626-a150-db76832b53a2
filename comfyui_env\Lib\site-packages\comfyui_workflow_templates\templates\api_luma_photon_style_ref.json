{"id": "56bd4347-9347-49da-a167-3db05d3bcc28", "revision": 0, "last_node_id": 55, "last_link_id": 62, "nodes": [{"id": 43, "type": "ImageBatch", "pos": [546.4483032226562, 2604.6484375], "size": [140, 46], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 50}, {"name": "image2", "type": "IMAGE", "link": 51}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [54]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 47, "type": "ImageBatch", "pos": [546.4483032226562, 2704.6484375], "size": [140, 46], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 52}, {"name": "image2", "type": "IMAGE", "link": 53}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [55]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 46, "type": "LoadImage", "pos": [446.448486328125, 2824.6484375], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [53]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["charater_image.png", "image"]}, {"id": 48, "type": "ImageBatch", "pos": [876.4483642578125, 2654.6484375], "size": [140, 46], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 54}, {"name": "image2", "type": "IMAGE", "link": 55}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [61]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 41, "type": "LoadImage", "pos": [751.3177490234375, 2047.623046875], "size": [270, 314.0001220703125], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [44]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 52, "type": "LoadImage", "pos": [1076.8538818359375, 2052.3544921875], "size": [270, 314.0001220703125], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [60]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "sytleref", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["style.png", "image"]}, {"id": 42, "type": "LumaReferenceNode", "pos": [751.3177490234375, 2416.752197265625], "size": [270, 78], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 44}, {"name": "luma_ref", "shape": 7, "type": "LUMA_REF", "link": 46}], "outputs": [{"name": "luma_ref", "type": "LUMA_REF", "links": [62]}], "properties": {"Node name for S&R": "LumaReferenceNode"}, "widgets_values": [0.5000000000000001]}, {"id": 36, "type": "LoadImage", "pos": [-163.55154418945312, 2824.6484375], "size": [274.080078125, 314], "flags": {}, "order": 3, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [50]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["charater_image.png", "image"]}, {"id": 44, "type": "LoadImage", "pos": [146.44847106933594, 2814.6484375], "size": [274.080078125, 314], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [51]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["charater_image.png", "image"]}, {"id": 45, "type": "LoadImage", "pos": [746.4483032226562, 2824.6484375], "size": [274.080078125, 314], "flags": {}, "order": 5, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [52]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["charater_image.png", "image"]}, {"id": 30, "type": "LumaReferenceNode", "pos": [-191.53564453125, 2416.752197265625], "size": [270, 78], "flags": {}, "order": 14, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 48}, {"name": "luma_ref", "shape": 7, "type": "LUMA_REF", "link": null}], "outputs": [{"name": "luma_ref", "type": "LUMA_REF", "links": [33]}], "properties": {"Node name for S&R": "LumaReferenceNode"}, "widgets_values": [0.5000000000000001]}, {"id": 38, "type": "LoadImage", "pos": [-195.6481475830078, 2047.623046875], "size": [270, 314.0001220703125], "flags": {}, "order": 6, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [48]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 26, "type": "LoadImage", "pos": [120.00718688964844, 2047.623046875], "size": [270, 314.0001220703125], "flags": {}, "order": 7, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [49]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 37, "type": "LumaReferenceNode", "pos": [122.74884796142578, 2416.752197265625], "size": [270, 78], "flags": {}, "order": 16, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 49}, {"name": "luma_ref", "shape": 7, "type": "LUMA_REF", "link": 33}], "outputs": [{"name": "luma_ref", "type": "LUMA_REF", "links": [45]}], "properties": {"Node name for S&R": "LumaReferenceNode"}, "widgets_values": [0.5000000000000001]}, {"id": 24, "type": "LumaImageNode", "pos": [1078.1558837890625, 2570.47119140625], "size": [400, 248], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "image_luma_ref", "shape": 7, "type": "LUMA_REF", "link": 62}, {"name": "style_image", "shape": 7, "type": "IMAGE", "link": 60}, {"name": "character_image", "shape": 7, "type": "IMAGE", "link": 61}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30]}], "properties": {"Node name for S&R": "LumaImageNode"}, "widgets_values": ["a man with a ice crown on top of his head in the style of liquid chrome style ", "photon-1", "3:4", 797784790034618, "randomize", 1], "color": "#322", "bgcolor": "#533"}, {"id": 32, "type": "SaveImage", "pos": [1501.************, 2574.7333984375], "size": [270, 58], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 30}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 54, "type": "<PERSON>downNote", "pos": [1082.6822509765625, 2871.91455078125], "size": [389.2633972167969, 320.4095764160156], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "About Luma Image to Image", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/luma/luma-text-to-image) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/luma/luma-text-to-image) \n\n1 - \"image_luma_ref\" and \"charater_image\" support a maximum of 4 reference image inputs.\n\n2 - The larger the \"style_image_weight\", the more obvious the style.\n\n3 - You can right - click on the corresponding node group to set the \"mode\" to \"always\" to enable the corresponding image output.\n\n4 - \"Batch Images\" requires both inputs to exist simultaneously. By mixing \"load image\" and \"Batch Images\", combinations with different numbers of input images can be achieved.\n\n---\n\n1 - “image_luma_ref” 和 “charater_image” 最多支持 4 张参考图片输入\n\n2 - “style_image_weight” 越大，风格越明显\n\n3 - 可以在对应节点分组上右键来设置“模式(mode)”为“总是(always)” 来启用对应的图片输出\n\n4 -  \"Batch Images\" 要求同时两个输入同时存在，通过混合 \"load image\" 和  \"Batch Images\" 可以实现不同输入图片数量的组合"], "color": "#432", "bgcolor": "#653"}, {"id": 53, "type": "<PERSON>downNote", "pos": [1499.0423583984375, 2879.613525390625], "size": [414.1007385253906, 235.65411376953125], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 55, "type": "<PERSON>downNote", "pos": [-534.**********, 2466.***********], "size": [285.**********, 134.*************], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "title": "How to enable input images", "properties": {}, "widgets_values": ["You can right - click on the corresponding node group to set the \"mode\" to \"always\" to enable the corresponding image output.\n\n---\n你可以在对应节点分组上右键来设置“模式(mode)”为“总是(always)” 来启用对应的图片输出\n"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "LumaReferenceNode", "pos": [437.0333251953125, 2416.752197265625], "size": [270, 78], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 43}, {"name": "luma_ref", "shape": 7, "type": "LUMA_REF", "link": 45}], "outputs": [{"name": "luma_ref", "type": "LUMA_REF", "links": [46]}], "properties": {"Node name for S&R": "LumaReferenceNode"}, "widgets_values": [0.5000000000000001]}, {"id": 40, "type": "LoadImage", "pos": [435.66253662109375, 2047.623046875], "size": [270, 314.0001220703125], "flags": {}, "order": 11, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [43]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}], "links": [[30, 24, 0, 32, 0, "IMAGE"], [33, 30, 0, 37, 1, "LUMA_REF"], [43, 40, 0, 39, 0, "IMAGE"], [44, 41, 0, 42, 0, "IMAGE"], [45, 37, 0, 39, 1, "LUMA_REF"], [46, 39, 0, 42, 1, "LUMA_REF"], [48, 38, 0, 30, 0, "IMAGE"], [49, 26, 0, 37, 0, "IMAGE"], [50, 36, 0, 43, 0, "IMAGE"], [51, 44, 0, 43, 1, "IMAGE"], [52, 45, 0, 47, 0, "IMAGE"], [53, 46, 0, 47, 1, "IMAGE"], [54, 43, 0, 48, 0, "IMAGE"], [55, 47, 0, 48, 1, "IMAGE"], [60, 52, 0, 24, 1, "IMAGE"], [61, 48, 0, 24, 2, "IMAGE"], [62, 42, 0, 24, 0, "LUMA_REF"]], "groups": [{"id": 1, "title": "image_luma_ref (up to 4)", "bounding": [-205.6481475830078, 1974.0230712890625, 1236.9659423828125, 530.7291259765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "charater_image (up to 4)", "bounding": [-203.5515594482422, 2534.6484375, 1249.1517333984375, 628.8998413085938], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "style_image(1 max)", "bounding": [1066.8538818359375, 1978.7576904296875, 290, 397.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.19.0"}, "version": 0.4}