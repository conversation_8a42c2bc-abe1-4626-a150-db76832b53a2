{"last_node_id": 46, "last_link_id": 70, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [630, 80], "size": [315, 474], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 52}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 66}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 67}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [508527445193039, "randomize", 20, 7, "euler_ancestral", "normal", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [260.2398376464844, 187.9658660888672], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [-220.35018920898438, 299.2201843261719], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 49}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [62]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2),human"]}, {"id": 8, "type": "VAEDecode", "pos": [980, 80], "size": [210, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 51}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [984.3109741210938, 189.0495147705078], "size": [420, 580], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 20, "type": "LoadImage", "pos": [-211.16993713378906, 678.6224975585938], "size": [370, 370], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [65]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["depth_input (1).png", "image"]}, {"id": 24, "type": "CLIPTextEncode", "pos": [-220, 80], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 50}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [61]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["streamlined,special form building,white wall,curtain wall,window,indoor warm light,indoor spaces can be seen through the glass,front square,rain stains on the road,high saturation,masterpiece,high quality,8k,best quality,super Realistic,high detail,clean and tidy, "]}, {"id": 34, "type": "CheckpointLoaderSimple", "pos": [-640, 80], "size": [370, 100], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [52]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [49, 50]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [51]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "architecturerealmix_v11.safetensors", "url": "https://civitai.com/api/download/models/431755?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["architecturerealmix_v11.safetensors"]}, {"id": 42, "type": "ControlNetApplyAdvanced", "pos": [231.18695068359375, 572.88232421875], "size": [315, 186], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 61}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 62}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 70}, {"label": "image", "name": "image", "type": "IMAGE", "link": 65}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [66]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [67]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 45, "type": "ControlNetLoader", "pos": [-200.28060913085938, 573.9783325195312], "size": [353.8935852050781, 61.55488967895508], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [70]}], "properties": {"Node name for S&R": "ControlNetLoader", "models": [{"name": "control_v11f1p_sd15_depth_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1p_sd15_depth_fp16.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["control_v11f1p_sd15_depth_fp16.safetensors"]}, {"id": 46, "type": "<PERSON>downNote", "pos": [-640, 232], "size": [336, 152], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ControlNet - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/#t2i-adapter-vs-controlnets) — Overview\n> \n> [Depth ControlNet Usage - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/depth-controlnet) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [49, 34, 1, 7, 0, "CLIP"], [50, 34, 1, 24, 0, "CLIP"], [51, 34, 2, 8, 1, "VAE"], [52, 34, 0, 3, 0, "MODEL"], [61, 24, 0, 42, 0, "CONDITIONING"], [62, 7, 0, 42, 1, "CONDITIONING"], [65, 20, 0, 42, 3, "IMAGE"], [66, 42, 0, 3, 1, "CONDITIONING"], [67, 42, 1, 3, 2, "CONDITIONING"], [70, 45, 0, 42, 2, "CONTROL_NET"]], "groups": [{"id": 2, "title": "Apply ControlNet", "bounding": [-224, 496, 777.35693359375, 559.3401489257812], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"node_versions": {"comfy-core": "0.3.14"}}, "version": 0.4}