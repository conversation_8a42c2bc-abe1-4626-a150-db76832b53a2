einops-0.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
einops-0.8.1.dist-info/METADATA,sha256=eVKVWQRWA8zSdxPIRPK1ckfEjpMTUHe9uQS8U-0YlAM,13451
einops-0.8.1.dist-info/RECORD,,
einops-0.8.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
einops-0.8.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
einops-0.8.1.dist-info/licenses/LICENSE,sha256=MNmENkKW9R_67K1LAe4SfpUlDFBokY1LZvyWIGcj5DQ,1073
einops/__init__.py,sha256=rKyvE64bYMOMXAG4EfMMSVGxgFZ0x51dt82pRsw4lHE,422
einops/__pycache__/__init__.cpython-311.pyc,,
einops/__pycache__/_backends.cpython-311.pyc,,
einops/__pycache__/_torch_specific.cpython-311.pyc,,
einops/__pycache__/array_api.cpython-311.pyc,,
einops/__pycache__/einops.cpython-311.pyc,,
einops/__pycache__/packing.cpython-311.pyc,,
einops/__pycache__/parsing.cpython-311.pyc,,
einops/_backends.py,sha256=7BAiKWdBLLvAiryfhwDbwxglGDW9Dh5FNMT1ERSuzxk,21281
einops/_torch_specific.py,sha256=yMaQeqAZhBLWR1Q-Jv6uRINJfzROhLb-rzKKevpefUU,4138
einops/array_api.py,sha256=jOb8RhwLS9wob_Y_e_KrnBR6ihQPoB2Ly0tfrHr-_Zk,5247
einops/einops.py,sha256=sXvD8SWFqufziyQJKRPmfAGHVN1cMDvYOPNuZ8L1XQU,37569
einops/experimental/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
einops/experimental/__pycache__/__init__.cpython-311.pyc,,
einops/experimental/__pycache__/indexing.cpython-311.pyc,,
einops/experimental/indexing.py,sha256=yFFflW3-kV6_5PPJU7_jOJsJBCWCWlE4dGlu9gwSPXo,121
einops/layers/__init__.py,sha256=vBtnAt2afs4QlqpeFU4dlZNxBuC9IXl3fmilk-2OzHM,3747
einops/layers/__pycache__/__init__.cpython-311.pyc,,
einops/layers/__pycache__/_einmix.cpython-311.pyc,,
einops/layers/__pycache__/flax.cpython-311.pyc,,
einops/layers/__pycache__/keras.cpython-311.pyc,,
einops/layers/__pycache__/oneflow.cpython-311.pyc,,
einops/layers/__pycache__/paddle.cpython-311.pyc,,
einops/layers/__pycache__/tensorflow.cpython-311.pyc,,
einops/layers/__pycache__/torch.cpython-311.pyc,,
einops/layers/_einmix.py,sha256=kNyW05BEG-miMpu1vGQqKXr8OJMe-xweS_aMUguerqM,11125
einops/layers/flax.py,sha256=zFy83gSLRm31cLuKFRvZ82_HsefnXPbRvkKZh1KkC1I,2536
einops/layers/keras.py,sha256=-7So0w94phvf9HdW0xi2mSeBg02qVPvAyfp_1XR02NM,212
einops/layers/oneflow.py,sha256=YEPzz4xc7BDRQfb8ulD3teqQJdbO6qQg7Z4KIPVTLz8,1864
einops/layers/paddle.py,sha256=8cRZQ8BT9vYEczh7pNProuTM_3XjLty2ht2sdvXNFiI,1907
einops/layers/tensorflow.py,sha256=T9uhSVwbXREahc31ARAHoN5K-7zsuS8NRNPdY6Zk1Bc,3324
einops/layers/torch.py,sha256=504G99kEgy7dk1UPBbj9hzJmZkAHwVhMDFN_8J-p3C8,2399
einops/packing.py,sha256=1wN9vMa-coq1RiOR_Nu2aXqgIkrDf3j-N1Q-YN0-xWo,7650
einops/parsing.py,sha256=xbqcvwReLiROEucoegZ20WQiEHlLg0uxo_vYoezKB_4,6746
einops/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
einops/tests/__init__.py,sha256=CLKCGvFTCwmUoUxfa-JGHQQL9c43rTiqienVjT18Phc,3525
einops/tests/__pycache__/__init__.cpython-311.pyc,,
einops/tests/__pycache__/run_tests.cpython-311.pyc,,
einops/tests/__pycache__/test_einsum.cpython-311.pyc,,
einops/tests/__pycache__/test_examples.cpython-311.pyc,,
einops/tests/__pycache__/test_layers.cpython-311.pyc,,
einops/tests/__pycache__/test_ops.cpython-311.pyc,,
einops/tests/__pycache__/test_other.cpython-311.pyc,,
einops/tests/__pycache__/test_packing.cpython-311.pyc,,
einops/tests/__pycache__/test_parsing.cpython-311.pyc,,
einops/tests/run_tests.py,sha256=V8q7_Y9dMksnLsqobqNHbR0j8dVCvo1hOeB2AjQ83No,2887
einops/tests/test_einsum.py,sha256=efS7ynJvwI3VFfTzpYruWnKYhxWyxfRzdbXsMnZFHWM,10977
einops/tests/test_examples.py,sha256=psd__CWOcX2J5APXxsFRvXlpKm9yioTQEnEebEStPKc,11538
einops/tests/test_layers.py,sha256=oQVfU3GzsXq_dJV3ZkYBJGp8EumJEtPRB0eRg6paLa8,18488
einops/tests/test_ops.py,sha256=oljzJ2N4k75QjSthaOAR4k57j0yBKjuSrDac7JuZHak,26998
einops/tests/test_other.py,sha256=ag_h6oz3gMH8Mkd4CR5QXXw9kmeYEefUaGobXEgHjMM,11350
einops/tests/test_packing.py,sha256=PdIOvHr1K7d4F5s1MtCAOIrIkvAniJBw5wnbnpDx3xk,10444
einops/tests/test_parsing.py,sha256=XKFIe10Flpa5gTE1lHXeYt1GKroXkUI-b18Pat_uQeE,4389
