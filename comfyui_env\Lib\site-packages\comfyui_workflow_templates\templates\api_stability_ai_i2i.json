{"id": "e62e2c49-db9e-49bc-a71b-3871b67cc7da", "revision": 0, "last_node_id": 7, "last_link_id": 3, "nodes": [{"id": 5, "type": "LoadImage", "pos": [1555.6781005859375, 1277.0203857421875], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"image": true, "upload": true}}, "widgets_values": ["input.png", "image"]}, {"id": 4, "type": "StabilityStableImageUltraNode", "pos": [1900.96826171875, 1283.7178955078125], "size": [400, 228], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 2}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3]}], "properties": {"Node name for S&R": "StabilityStableImageUltraNode", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"prompt": true, "aspect_ratio": true, "style_preset": true, "seed": true, "image_denoise": true}}, "widgets_values": ["Girl with a <PERSON>ring by <PERSON>, but the character wears a stylish pair of sunglasses", "1:1", "pixel-art", 3366771684, 0.6600000000000001, [false, true]]}, {"id": 6, "type": "SaveImage", "pos": [2331.05078125, 1285.5484619140625], "size": [315, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"filename_prefix": true}}, "widgets_values": ["ComfyUI"]}, {"id": 7, "type": "<PERSON>downNote", "pos": [1219.3486328125, 1278.9378662109375], "size": [315.338134765625, 273.75128173828125], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 4, 0, "IMAGE"], [3, 4, 0, 6, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.****************, "offset": [-1235.1690895449656, -1053.4154681439989]}, "frontendVersion": "1.18.5", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}