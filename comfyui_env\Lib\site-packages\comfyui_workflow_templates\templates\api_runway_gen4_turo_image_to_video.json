{"id": "cbeeb8a3-8519-434e-a8c4-874227569ca0", "revision": 0, "last_node_id": 8, "last_link_id": 7, "nodes": [{"id": 1, "type": "<PERSON>downNote", "pos": [1100, 1420], "size": [390, 320], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "LoadImage", "pos": [1510, 1420], "size": [274.*********, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["godfather.webp", "image"]}, {"id": 6, "type": "SaveVideo", "pos": [2220, 1420], "size": [374, 460], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 7}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 8, "type": "RunwayImageToVideoNodeGen4", "pos": [1800, 1420], "size": [400, 270], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "start_frame", "type": "IMAGE", "link": 6}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [7]}], "properties": {"Node name for S&R": "RunwayImageToVideoNodeGen4"}, "widgets_values": ["A man sits alone in a dimly lit room, smoking a cigarette. The smoke slowly fills the space. He suddenly turns his head to look directly at the camera, making clear eye contact. The camera then slowly pushes forward toward his face, ending in a close-up. The mood is cinematic and introspective. Shallow depth of field, dramatic lighting, soft shadows, 24fps film look.\n\n", 5, "1280:720", 549931386, "randomize", "Result URL: https://dnznrvs05pmza.cloudfront.net/0eff3078-edad-4d60-b2e8-6f7ffdb00265.mp4?_jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXlIYXNoIjoiYjA4ZDY0NTY4YWI5OGY4MyIsImJ1Y2tldCI6InJ1bndheS10YXNrLWFydGlmYWN0cyIsInN0YWdlIjoicHJvZCIsImV4cCI6MTc0ODMwNDAwMH0.cCIgrx159VGochPpY2g06txej6SKLGJ6dCPbIIwvPg0"], "color": "#432", "bgcolor": "#653"}], "links": [[6, 4, 0, 8, 0, "IMAGE"], [7, 8, 0, 6, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1000000000000005, "offset": [-1438.6288484398199, -1353.7748283747776]}, "frontendVersion": "1.21.0"}, "version": 0.4}