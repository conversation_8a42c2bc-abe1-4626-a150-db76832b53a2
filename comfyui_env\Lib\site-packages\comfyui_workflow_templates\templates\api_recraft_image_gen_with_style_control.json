{"id": "ba9df292-1ee9-4b7c-af08-690441990d87", "revision": 0, "last_node_id": 25, "last_link_id": 27, "nodes": [{"id": 1, "type": "SaveImage", "pos": [635.0529174804688, 924.9351196289062], "size": [270, 270], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 12, "type": "RecraftVectorizeImageNode", "pos": [635.50927734375, 1281.8238525390625], "size": [212.42849731445312, 38.70311737060547], "flags": {}, "order": 11, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 25}], "outputs": [{"name": "SVG", "type": "SVG", "links": [17]}], "properties": {"Node name for S&R": "RecraftVectorizeImageNode"}}, {"id": 13, "type": "SaveSVG", "pos": [905.5093994140625, 1281.8238525390625], "size": [270, 58], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "svg", "type": "SVG", "link": 17}], "outputs": [], "properties": {"Node name for S&R": "SaveSVG"}, "widgets_values": ["svg/ComfyUI"]}, {"id": 14, "type": "RecraftStyleV3LogoRaster", "pos": [-125.43206787109375, 746.3561401367188], "size": [270, 58], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "recraft_style", "type": "RECRAFT_V3_STYLE", "links": null}], "properties": {"Node name for S&R": "RecraftStyleV3LogoRaster"}, "widgets_values": ["emblem_graffiti"]}, {"id": 15, "type": "RecraftStyleV3RealisticImage", "pos": [-129.12901306152344, 863.734375], "size": [288.1822204589844, 58], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"name": "recraft_style", "type": "RECRAFT_V3_STYLE", "links": null}], "properties": {"Node name for S&R": "RecraftStyleV3RealisticImage"}, "widgets_values": ["None"]}, {"id": 3, "type": "RecraftTextToImageNode", "pos": [206.0524139404297, 927.2428588867188], "size": [394.75518798828125, 249.34962463378906], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "recraft_style", "shape": 7, "type": "RECRAFT_V3_STYLE", "link": 26}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}, {"name": "recraft_controls", "shape": 7, "type": "RECRAFT_CONTROLS", "link": 27}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 25]}], "properties": {"Node name for S&R": "RecraftTextToImageNode"}, "widgets_values": ["A young boy performing a dynamic skateboard trick in a sunlit skate park. He's wearing a white t-shirt and green shorts, captured mid-air with his blonde hair blowing in the wind. The skateboard is in motion as he rides a blue, curved ramp, creating a sense of energy and movement. The scene is painted in a bold, expressive oil painting style, with thick brushstrokes that enhance the vibrant and lively atmosphere. The background features a bright blue ramp, adding to the feeling of freedom and adventure.", "1536x1024", 1, 1020777976509614, "randomize"]}, {"id": 24, "type": "RecraftStyleV3DigitalIllustration", "pos": [-125.92620086669922, 976.0741577148438], "size": [289.51165771484375, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "recraft_style", "type": "RECRAFT_V3_STYLE", "links": [26]}], "properties": {"Node name for S&R": "RecraftStyleV3DigitalIllustration"}, "widgets_values": ["2d_art_poster"]}, {"id": 20, "type": "<PERSON>downNote", "pos": [533.105224609375, 620.5520629882812], "size": [389.0563049316406, 249.69961547851562], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 18, "type": "RecraftColorRGB", "pos": [-448.*************, 747.*************], "size": [276.************, 106], "flags": {}, "order": 4, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": null}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [18, 24]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [24, 44, 196]}, {"id": 16, "type": "RecraftColorRGB", "pos": [-448.*************, 907.*************], "size": [276.************, 106], "flags": {}, "order": 6, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 18}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [19]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [241, 44, 208]}, {"id": 17, "type": "RecraftColorRGB", "pos": [-448.*************, 1057.8499755859375], "size": [276.************, 106], "flags": {}, "order": 7, "mode": 4, "inputs": [{"name": "recraft_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 19}], "outputs": [{"name": "recraft_color", "type": "RECRAFT_COLOR", "links": [23]}], "properties": {"Node name for S&R": "RecraftColorRGB"}, "widgets_values": [241, 255, 65]}, {"id": 22, "type": "RecraftControls", "pos": [-122.50165557861328, 1120.4423828125], "size": [287.4034118652344, 55.30767059326172], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "colors", "shape": 7, "type": "RECRAFT_COLOR", "link": 23}, {"name": "background_color", "shape": 7, "type": "RECRAFT_COLOR", "link": 24}], "outputs": [{"name": "recraft_controls", "type": "RECRAFT_CONTROLS", "links": [27]}], "properties": {"Node name for S&R": "RecraftControls"}}, {"id": 23, "type": "<PERSON>downNote", "pos": [213.1055450439453, 620.5520629882812], "size": [304.0386962890625, 248.52760314941406], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "About Recraft Text to Image", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/recraft/recarft-text-to-image) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/recraft/recarft-text-to-image) \n\n1 - Different \"Recraft Styles\" can quickly set different output styles.\n\n2 - \"seed\" is only used to determine whether a node should be re - run, but the actual result has nothing to do with the seed.\n\n---\n\n1 - 不同的 “Recraft Style ”  可以快速设置不同的输出风格\n\n2 - “seed” 仅用于确定节点是否应重新运行，但实际结果与种子无关 \n"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 3, 0, 1, 0, "IMAGE"], [17, 12, 0, 13, 0, "SVG"], [18, 18, 0, 16, 0, "RECRAFT_COLOR"], [19, 16, 0, 17, 0, "RECRAFT_COLOR"], [23, 17, 0, 22, 0, "RECRAFT_COLOR"], [24, 18, 0, 22, 1, "RECRAFT_COLOR"], [25, 3, 0, 12, 0, "IMAGE"], [26, 24, 0, 3, 0, "RECRAFT_V3_STYLE"], [27, 22, 0, 3, 2, "RECRAFT_CONTROLS"]], "groups": [{"id": 1, "title": "(Optional) Convert into SVG (need credits)", "bounding": [625.5093383789062, 1208.2237548828125, 568.6611938476562, 239.57406616210938], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Recraft Style", "bounding": [-139.1291046142578, 672.7559814453125, 312.71435546875, 371.3175354003906], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Color", "bounding": [-458.*************, 677.*************, 296.************, 499.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"frontendVersion": "1.18.5"}, "version": 0.4}