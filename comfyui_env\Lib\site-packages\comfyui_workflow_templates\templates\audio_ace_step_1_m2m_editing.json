{"id": "88ac5dad-efd7-40bb-84fe-fbaefdee1fa9", "revision": 0, "last_node_id": 75, "last_link_id": 138, "nodes": [{"id": 49, "type": "LatentApplyOperationCFG", "pos": [940, -160], "size": [290, 50], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 113}, {"name": "operation", "type": "LATENT_OPERATION", "link": 114}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [121]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LatentApplyOperationCFG"}, "widgets_values": []}, {"id": 40, "type": "CheckpointLoaderSimple", "pos": [180, -160], "size": [370, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [115]}, {"name": "CLIP", "type": "CLIP", "links": [80]}, {"name": "VAE", "type": "VAE", "links": [83, 137]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.32", "Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "ace_step_v1_3.5b.safetensors", "url": "https://huggingface.co/Comfy-Org/ACE-Step_ComfyUI_repackaged/resolve/main/all_in_one/ace_step_v1_3.5b.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["ace_step_v1_3.5b.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 48, "type": "<PERSON>downNote", "pos": [-460, -200], "size": [610, 820], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About ACE Step and Multi-language Input", "properties": {}, "widgets_values": ["[Tutorial](http://docs.comfy.org/tutorials/audio/ace-step/ace-step-v1) | [教程](http://docs.comfy.org/zh-CN/tutorials/audio/ace-step/ace-step-v1)\n\n\n### Model Download\n\nDownload the following model and save it to the **ComfyUI/models/checkpoints** folder.\n[ace_step_v1_3.5b.safetensors](https://huggingface.co/Comfy-Org/ACE-Step_ComfyUI_repackaged/blob/main/all_in_one/ace_step_v1_3.5b.safetensors)\n\n\n### Multilingual Support\n\nCurrently, the implementation of multi-language support for ACE-Step V1 is achieved by uniformly converting different languages into English characters. At present, in ComfyUI, we haven't implemented the step of converting multi-languages into English. This is because if we need to implement the corresponding conversion, we have to add additional core dependencies of ComfyUI, which may lead to uncertain dependency conflicts.\n\nSo, currently, if you need to input multi-language text, you have to manually convert it into English characters to complete this process. Then, at the beginning of the corresponding `lyrics`, input the abbreviation of the corresponding language code.\n\nFor example, for Chinese, use `[zh]`, for Japanese use `[ja]`, for Korean use `[ko]`, and so on. For specific language input, please check the examples in the instructions. \n\nFor example, Chinese `[zh]`, Japanese `[ja]`, Korean `[ko]`, etc.\n\nExample:\n\n```\n[verse]\n\n[zh]wo3zou3guo4shen1ye4de5jie1dao4\n[zh]leng3feng1chui1luan4si1nian4de5piao4liang4wai4tao4\n[zh]ni3de5wei1xiao4xiang4xing1guang1hen3xuan4yao4\n[zh]zhao4liang4le5wo3gu1du2de5mei3fen1mei3miao3\n\n[chorus]\n\n[verse]​\n[ko]hamkke si-kkeuleo-un sesang-ui sodong-eul pihae​\n[ko]honja ogsang-eseo dalbich-ui eolyeompus-ileul balaboda​\n[ko]niga salang-eun lideum-i ganghan eum-ag gatdago malhaess-eo​\n[ko]han ta han tamada ma-eum-ui ondoga eolmana heojeonhanji ijge hae\n\n[bridge]\n[es]cantar mi anhelo por ti sin ocultar\n[es]como poesía y pintura, lleno de anhelo indescifrable\n[es]tu sombra es tan terca como el viento, inborrable\n[es]persiguiéndote en vuelo, brilla como cruzar una mar de nubes\n\n[chorus]\n[fr]que tu sois le vent qui souffle sur ma main\n[fr]un contact chaud comme la douce pluie printanière\n[fr]que tu sois le vent qui s'entoure de mon corps\n[fr]un amour profond qui ne s'éloignera jamais\n\n```\n\n---\n\n### 模型下载\n\n下载下面的模型并保存到 **ComfyUI/models/checkpoints** 文件夹下\n[ace_step_v1_3.5b.safetensors](https://huggingface.co/Comfy-Org/ACE-Step_ComfyUI_repackaged/blob/main/all_in_one/ace_step_v1_3.5b.safetensors)\n\n\n### 多语言支持\n\n目前 ACE-Step V1 多语言的实现是通过将不同语言统一转换为英文字符来实现的，目前在 ComfyUI 中我们并没有实现多语言转换为英文的这一步骤。因为如果需要实现对应转换，则需要增加额外的 ComfyUI 核心依赖，这将可能带来不确定的依赖冲突。\n\n所以目前如果你需要输入多语言，则需要手动转换为英文字符来实现这一过程，然后在对应 `lyrics` 开头输入对应语言代码的缩写。\n\n比如中文`[zh]` 日语 `[ja]` 韩语 `[ko]` 等，具体语言输入请查看说明中的示例\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 18, "type": "VAEDecodeAudio", "pos": [1080, 270], "size": [150.93612670898438, 46], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 122}, {"name": "vae", "type": "VAE", "link": 83}], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [126, 127, 128]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.32", "Node name for S&R": "VAEDecodeAudio"}, "widgets_values": []}, {"id": 60, "type": "SaveAudio", "pos": [1260, 40], "size": [610, 112], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "audio", "type": "AUDIO", "link": 127}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAudio"}, "widgets_values": ["audio/ComfyUI"]}, {"id": 61, "type": "SaveAudioOpus", "pos": [1260, 220], "size": [610, 136], "flags": {}, "order": 16, "mode": 4, "inputs": [{"name": "audio", "type": "AUDIO", "link": 128}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAudioOpus"}, "widgets_values": ["audio/ComfyUI", "128k"]}, {"id": 44, "type": "ConditioningZeroOut", "pos": [600, 70], "size": [197.712890625, 26], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 108}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [120]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.32", "Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 51, "type": "ModelSamplingSD3", "pos": [590, -40], "size": [330, 60], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 115}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [113]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [5.000000000000001]}, {"id": 50, "type": "LatentOperationTonemapReinhard", "pos": [590, -160], "size": [330, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT_OPERATION", "type": "LATENT_OPERATION", "links": [114]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LatentOperationTonemapReinhard"}, "widgets_values": [1.0000000000000002]}, {"id": 17, "type": "EmptyAceStepLatentAudio", "pos": [180, 50], "size": [370, 82], "flags": {}, "order": 3, "mode": 4, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": []}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.32", "Node name for S&R": "EmptyAceStepLatentAudio"}, "widgets_values": [120, 1]}, {"id": 68, "type": "VAEEncodeAudio", "pos": [180, 180], "size": [370, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 136}, {"name": "vae", "type": "VAE", "link": 137}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [138]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "VAEEncodeAudio"}, "widgets_values": []}, {"id": 64, "type": "LoadAudio", "pos": [180, 340], "size": [370, 140], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [136]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "LoadAudio"}, "widgets_values": ["audio_ace_step_1_t2a_song-1.mp3", null, null], "color": "#322", "bgcolor": "#533"}, {"id": 52, "type": "K<PERSON><PERSON><PERSON>", "pos": [940, -40], "size": [290, 262], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 121}, {"name": "positive", "type": "CONDITIONING", "link": 117}, {"name": "negative", "type": "CONDITIONING", "link": 120}, {"name": "latent_image", "type": "LATENT", "link": 138}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [122]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [938549746349002, "randomize", 50, 5, "euler", "simple", 0.30000000000000004]}, {"id": 59, "type": "SaveAudioMP3", "pos": [1260, -160], "size": [610, 136], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "audio", "type": "AUDIO", "link": 126}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.34", "Node name for S&R": "SaveAudioMP3"}, "widgets_values": ["audio/ComfyUI", "V0"]}, {"id": 73, "type": "Note", "pos": [1260, 410], "size": [610, 90], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["These nodes can save audio in different formats. Currently, all the modes are Bypass. You can enable them as per your needs.\n\n这些节点可以将 audio  保存成不同格式，目前的模式都是 Bypass ，你可以按你的需要来启用"], "color": "#432", "bgcolor": "#653"}, {"id": 14, "type": "TextEncodeAceStepAudio", "pos": [590, 120], "size": [340, 500], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 80}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [108, 117]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.32", "Node name for S&R": "TextEncodeAceStepAudio"}, "widgets_values": ["anime, cute female vocals, kawaii pop, j-pop, childish, piano, guitar, synthesizer, fast, happy, cheerful, lighthearted", "[verse]\nフワフワ　オミミガ\nユレルヨ　カゼノナカ\nキラキラ　アオイメ\nミツメル　セカイヲ\n\n[verse]\nフワフワ　シッポハ\nオオキク　ユレルヨ\nキンイロ　カミノケ\nナビクヨ　カゼノナカ\n\n[verse]\nコンフィーユーアイノ\nマモリビト\nピンクノ　セーターデ\nエガオヲ　クレルヨ\n\nアオイロ　スカートト\nクロイコート　キンノモヨウ\nヤサシイ　ヒカリガ\nツツムヨ　フェネックガール\n\n[verse]\nフワフワ　オミミデ\nキコエル　ココロノ　コエ\nダイスキ　フェネックガール\nイツデモ　ソバニイルヨ", 0.9900000000000002]}, {"id": 75, "type": "<PERSON>downNote", "pos": [950, 410], "size": [280, 210], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "About Repainting", "properties": {}, "widgets_values": ["Providing the lyrics of the original song or the modified lyrics is very important for the output of repainting or editing. \n\nAdjust the value of the **denoise** parameter in KSampler. The larger the value, the lower the similarity between the output audio and the original audio.\n\n提供原始歌曲的歌词或者修改后的歌词对于音频编辑的输出是非常重要的，调整 KSampler 中的  denoise 参数的数值，数值越大输出的音频与原始音频相似度越低"], "color": "#432", "bgcolor": "#653"}], "links": [[80, 40, 1, 14, 0, "CLIP"], [83, 40, 2, 18, 1, "VAE"], [108, 14, 0, 44, 0, "CONDITIONING"], [113, 51, 0, 49, 0, "MODEL"], [114, 50, 0, 49, 1, "LATENT_OPERATION"], [115, 40, 0, 51, 0, "MODEL"], [117, 14, 0, 52, 1, "CONDITIONING"], [120, 44, 0, 52, 2, "CONDITIONING"], [121, 49, 0, 52, 0, "MODEL"], [122, 52, 0, 18, 0, "LATENT"], [126, 18, 0, 59, 0, "AUDIO"], [127, 18, 0, 60, 0, "AUDIO"], [128, 18, 0, 61, 0, "AUDIO"], [136, 64, 0, 68, 0, "AUDIO"], [137, 40, 2, 68, 1, "VAE"], [138, 68, 0, 52, 3, "LATENT"]], "groups": [{"id": 1, "title": "Load model here", "bounding": [170, -230, 390, 180], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Latent", "bounding": [170, -30, 390, 280], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Adjust the vocal volume", "bounding": [580, -230, 350, 140], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "For repainting", "bounding": [170, 270, 390, 223.60000610351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Output", "bounding": [1250, -230, 630, 760], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6830134553650705, "offset": [785.724285521853, 434.02395631202546]}, "frontendVersion": "1.19.9", "node_versions": {"comfy-core": "0.3.34", "ace-step": "06f751d65491c9077fa2bc9b06d2c6f2a90e4c56"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}