{"version": 3, "file": "uvMirrors-bGzd1syV.js", "sources": ["../../src/constants/uvMirrors.ts"], "sourcesContent": ["export interface UVMirror {\n  /**\n   * The setting id defined for the mirror.\n   */\n  settingId: string\n  /**\n   * The default mirror to use.\n   */\n  mirror: string\n  /**\n   * The fallback mirror to use.\n   */\n  fallbackMirror: string\n  /**\n   * The path suffix to validate the mirror is reachable.\n   */\n  validationPathSuffix?: string\n}\n\nexport const PYTHON_MIRROR: UVMirror = {\n  settingId: 'Comfy-Desktop.UV.PythonInstallMirror',\n  mirror:\n    'https://github.com/astral-sh/python-build-standalone/releases/download',\n  fallbackMirror:\n    'https://python-standalone.org/mirror/astral-sh/python-build-standalone',\n  validationPathSuffix:\n    '/20250115/cpython-3.10.16+20250115-aarch64-apple-darwin-debug-full.tar.zst.sha256'\n}\n\nexport const PYPI_MIRROR: UVMirror = {\n  settingId: 'Comfy-Desktop.UV.PypiInstallMirror',\n  mirror: 'https://pypi.org/simple/',\n  fallbackMirror: 'https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple'\n}\n"], "names": [], "mappings": "AAmBO,MAAM,gBAA0B;AAAA,EACrC,WAAW;AAAA,EACX,QACE;AAAA,EACF,gBACE;AAAA,EACF,sBACE;AACJ;AAEO,MAAM,cAAwB;AAAA,EACnC,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,gBAAgB;AAClB;"}