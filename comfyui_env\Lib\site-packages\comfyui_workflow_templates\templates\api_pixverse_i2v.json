{"id": "facd78cb-d909-4d5c-a4b3-cdae43b953d4", "revision": 0, "last_node_id": 12, "last_link_id": 6, "nodes": [{"id": 6, "type": "LoadImage", "pos": [348.4389953613281, 446.6658020019531], "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["astronaut.png", "image"]}, {"id": 8, "type": "<PERSON>downNote", "pos": [-48.47196578979492, 449.2103576660156], "size": [370.8778991699219, 293.9327697753906], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "PixverseTemplateNode", "pos": [393.*************, 815.************], "size": [270, 58], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "pixverse_template", "type": "PIXVERSE_TEMPLATE", "links": [6]}], "properties": {"Node name for S&R": "PixverseTemplateNode"}, "widgets_values": ["Microwave"]}, {"id": 7, "type": "SaveVideo", "pos": [1165.8995361328125, 446.8341979980469], "size": [315, 413], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 5}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 4, "type": "PixverseImageToVideoNode", "pos": [728.2763671875, 447.9843444824219], "size": [403.11004638671875, 297.7445983886719], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 4}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}, {"name": "pixverse_template", "shape": 7, "type": "PIXVERSE_TEMPLATE", "link": 6}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [5]}], "properties": {"Node name for S&R": "PixverseImageToVideoNode", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"prompt": true, "quality": true, "duration_seconds": true, "motion_mode": true, "seed": true}}, "widgets_values": ["An astronaut was lying on a field full of orange flowers, holding an old-fashioned radio in his hands. The camera slowly rotated around him", "1080p", 5, "normal", 1508029319, "randomize"], "color": "#322", "bgcolor": "#533"}], "links": [[4, 6, 0, 4, 0, "IMAGE"], [5, 4, 0, 7, 0, "VIDEO"], [6, 10, 0, 4, 2, "PIXVERSE_TEMPLATE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9646149645000017, "offset": [432.2021678495464, -189.89936179053993]}, "frontendVersion": "1.18.5", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}