{"id": "ef49adbe-6e63-45c0-9f29-fa4677f627ac", "revision": 0, "last_node_id": 25, "last_link_id": 17, "nodes": [{"id": 5, "type": "<PERSON>downNote", "pos": [-5900, 1090], "size": [280, 150], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "Save location", "properties": {}, "widgets_values": ["Output model will be auto saved to \"ComfyUI/output/\"\n\nOr you can use the export function in the preview 3D node to download it.\n\n---\n\n输出模型将自动保存到 “ComfyUI/output/”。\n\n或者你可以使用 3D 预览节点中的导出功能来下载它。"], "color": "#432", "bgcolor": "#653"}, {"id": 7, "type": "TripoRetargetNode", "pos": [-4120, 1150], "size": [310.453125, 78], "flags": {}, "order": 11, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "RIG_TASK_ID", "link": 7}], "outputs": [{"name": "model_file", "type": "STRING", "links": [15]}, {"name": "retarget task_id", "type": "RETARGET_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoRetargetNode"}, "widgets_values": ["preset:walk"], "color": "#432", "bgcolor": "#653"}, {"id": 3, "type": "TripoTextToModelNode", "pos": [-6240, 1290], "size": [320, 540], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "model_file", "type": "STRING", "links": [13]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": [4, 5, 9, 10]}], "properties": {"Node name for S&R": "TripoTextToModelNode"}, "widgets_values": ["Generate a 3D model of a steampunk-inspired spider drone with brass legs, steam vents, and a camera eye, set in a crouched, ready-to-leap posture.\n", "", "v2.5-20250123", "None", true, true, 42, 42, 42, "detailed", -1, false], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "<PERSON>downNote", "pos": [-6640, 1290], "size": [380, 320], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "TripoConversionNode", "pos": [-4650, 980], "size": [270, 154], "flags": {}, "order": 6, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "MODEL_TASK_ID,RIG_TASK_ID,RETARGET_TASK_ID", "link": 4}], "outputs": [], "properties": {"Node name for S&R": "TripoConversionNode"}, "widgets_values": ["GLTF", false, -1, 4096, "JPEG"], "color": "#432", "bgcolor": "#653"}, {"id": 17, "type": "Preview3D", "pos": [-5900, 1290], "size": [820, 710], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 13}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 7.2460986315374365, "y": 7.2460986315374365, "z": 7.246098631537437}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 22, "type": "Preview3D", "pos": [-5005, 1290], "size": [820, 710], "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 14}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 11.131601866691193, "y": 11.131601866691193, "z": 11.131601866691195}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 23, "type": "Preview3D", "pos": [-4110, 1290], "size": [820, 710], "flags": {}, "order": 14, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 15}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 24, "type": "Preview3D", "pos": [-3215, 1290], "size": [820, 710], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 16}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 10, "y": 10, "z": 10.000000000000002}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 25, "type": "Preview3D", "pos": [-2320, 1290], "size": [820, 710], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 17}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 6, "type": "TripoRigNode", "pos": [-5000, 1180], "size": [222.439453125, 46], "flags": {}, "order": 7, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "MODEL_TASK_ID", "link": 5}], "outputs": [{"name": "model_file", "type": "STRING", "links": [14]}, {"name": "rig task_id", "type": "RIG_TASK_ID", "links": [7]}], "properties": {"Node name for S&R": "TripoRigNode"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "<PERSON>downNote", "pos": [-5000, 970], "size": [290, 160], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "Save location", "properties": {}, "widgets_values": ["After the task generated by the base model is completed, <PERSON><PERSON> provides relevant APIs for subsequent model processing. You can enable them as needed. Just use the right - click and set the **Mode** of the corresponding part to **Always**.\n\n---\n\n在基础模型生成的任务执行完成后， Tripo 提供了后续的模型处理的相关 API 你可以按需启用，只需要使用右键，将对应的部分的 **模式** 设置为 **总是** 即可"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "TripoRefineNode", "pos": [-3220, 1170], "size": [196.41796875, 46], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "model_task_id", "type": "MODEL_TASK_ID", "link": 9}], "outputs": [{"name": "model_file", "type": "STRING", "links": [16]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoRefineNode"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 11, "type": "<PERSON>downNote", "pos": [-3220, 1020], "size": [240, 90], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "About Refine Draft model", "properties": {}, "widgets_values": ["It is only available when using the model of version v1.4.\n\n---\n\n只有在使用模型 v1.4 版本的模型时才可用"], "color": "#322", "bgcolor": "#533"}, {"id": 9, "type": "TripoTextureNode", "pos": [-2320, 1030], "size": [270, 174], "flags": {}, "order": 9, "mode": 4, "inputs": [{"name": "model_task_id", "type": "MODEL_TASK_ID", "link": 10}], "outputs": [{"name": "model_file", "type": "STRING", "links": [17]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoTextureNode"}, "widgets_values": [true, true, 42, "standard", "original_image"], "color": "#432", "bgcolor": "#653"}], "links": [[4, 3, 1, 8, 0, "MODEL_TASK_ID,RIG_TASK_ID,RETARGET_TASK_ID"], [5, 3, 1, 6, 0, "MODEL_TASK_ID"], [7, 6, 1, 7, 0, "RIG_TASK_ID"], [9, 3, 1, 10, 0, "MODEL_TASK_ID"], [10, 3, 1, 9, 0, "MODEL_TASK_ID"], [13, 3, 0, 17, 1, "STRING"], [14, 6, 0, 22, 1, "STRING"], [15, 7, 0, 23, 1, "STRING"], [16, 10, 0, 24, 1, "STRING"], [17, 9, 0, 25, 1, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.23579476910002525, "offset": [6942.689229629733, -51.10583160223882]}, "frontendVersion": "1.21.0"}, "version": 0.4}