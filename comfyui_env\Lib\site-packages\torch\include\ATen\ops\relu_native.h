#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & relu_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor relu(const at::Tensor & self);
TORCH_API at::Tensor & relu_(at::Tensor & self);
TORCH_API at::Tensor NestedTensor_relu(const at::Tensor & self);
TORCH_API at::Tensor & NestedTensor_relu_(at::Tensor & self);
TORCH_API at::Tensor relu_sparse(const at::Tensor & self);
TORCH_API at::Tensor & relu_sparse_(at::Tensor & self);
TORCH_API at::Tensor relu_sparse_csr(const at::Tensor & self);
TORCH_API at::Tensor & relu_sparse_csr_(at::Tensor & self);
TORCH_API at::Tensor mkldnn_relu(const at::Tensor & self);
TORCH_API at::Tensor & mkldnn_relu_(at::Tensor & self);
TORCH_API at::Tensor relu_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor & relu_quantized_cpu_(at::Tensor & self);
TORCH_API at::Tensor relu_quantized_cuda(const at::Tensor & self);
TORCH_API at::Tensor & relu_quantized_cuda_(at::Tensor & self);
} // namespace native
} // namespace at
