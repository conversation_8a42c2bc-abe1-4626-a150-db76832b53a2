# Copyright (c) Alibaba, Inc. and its affiliates.
from typing import TYPE_CHECKING

from modelscope.utils.import_utils import LazyImportModule

if TYPE_CHECKING:
    from .disco_guided_diffusion import DiscoDiffusionPipeline
    from .utils import resize
else:
    _import_structure = {
        'disco_guided_diffusion': ['DiscoDiffusionPipeline'],
        'utils': ['resize'],
    }

    import sys

    sys.modules[__name__] = LazyImportModule(
        __name__,
        globals()['__file__'],
        _import_structure,
        module_spec=__spec__,
        extra_objects={},
    )
