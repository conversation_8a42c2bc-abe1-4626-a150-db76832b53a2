#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/mm_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_mm_out_cpu : public at::meta::structured_mm {
void impl(const at::Tensor & self, const at::Tensor & mat2, const at::Tensor & out);
};
struct TORCH_API structured_mm_out_cuda : public at::meta::structured_mm {
void impl(const at::Tensor & self, const at::Tensor & mat2, const at::Tensor & out);
};
TORCH_API at::Tensor _sparse_mm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & _sparse_mm_out(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor _sparse_csr_mm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & _sparse_csr_mm_out(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
} // namespace native
} // namespace at
