{"id": "c81d55fb-b953-4ba0-b366-2b1d830ca387", "revision": 0, "last_node_id": 21, "last_link_id": 8, "nodes": [{"id": 12, "type": "SaveImage", "pos": [1691.237548828125, 1608.6868896484375], "size": [270, 270], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 7}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 11, "type": "FluxProUltraImageNode", "pos": [1256.12744140625, 1607.2718505859375], "size": [396.0795593261719, 305.0788269042969], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image_prompt", "shape": 7, "type": "IMAGE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [7]}], "properties": {"Node name for S&R": "FluxProUltraImageNode"}, "widgets_values": ["A dreamy, surreal half-body portrait of a young woman meditating. She has a short, straight bob haircut dyed in pastel pink, with soft bangs covering her forehead. Her eyes are gently closed, and her hands are raised in a calm, open-palmed meditative pose, fingers slightly curved, as if levitating or in deep concentration. She wears a colorful dress made of patchwork-like pastel tiles, featuring clouds, stars, and rainbows. Around her float translucent, iridescent soap bubbles reflecting the rainbow hues. The background is a fantastical sky filled with cotton-candy clouds and vivid rainbow waves, giving the entire scene a magical, dreamlike atmosphere. Emphasis on youthful serenity, whimsical ambiance, and vibrant soft lighting.", false, 589991183902375, "randomize", "1:1", false, 0.4000000000000001]}, {"id": 14, "type": "LoadImage", "pos": [885.8912353515625, 1605.019287109375], "size": [342.5999755859375, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 20, "type": "<PERSON>downNote", "pos": [891.7418823242188, 1969.7508544921875], "size": [315.394287109375, 149.74844360351562], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "How to enable image_prompt", "properties": {}, "widgets_values": ["Right-click on the Load image node, set the mode to **always** to enable **image_prompt**\n\n---\n在 Load image 节点上右键，设置模式为 **always** 可启用 **image_prompt**"], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "<PERSON>downNote", "pos": [551.428955078125, 1605.022216796875], "size": [317.1644592285156, 427.8039245605469], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "Flux 1.1 [pro] Ultra Image", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/black-forest-labs/flux-1-1-pro-ultra-image) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/black-forest-labs/flux-1-1-pro-ultra-image) \n\n### `image_prompt` is optional.\nWhen `image_prompt` is input, the larger the value of `image_prompt_strength`, the closer the output result is to the input image.\n\n### `raw` determines whether it is Ultra mode or raw format.\n- **Ultra mode**: Specifically designed for high-resolution requirements, it can accurately restore the prompt and maintain the generation speed.\n- **Raw mode**: Focuses on natural realism, optimizes details such as human skin color, light and shadow, and natural landscapes, making the results more realistic and less AI - like.\n---\n### `image_prompt` 是可选的\n- 当输入`image_prompt`  时  `image_prompt_strength` 值越大，则输出结果与输入图越接近\n\n### `raw` 决定是 Ultra 模式还是 raw 格式\n- **Ultra 模式**：专为高分辨率需求设计，能精准还原提示词并保持生成速度。\n- **Raw 模式**：侧重自然真实感，优化人像肤色、光影及自然景观的细节，让结果更真实，少一些 AI 感。\n"], "color": "#432", "bgcolor": "#653"}, {"id": 21, "type": "<PERSON>downNote", "pos": [549.4401245117188, 2078.156494140625], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[7, 11, 0, 12, 0, "IMAGE"], [8, 14, 0, 11, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.****************, "offset": [-313.73920055428545, -1397.3474664410144]}, "frontendVersion": "1.18.5"}, "version": 0.4}