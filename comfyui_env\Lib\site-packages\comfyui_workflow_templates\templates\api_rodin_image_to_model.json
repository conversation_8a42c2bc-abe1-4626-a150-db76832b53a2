{"id": "cb4a348c-918b-42f8-b0a9-ff75063a3171", "revision": 0, "last_node_id": 61, "last_link_id": 49, "nodes": [{"id": 37, "type": "ImageBatch", "pos": [-1847.4912109375, 1148.910400390625], "size": [140, 46], "flags": {}, "order": 11, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 30}, {"name": "image2", "type": "IMAGE", "link": 31}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [22]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 39, "type": "ImageBatch", "pos": [-1847.222412109375, 1245.193359375], "size": [140, 46], "flags": {}, "order": 16, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 21}, {"name": "image2", "type": "IMAGE", "link": 22}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [33]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 38, "type": "ImageBatch", "pos": [-1850.745849609375, 1054.370849609375], "size": [140, 46], "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 28}, {"name": "image2", "type": "IMAGE", "link": 29}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [21]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 47, "type": "ImageBatch", "pos": [-1689.3446044921875, 1058.7923583984375], "size": [140, 46], "flags": {}, "order": 21, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 32}, {"name": "image2", "type": "IMAGE", "link": 33}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 41, "type": "LoadImage", "pos": [-2443.35400390625, 1044.9283447265625], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 40, "type": "LoadImage", "pos": [-2143.35400390625, 1044.9283447265625], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 43, "type": "LoadImage", "pos": [-2153.35400390625, 1414.927978515625], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 44, "type": "LoadImage", "pos": [-1856.6424560546875, 1408.01025390625], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 3, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [32]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 42, "type": "LoadImage", "pos": [-2443.35400390625, 1414.927978515625], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [31]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 46, "type": "<PERSON>downNote", "pos": [-2454.85205078125, 628.801513671875], "size": [380.9487609863281, 309.4496765136719], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 48, "type": "<PERSON>downNote", "pos": [-1459.*************, 1793.************], "size": [409.*************, 141.*************], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "File location", "properties": {}, "widgets_values": ["Output model will be auto saved to \"ComfyUI/output/Rodin3D\"\n\nOr you can use the export function in the preview 3D node to download it.\n\n---\n\n输出模型将自动保存到 “ComfyUI/output/Rodin3D”。\n\n或者你可以使用 3D 预览节点中的导出功能来下载它。"], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "Note", "pos": [-2061.813720703125, 630.0270385742188], "size": [236.6021270751953, 309.6337890625], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Tips", "properties": {}, "widgets_values": ["Try to provide the following types of images:\n- Provide images from multiple angles.\n- Images with a solid color background.\n\nThis helps to generate a model that better meets the requirements. Of course, thanks to <PERSON><PERSON>'s excellent product capabilities, the subject in the unprocessed image can also achieve a good model output.  \n\n---\n\n尽量提供下面的图片类型：\n- 提供多角度\n- 纯色背景的图片\n\n这有助于生成更符合要求的模型，当然依托于 Rodin 的优秀的产品能力，未经处理的图片中的主体也获得不错的模型输出"], "color": "#432", "bgcolor": "#653"}, {"id": 55, "type": "Note", "pos": [-1077.4710693359375, 790.9989013671875], "size": [294.3723449707031, 104.65211486816406], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "Tips", "properties": {}, "widgets_values": ["The larger the Polygon_count, the more detailed the model.\n\nPolygon_count 越大模型越精细"], "color": "#432", "bgcolor": "#653"}, {"id": 49, "type": "LoadImage", "pos": [-1803.8856201171875, 627.3712158203125], "size": [266.3306884765625, 326], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [39, 40, 41, 42]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 28, "type": "Rodin3D_Regular", "pos": [-1450, 790], "size": [344.2574157714844, 106], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "Images", "type": "IMAGE", "link": 39}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [46]}], "properties": {"Node name for S&R": "Rodin3D_Regular"}, "widgets_values": [0, "PBR", "200K-Triangle"], "color": "#432", "bgcolor": "#653"}, {"id": 56, "type": "Preview3D", "pos": [-1450, 990], "size": [880, 750], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 46}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 9.933540543639973, "y": 9.933540543639975, "z": 9.933540543639975}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 31, "type": "Rodin3D_Sketch", "pos": [-515.5814819335938, 852.9099731445312], "size": [354.0360107421875, 58], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 40}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [47]}], "properties": {"Node name for S&R": "Rodin3D_Sketch"}, "widgets_values": [0], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "Rodin3D_Detail", "pos": [414.8179626464844, 820.849365234375], "size": [350.0273742675781, 106], "flags": {}, "order": 14, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 41}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [48]}], "properties": {"Node name for S&R": "Rodin3D_Detail"}, "widgets_values": [0, "PBR", "18K-Quad"], "color": "#432", "bgcolor": "#653"}, {"id": 30, "type": "Rodin3D_Smooth", "pos": [1329.1629638671875, 817.242431640625], "size": [343.4781188964844, 106], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 42}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [49]}], "properties": {"Node name for S&R": "Rodin3D_Smooth"}, "widgets_values": [0, "PBR", "18K-Quad"], "color": "#432", "bgcolor": "#653"}, {"id": 61, "type": "Preview3D", "pos": [1340, 990], "size": [880, 750], "flags": {}, "order": 20, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 49}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 9.933540543639973, "y": 9.933540543639975, "z": 9.933540543639975}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 59, "type": "Preview3D", "pos": [410, 990], "size": [880, 750], "flags": {}, "order": 19, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 48}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 9.933540543639973, "y": 9.933540543639975, "z": 9.933540543639975}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 60, "type": "Preview3D", "pos": [-510, 990], "size": [880, 750], "flags": {}, "order": 18, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 47}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 6.912099131936989, "y": 6.912099131936989, "z": 6.91209913193699}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}], "links": [[21, 38, 0, 39, 0, "IMAGE"], [22, 37, 0, 39, 1, "IMAGE"], [28, 40, 0, 38, 0, "IMAGE"], [29, 41, 0, 38, 1, "IMAGE"], [30, 43, 0, 37, 0, "IMAGE"], [31, 42, 0, 37, 1, "IMAGE"], [32, 44, 0, 47, 0, "IMAGE"], [33, 39, 0, 47, 1, "IMAGE"], [39, 49, 0, 28, 0, "IMAGE"], [40, 49, 0, 31, 0, "IMAGE"], [41, 49, 0, 29, 0, "IMAGE"], [42, 49, 0, 30, 0, "IMAGE"], [46, 28, 0, 56, 1, "STRING"], [47, 31, 0, 60, 1, "STRING"], [48, 29, 0, 59, 1, "STRING"], [49, 30, 0, 61, 1, "STRING"]], "groups": [{"id": 1, "title": "Example - Maximum input images: 5", "bounding": [-2460, 960, 924.6036987304688, 783.1796875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.2853116706110134, "offset": [2276.9877817752463, 296.2223275283754]}, "frontendVersion": "1.21.0"}, "version": 0.4}