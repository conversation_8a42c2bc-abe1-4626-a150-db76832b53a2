# LICENSE HEADER MANAGED BY add-license-header
#
# Copyright 2018 Kornia Team
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

from __future__ import annotations

from typing import List

# All colormaps has 64 base colors. Extracted from matplotlib.
# https://matplotlib.org/stable/users/explain/colors/colormaps.html


RGBColor = List[float]


def get_autumn_base() -> list[RGBColor]:
    return [
        [1.0, 0.0, 0.0],
        [1.0, 0.015873015873015872, 0.0],
        [1.0, 0.031746031746031744, 0.0],
        [1.0, 0.047619047619047616, 0.0],
        [1.0, 0.06349206349206349, 0.0],
        [1.0, 0.07936507936507936, 0.0],
        [1.0, 0.09523809523809523, 0.0],
        [1.0, 0.1111111111111111, 0.0],
        [1.0, 0.12698412698412698, 0.0],
        [1.0, 0.14285714285714285, 0.0],
        [1.0, 0.15873015873015872, 0.0],
        [1.0, 0.1746031746031746, 0.0],
        [1.0, 0.19047619047619047, 0.0],
        [1.0, 0.20634920634920634, 0.0],
        [1.0, 0.2222222222222222, 0.0],
        [1.0, 0.23809523809523808, 0.0],
        [1.0, 0.25396825396825395, 0.0],
        [1.0, 0.2698412698412698, 0.0],
        [1.0, 0.2857142857142857, 0.0],
        [1.0, 0.30158730158730157, 0.0],
        [1.0, 0.31746031746031744, 0.0],
        [1.0, 0.3333333333333333, 0.0],
        [1.0, 0.3492063492063492, 0.0],
        [1.0, 0.36507936507936506, 0.0],
        [1.0, 0.38095238095238093, 0.0],
        [1.0, 0.3968253968253968, 0.0],
        [1.0, 0.4126984126984127, 0.0],
        [1.0, 0.42857142857142855, 0.0],
        [1.0, 0.4444444444444444, 0.0],
        [1.0, 0.4603174603174603, 0.0],
        [1.0, 0.47619047619047616, 0.0],
        [1.0, 0.49206349206349204, 0.0],
        [1.0, 0.5079365079365079, 0.0],
        [1.0, 0.5238095238095237, 0.0],
        [1.0, 0.5396825396825397, 0.0],
        [1.0, 0.5555555555555556, 0.0],
        [1.0, 0.5714285714285714, 0.0],
        [1.0, 0.5873015873015872, 0.0],
        [1.0, 0.6031746031746031, 0.0],
        [1.0, 0.6190476190476191, 0.0],
        [1.0, 0.6349206349206349, 0.0],
        [1.0, 0.6507936507936507, 0.0],
        [1.0, 0.6666666666666666, 0.0],
        [1.0, 0.6825396825396826, 0.0],
        [1.0, 0.6984126984126984, 0.0],
        [1.0, 0.7142857142857142, 0.0],
        [1.0, 0.7301587301587301, 0.0],
        [1.0, 0.746031746031746, 0.0],
        [1.0, 0.7619047619047619, 0.0],
        [1.0, 0.7777777777777777, 0.0],
        [1.0, 0.7936507936507936, 0.0],
        [1.0, 0.8095238095238095, 0.0],
        [1.0, 0.8253968253968254, 0.0],
        [1.0, 0.8412698412698412, 0.0],
        [1.0, 0.8571428571428571, 0.0],
        [1.0, 0.873015873015873, 0.0],
        [1.0, 0.8888888888888888, 0.0],
        [1.0, 0.9047619047619047, 0.0],
        [1.0, 0.9206349206349206, 0.0],
        [1.0, 0.9365079365079365, 0.0],
        [1.0, 0.9523809523809523, 0.0],
        [1.0, 0.9682539682539681, 0.0],
        [1.0, 0.9841269841269841, 0.0],
        [1.0, 1.0, 0.0],
    ]


def get_bone_base() -> list[RGBColor]:
    return [
        [0.0, 0.0, 0.0],
        [0.013888888888888888, 0.013888883454100847, 0.019323671497584544],
        [0.027777777777777776, 0.027777766908201693, 0.03864734299516909],
        [0.041666666666666664, 0.04166665036230254, 0.05797101449275363],
        [0.05555555555555555, 0.05555553381640339, 0.07729468599033817],
        [0.06944444444444445, 0.06944441727050424, 0.09661835748792272],
        [0.08333333333333333, 0.08333330072460508, 0.11594202898550726],
        [0.09722222222222221, 0.09722218417870591, 0.13526570048309178],
        [0.1111111111111111, 0.11111106763280677, 0.15458937198067635],
        [0.125, 0.12499995108690763, 0.1739130434782609],
        [0.1388888888888889, 0.13888883454100848, 0.19323671497584544],
        [0.15277777777777776, 0.1527777179951093, 0.21256038647342998],
        [0.16666666666666666, 0.16666660144921017, 0.23188405797101452],
        [0.18055555555555555, 0.18055548490331103, 0.2512077294685991],
        [0.19444444444444442, 0.19444436835741183, 0.27053140096618356],
        [0.2083333333333333, 0.2083332518115127, 0.2898550724637681],
        [0.2222222222222222, 0.22222213526561355, 0.3091787439613527],
        [0.23611111111111108, 0.2361110187197144, 0.3285024154589372],
        [0.25, 0.24999990217381526, 0.3478260869565218],
        [0.26388888888888884, 0.2638887856279161, 0.36714975845410636],
        [0.2777777777777778, 0.27777766908201695, 0.38647342995169087],
        [0.29166666666666663, 0.2916665525361178, 0.4057971014492754],
        [0.3055555555555555, 0.3055554359902186, 0.42512077294685996],
        [0.3194444444444444, 0.31944443923603627, 0.44444431944451634],
        [0.3333333333333333, 0.3385416582030555, 0.45833321145840344],
        [0.34722222222222215, 0.35763887717007464, 0.47222210347229054],
        [0.3611111111111111, 0.3767360961370938, 0.48611099548617764],
        [0.375, 0.395833315104113, 0.49999988750006474],
        [0.38888888888888884, 0.4149305340711322, 0.5138887795139518],
        [0.40277777777777773, 0.43402775303815133, 0.5277776715278389],
        [0.4166666666666666, 0.4531249720051705, 0.5416665635417259],
        [0.4305555555555555, 0.47222219097218965, 0.555555455555613],
        [0.4444444444444444, 0.49131940993920886, 0.5694443475695001],
        [0.4583333333333332, 0.5104166289062279, 0.5833332395833872],
        [0.47222222222222215, 0.5295138478732472, 0.5972221315972743],
        [0.48611111111111105, 0.5486110668402664, 0.6111110236111614],
        [0.5, 0.5677082858072856, 0.6249999156250485],
        [0.5138888888888887, 0.5868055047743046, 0.6388888076389355],
        [0.5277777777777777, 0.6059027237413239, 0.6527776996528227],
        [0.5416666666666666, 0.624999942708343, 0.6666665916667098],
        [0.5555555555555556, 0.6440971616753622, 0.6805554836805969],
        [0.5694444444444443, 0.6631943806423812, 0.6944443756944839],
        [0.5833333333333333, 0.6822915996094006, 0.7083332677083711],
        [0.5972222222222221, 0.7013888185764198, 0.7222221597222582],
        [0.611111111111111, 0.720486037543439, 0.7361110517361452],
        [0.6249999999999998, 0.739583256510458, 0.7499999437500322],
        [0.6388888888888888, 0.7586804754774773, 0.7638888357639194],
        [0.6527777777777777, 0.7777776944444965, 0.7777777277778065],
        [0.6744790494790494, 0.7916666666666666, 0.7916666197916936],
        [0.6961804461804459, 0.8055555555555554, 0.8055555118055806],
        [0.7178818428818429, 0.8194444444444444, 0.8194444038194678],
        [0.7395832395832396, 0.8333333333333333, 0.8333332958333549],
        [0.7612846362846363, 0.8472222222222222, 0.8472221878472419],
        [0.7829860329860328, 0.8611111111111109, 0.861111079861129],
        [0.8046874296874297, 0.875, 0.8749999718750161],
        [0.8263888263888264, 0.8888888888888888, 0.8888888638889032],
        [0.8480902230902231, 0.9027777777777778, 0.9027777559027903],
        [0.8697916197916196, 0.9166666666666665, 0.9166666479166774],
        [0.8914930164930165, 0.9305555555555556, 0.9305555399305645],
        [0.9131944131944132, 0.9444444444444444, 0.9444444319444516],
        [0.9348958098958099, 0.9583333333333334, 0.9583333239583387],
        [0.9565972065972064, 0.9722222222222221, 0.9722222159722258],
        [0.9782986032986033, 0.9861111111111112, 0.9861111079861129],
        [1.0, 1.0, 1.0],
    ]


def get_jet_base() -> list[RGBColor]:
    return [
        [0.0, 0.0, 0.5],
        [0.0, 0.0, 0.5721500721500722],
        [0.0, 0.0, 0.6443001443001444],
        [0.0, 0.0, 0.7164502164502164],
        [0.0, 0.0, 0.7886002886002886],
        [0.0, 0.0, 0.8607503607503608],
        [0.0, 0.0, 0.9329004329004329],
        [0.0, 0.0, 1.0],
        [0.0, 0.007936507936507936, 1.0],
        [0.0, 0.07142857142857142, 1.0],
        [0.0, 0.1349206349206349, 1.0],
        [0.0, 0.1984126984126984, 1.0],
        [0.0, 0.2619047619047619, 1.0],
        [0.0, 0.3253968253968254, 1.0],
        [0.0, 0.3888888888888889, 1.0],
        [0.0, 0.4523809523809524, 1.0],
        [0.0, 0.5158730158730159, 1.0],
        [0.0, 0.5793650793650794, 1.0],
        [0.0, 0.6428571428571429, 1.0],
        [0.0, 0.7063492063492064, 1.0],
        [0.0, 0.7698412698412699, 1.0],
        [0.0, 0.8333333333333334, 1.0],
        [0.0, 0.8968253968253969, 0.9703020993343575],
        [0.048643113159242315, 0.9603174603174603, 0.9190988223246289],
        [0.09984639016897091, 1.0, 0.8678955453149002],
        [0.15104966717869953, 1.0, 0.8166922683051716],
        [0.20225294418842812, 1.0, 0.765488991295443],
        [0.2534562211981567, 1.0, 0.7142857142857144],
        [0.30465949820788535, 1.0, 0.6630824372759858],
        [0.35586277521761395, 1.0, 0.6118791602662572],
        [0.40706605222734255, 1.0, 0.5606758832565285],
        [0.45826932923707114, 1.0, 0.5094726062467999],
        [0.5094726062467997, 1.0, 0.4582693292370713],
        [0.560675883256528, 1.0, 0.40706605222734304],
        [0.6118791602662569, 1.0, 0.355862775217614],
        [0.6630824372759855, 1.0, 0.3046594982078854],
        [0.7142857142857141, 1.0, 0.2534562211981568],
        [0.7654889912954423, 1.0, 0.20225294418842854],
        [0.8166922683051714, 1.0, 0.1510496671786996],
        [0.8678955453149, 1.0, 0.0998463901689709],
        [0.9190988223246286, 1.0, 0.0486431131592423],
        [0.9703020993343567, 0.9600235155790716, 0.0],
        [1.0, 0.9012345679012346, 0.0],
        [1.0, 0.8424456202233981, 0.0],
        [1.0, 0.7836566725455615, 0.0],
        [1.0, 0.7248677248677253, 0.0],
        [1.0, 0.6660787771898884, 0.0],
        [1.0, 0.6072898295120519, 0.0],
        [1.0, 0.5485008818342153, 0.0],
        [1.0, 0.48971193415637915, 0.0],
        [1.0, 0.4309229864785422, 0.0],
        [1.0, 0.3721340388007057, 0.0],
        [1.0, 0.3133450911228691, 0.0],
        [1.0, 0.25455614344503297, 0.0],
        [1.0, 0.19576719576719603, 0.0],
        [1.0, 0.13697824808935943, 0.0],
        [1.0, 0.07818930041152294, 0.0],
        [0.9329004329004335, 0.019400352733686788, 0.0],
        [0.8607503607503608, 0.0, 0.0],
        [0.7886002886002886, 0.0, 0.0],
        [0.7164502164502164, 0.0, 0.0],
        [0.6443001443001448, 0.0, 0.0],
        [0.5721500721500721, 0.0, 0.0],
        [0.5, 0.0, 0.0],
    ]


def get_winter_base() -> list[RGBColor]:
    return [
        [0.0, 0.0, 1.0],
        [0.0, 0.015873015873015872, 0.9920634920634921],
        [0.0, 0.031746031746031744, 0.9841269841269842],
        [0.0, 0.047619047619047616, 0.9761904761904762],
        [0.0, 0.06349206349206349, 0.9682539682539683],
        [0.0, 0.07936507936507936, 0.9603174603174603],
        [0.0, 0.09523809523809523, 0.9523809523809523],
        [0.0, 0.1111111111111111, 0.9444444444444444],
        [0.0, 0.12698412698412698, 0.9365079365079365],
        [0.0, 0.14285714285714285, 0.9285714285714286],
        [0.0, 0.15873015873015872, 0.9206349206349207],
        [0.0, 0.1746031746031746, 0.9126984126984127],
        [0.0, 0.19047619047619047, 0.9047619047619048],
        [0.0, 0.20634920634920634, 0.8968253968253969],
        [0.0, 0.2222222222222222, 0.8888888888888888],
        [0.0, 0.23809523809523808, 0.8809523809523809],
        [0.0, 0.25396825396825395, 0.873015873015873],
        [0.0, 0.2698412698412698, 0.8650793650793651],
        [0.0, 0.2857142857142857, 0.8571428571428572],
        [0.0, 0.30158730158730157, 0.8492063492063492],
        [0.0, 0.31746031746031744, 0.8412698412698413],
        [0.0, 0.3333333333333333, 0.8333333333333334],
        [0.0, 0.3492063492063492, 0.8253968253968254],
        [0.0, 0.36507936507936506, 0.8174603174603174],
        [0.0, 0.38095238095238093, 0.8095238095238095],
        [0.0, 0.3968253968253968, 0.8015873015873016],
        [0.0, 0.4126984126984127, 0.7936507936507937],
        [0.0, 0.42857142857142855, 0.7857142857142857],
        [0.0, 0.4444444444444444, 0.7777777777777778],
        [0.0, 0.4603174603174603, 0.7698412698412699],
        [0.0, 0.47619047619047616, 0.7619047619047619],
        [0.0, 0.49206349206349204, 0.753968253968254],
        [0.0, 0.5079365079365079, 0.746031746031746],
        [0.0, 0.5238095238095237, 0.7380952380952381],
        [0.0, 0.5396825396825397, 0.7301587301587302],
        [0.0, 0.5555555555555556, 0.7222222222222222],
        [0.0, 0.5714285714285714, 0.7142857142857143],
        [0.0, 0.5873015873015872, 0.7063492063492064],
        [0.0, 0.6031746031746031, 0.6984126984126984],
        [0.0, 0.6190476190476191, 0.6904761904761905],
        [0.0, 0.6349206349206349, 0.6825396825396826],
        [0.0, 0.6507936507936507, 0.6746031746031746],
        [0.0, 0.6666666666666666, 0.6666666666666667],
        [0.0, 0.6825396825396826, 0.6587301587301587],
        [0.0, 0.6984126984126984, 0.6507936507936508],
        [0.0, 0.7142857142857142, 0.6428571428571429],
        [0.0, 0.7301587301587301, 0.6349206349206349],
        [0.0, 0.746031746031746, 0.626984126984127],
        [0.0, 0.7619047619047619, 0.6190476190476191],
        [0.0, 0.7777777777777777, 0.6111111111111112],
        [0.0, 0.7936507936507936, 0.6031746031746033],
        [0.0, 0.8095238095238095, 0.5952380952380952],
        [0.0, 0.8253968253968254, 0.5873015873015873],
        [0.0, 0.8412698412698412, 0.5793650793650794],
        [0.0, 0.8571428571428571, 0.5714285714285714],
        [0.0, 0.873015873015873, 0.5634920634920635],
        [0.0, 0.8888888888888888, 0.5555555555555556],
        [0.0, 0.9047619047619047, 0.5476190476190477],
        [0.0, 0.9206349206349206, 0.5396825396825398],
        [0.0, 0.9365079365079365, 0.5317460317460317],
        [0.0, 0.9523809523809523, 0.5238095238095238],
        [0.0, 0.9682539682539681, 0.5158730158730159],
        [0.0, 0.9841269841269841, 0.5079365079365079],
        [0.0, 1.0, 0.5],
    ]


def get_rainbow_base() -> list[RGBColor]:
    return [
        [0.5, 0.0, 1.0],
        [0.46825396825396826, 0.049845885660697156, 0.9996891820008162],
        [0.4365079365079365, 0.09956784659581665, 0.9987569212189223],
        [0.40476190476190477, 0.14904226617617441, 0.9972037971811801],
        [0.373015873015873, 0.19814614319939755, 0.9950307753654014],
        [0.3412698412698413, 0.24675739769029362, 0.9922392066001721],
        [0.30952380952380953, 0.29475517441090415, 0.9888308262251285],
        [0.2777777777777778, 0.3420201433256687, 0.984807753012208],
        [0.24603174603174605, 0.3884347962746947, 0.9801724878485438],
        [0.2142857142857143, 0.4338837391175581, 0.9749279121818236],
        [0.18253968253968256, 0.4782539786213182, 0.969077286229078],
        [0.1507936507936508, 0.521435203379498, 0.962624246950012],
        [0.11904761904761907, 0.563320058063622, 0.9555728057861408],
        [0.08730158730158732, 0.6038044103254774, 0.9479273461671318],
        [0.05555555555555558, 0.6427876096865393, 0.9396926207859084],
        [0.023809523809523836, 0.6801727377709194, 0.9308737486442042],
        [0.007936507936507908, 0.7158668492597183, 0.9214762118704076],
        [0.03968253968253965, 0.7497812029677341, 0.9115058523116731],
        [0.0714285714285714, 0.7818314824680298, 0.9009688679024191],
        [0.10317460317460314, 0.8119380057158565, 0.8898718088114687],
        [0.13492063492063489, 0.8400259231507714, 0.8782215733702285],
        [0.16666666666666663, 0.8660254037844386, 0.8660254037844387],
        [0.19841269841269837, 0.8898718088114685, 0.8532908816321557],
        [0.23015873015873012, 0.9115058523116731, 0.8400259231507715],
        [0.26190476190476186, 0.9308737486442041, 0.8262387743159949],
        [0.2936507936507936, 0.9479273461671317, 0.8119380057158566],
        [0.32539682539682535, 0.962624246950012, 0.7971325072229225],
        [0.3571428571428571, 0.9749279121818236, 0.7818314824680298],
        [0.38888888888888884, 0.984807753012208, 0.766044443118978],
        [0.4206349206349206, 0.9922392066001721, 0.7497812029677342],
        [0.45238095238095233, 0.9972037971811801, 0.7330518718298263],
        [0.4841269841269841, 0.9996891820008162, 0.7158668492597184],
        [0.5158730158730158, 0.9996891820008162, 0.6982368180860729],
        [0.5476190476190474, 0.9972037971811801, 0.6801727377709196],
        [0.5793650793650793, 0.9922392066001721, 0.6616858375968595],
        [0.6111111111111112, 0.984807753012208, 0.6427876096865394],
        [0.6428571428571428, 0.9749279121818236, 0.6234898018587336],
        [0.6746031746031744, 0.9626242469500121, 0.6038044103254775],
        [0.7063492063492063, 0.9479273461671318, 0.58374367223479],
        [0.7380952380952381, 0.9308737486442042, 0.563320058063622],
        [0.7698412698412698, 0.9115058523116732, 0.5425462638657594],
        [0.8015873015873014, 0.8898718088114688, 0.5214352033794982],
        [0.8333333333333333, 0.8660254037844387, 0.5000000000000001],
        [0.8650793650793651, 0.8400259231507715, 0.47825397862131824],
        [0.8968253968253967, 0.8119380057158566, 0.4562106573531631],
        [0.9285714285714284, 0.7818314824680301, 0.43388373911755834],
        [0.9603174603174602, 0.7497812029677344, 0.4112871031306117],
        [0.9920634920634921, 0.7158668492597186, 0.3884347962746948],
        [1.0, 0.6801727377709197, 0.3653410243663952],
        [1.0, 0.6427876096865395, 0.3420201433256688],
        [1.0, 0.6038044103254777, 0.31848665025168466],
        [1.0, 0.5633200580636223, 0.2947551744109043],
        [1.0, 0.5214352033794981, 0.27084046814300516],
        [1.0, 0.47825397862131847, 0.24675739769029378],
        [1.0, 0.43388373911755823, 0.22252093395631445],
        [1.0, 0.3884347962746946, 0.19814614319939755],
        [1.0, 0.3420201433256689, 0.17364817766693041],
        [1.0, 0.2947551744109046, 0.14904226617617464],
        [1.0, 0.24675739769029384, 0.12434370464748527],
        [1.0, 0.1981461431993976, 0.09956784659581666],
        [1.0, 0.14904226617617472, 0.07473009358642439],
        [1.0, 0.09956784659581717, 0.04984588566069742],
        [1.0, 0.04984588566069748, 0.024930691738073035],
        [1.0, 1.2246467991473532e-16, 6.123233995736766e-17],
    ]


def get_ocean_base() -> list[RGBColor]:
    return [
        [0.0, 0.5, 0.0],
        [0.0, 0.47619047619047616, 0.015873015873015872],
        [0.0, 0.4523809523809524, 0.031746031746031744],
        [0.0, 0.4285714285714286, 0.047619047619047616],
        [0.0, 0.40476190476190477, 0.06349206349206349],
        [0.0, 0.38095238095238093, 0.07936507936507936],
        [0.0, 0.35714285714285715, 0.09523809523809523],
        [0.0, 0.33333333333333337, 0.1111111111111111],
        [0.0, 0.30952380952380953, 0.12698412698412698],
        [0.0, 0.2857142857142857, 0.14285714285714285],
        [0.0, 0.2619047619047619, 0.15873015873015872],
        [0.0, 0.23809523809523814, 0.1746031746031746],
        [0.0, 0.2142857142857143, 0.19047619047619047],
        [0.0, 0.19047619047619047, 0.20634920634920634],
        [0.0, 0.16666666666666669, 0.2222222222222222],
        [0.0, 0.1428571428571429, 0.23809523809523808],
        [0.0, 0.11904761904761907, 0.25396825396825395],
        [0.0, 0.09523809523809523, 0.2698412698412698],
        [0.0, 0.07142857142857145, 0.2857142857142857],
        [0.0, 0.04761904761904767, 0.30158730158730157],
        [0.0, 0.023809523809523836, 0.31746031746031744],
        [0.0, 0.0, 0.3333333333333333],
        [0.0, 0.023809523809523725, 0.3492063492063492],
        [0.0, 0.04761904761904756, 0.36507936507936506],
        [0.0, 0.0714285714285714, 0.38095238095238093],
        [0.0, 0.09523809523809523, 0.3968253968253968],
        [0.0, 0.11904761904761907, 0.4126984126984127],
        [0.0, 0.1428571428571428, 0.42857142857142855],
        [0.0, 0.16666666666666663, 0.4444444444444444],
        [0.0, 0.19047619047619047, 0.4603174603174603],
        [0.0, 0.2142857142857142, 0.47619047619047616],
        [0.0, 0.23809523809523803, 0.49206349206349204],
        [0.0, 0.26190476190476186, 0.5079365079365079],
        [0.0, 0.2857142857142856, 0.5238095238095237],
        [0.0, 0.30952380952380953, 0.5396825396825397],
        [0.0, 0.33333333333333337, 0.5555555555555556],
        [0.0, 0.3571428571428571, 0.5714285714285714],
        [0.0, 0.3809523809523808, 0.5873015873015872],
        [0.0, 0.40476190476190466, 0.6031746031746031],
        [0.0, 0.4285714285714286, 0.6190476190476191],
        [0.0, 0.45238095238095233, 0.6349206349206349],
        [0.0, 0.47619047619047605, 0.6507936507936507],
        [0.0, 0.5, 0.6666666666666666],
        [0.04761904761904745, 0.5238095238095237, 0.6825396825396826],
        [0.0952380952380949, 0.5476190476190474, 0.6984126984126984],
        [0.14285714285714235, 0.5714285714285712, 0.7142857142857142],
        [0.19047619047619024, 0.5952380952380951, 0.7301587301587301],
        [0.23809523809523814, 0.6190476190476191, 0.746031746031746],
        [0.2857142857142856, 0.6428571428571428, 0.7619047619047619],
        [0.33333333333333304, 0.6666666666666665, 0.7777777777777777],
        [0.38095238095238093, 0.6904761904761905, 0.7936507936507936],
        [0.4285714285714288, 0.7142857142857144, 0.8095238095238095],
        [0.4761904761904763, 0.7380952380952381, 0.8253968253968254],
        [0.5238095238095237, 0.7619047619047619, 0.8412698412698412],
        [0.5714285714285712, 0.7857142857142856, 0.8571428571428571],
        [0.6190476190476191, 0.8095238095238095, 0.873015873015873],
        [0.6666666666666665, 0.8333333333333333, 0.8888888888888888],
        [0.714285714285714, 0.857142857142857, 0.9047619047619047],
        [0.7619047619047619, 0.8809523809523809, 0.9206349206349206],
        [0.8095238095238093, 0.9047619047619047, 0.9365079365079365],
        [0.8571428571428568, 0.9285714285714284, 0.9523809523809523],
        [0.9047619047619042, 0.9523809523809521, 0.9682539682539681],
        [0.9523809523809521, 0.976190476190476, 0.9841269841269841],
        [1.0, 1.0, 1.0],
    ]


def get_summer_base() -> list[RGBColor]:
    return [
        [0.0, 0.5, 0.4],
        [0.015873015873015872, 0.5079365079365079, 0.4],
        [0.031746031746031744, 0.5158730158730158, 0.4],
        [0.047619047619047616, 0.5238095238095238, 0.4],
        [0.06349206349206349, 0.5317460317460317, 0.4],
        [0.07936507936507936, 0.5396825396825397, 0.4],
        [0.09523809523809523, 0.5476190476190477, 0.4],
        [0.1111111111111111, 0.5555555555555556, 0.4],
        [0.12698412698412698, 0.5634920634920635, 0.4],
        [0.14285714285714285, 0.5714285714285714, 0.4],
        [0.15873015873015872, 0.5793650793650793, 0.4],
        [0.1746031746031746, 0.5873015873015873, 0.4],
        [0.19047619047619047, 0.5952380952380952, 0.4],
        [0.20634920634920634, 0.6031746031746031, 0.4],
        [0.2222222222222222, 0.6111111111111112, 0.4],
        [0.23809523809523808, 0.6190476190476191, 0.4],
        [0.25396825396825395, 0.626984126984127, 0.4],
        [0.2698412698412698, 0.6349206349206349, 0.4],
        [0.2857142857142857, 0.6428571428571428, 0.4],
        [0.30158730158730157, 0.6507936507936508, 0.4],
        [0.31746031746031744, 0.6587301587301587, 0.4],
        [0.3333333333333333, 0.6666666666666666, 0.4],
        [0.3492063492063492, 0.6746031746031746, 0.4],
        [0.36507936507936506, 0.6825396825396826, 0.4],
        [0.38095238095238093, 0.6904761904761905, 0.4],
        [0.3968253968253968, 0.6984126984126984, 0.4],
        [0.4126984126984127, 0.7063492063492063, 0.4],
        [0.42857142857142855, 0.7142857142857143, 0.4],
        [0.4444444444444444, 0.7222222222222222, 0.4],
        [0.4603174603174603, 0.7301587301587301, 0.4],
        [0.47619047619047616, 0.7380952380952381, 0.4],
        [0.49206349206349204, 0.746031746031746, 0.4],
        [0.5079365079365079, 0.753968253968254, 0.4],
        [0.5238095238095237, 0.7619047619047619, 0.4],
        [0.5396825396825397, 0.7698412698412698, 0.4],
        [0.5555555555555556, 0.7777777777777778, 0.4],
        [0.5714285714285714, 0.7857142857142857, 0.4],
        [0.5873015873015872, 0.7936507936507936, 0.4],
        [0.6031746031746031, 0.8015873015873016, 0.4],
        [0.6190476190476191, 0.8095238095238095, 0.4],
        [0.6349206349206349, 0.8174603174603174, 0.4],
        [0.6507936507936507, 0.8253968253968254, 0.4],
        [0.6666666666666666, 0.8333333333333333, 0.4],
        [0.6825396825396826, 0.8412698412698413, 0.4],
        [0.6984126984126984, 0.8492063492063492, 0.4],
        [0.7142857142857142, 0.8571428571428571, 0.4],
        [0.7301587301587301, 0.8650793650793651, 0.4],
        [0.746031746031746, 0.873015873015873, 0.4],
        [0.7619047619047619, 0.8809523809523809, 0.4],
        [0.7777777777777777, 0.8888888888888888, 0.4],
        [0.7936507936507936, 0.8968253968253967, 0.4],
        [0.8095238095238095, 0.9047619047619048, 0.4],
        [0.8253968253968254, 0.9126984126984127, 0.4],
        [0.8412698412698412, 0.9206349206349206, 0.4],
        [0.8571428571428571, 0.9285714285714286, 0.4],
        [0.873015873015873, 0.9365079365079365, 0.4],
        [0.8888888888888888, 0.9444444444444444, 0.4],
        [0.9047619047619047, 0.9523809523809523, 0.4],
        [0.9206349206349206, 0.9603174603174602, 0.4],
        [0.9365079365079365, 0.9682539682539683, 0.4],
        [0.9523809523809523, 0.9761904761904762, 0.4],
        [0.9682539682539681, 0.9841269841269841, 0.4],
        [0.9841269841269841, 0.9920634920634921, 0.4],
        [1.0, 1.0, 0.4],
    ]


def get_spring_base() -> list[RGBColor]:
    return [
        [1.0, 0.0, 1.0],
        [1.0, 0.015873015873015872, 0.9841269841269842],
        [1.0, 0.031746031746031744, 0.9682539682539683],
        [1.0, 0.047619047619047616, 0.9523809523809523],
        [1.0, 0.06349206349206349, 0.9365079365079365],
        [1.0, 0.07936507936507936, 0.9206349206349207],
        [1.0, 0.09523809523809523, 0.9047619047619048],
        [1.0, 0.1111111111111111, 0.8888888888888888],
        [1.0, 0.12698412698412698, 0.873015873015873],
        [1.0, 0.14285714285714285, 0.8571428571428572],
        [1.0, 0.15873015873015872, 0.8412698412698413],
        [1.0, 0.1746031746031746, 0.8253968253968254],
        [1.0, 0.19047619047619047, 0.8095238095238095],
        [1.0, 0.20634920634920634, 0.7936507936507937],
        [1.0, 0.2222222222222222, 0.7777777777777778],
        [1.0, 0.23809523809523808, 0.7619047619047619],
        [1.0, 0.25396825396825395, 0.746031746031746],
        [1.0, 0.2698412698412698, 0.7301587301587302],
        [1.0, 0.2857142857142857, 0.7142857142857143],
        [1.0, 0.30158730158730157, 0.6984126984126984],
        [1.0, 0.31746031746031744, 0.6825396825396826],
        [1.0, 0.3333333333333333, 0.6666666666666667],
        [1.0, 0.3492063492063492, 0.6507936507936508],
        [1.0, 0.36507936507936506, 0.6349206349206349],
        [1.0, 0.38095238095238093, 0.6190476190476191],
        [1.0, 0.3968253968253968, 0.6031746031746033],
        [1.0, 0.4126984126984127, 0.5873015873015873],
        [1.0, 0.42857142857142855, 0.5714285714285714],
        [1.0, 0.4444444444444444, 0.5555555555555556],
        [1.0, 0.4603174603174603, 0.5396825396825398],
        [1.0, 0.47619047619047616, 0.5238095238095238],
        [1.0, 0.49206349206349204, 0.5079365079365079],
        [1.0, 0.5079365079365079, 0.4920634920634921],
        [1.0, 0.5238095238095237, 0.4761904761904763],
        [1.0, 0.5396825396825397, 0.46031746031746035],
        [1.0, 0.5555555555555556, 0.4444444444444444],
        [1.0, 0.5714285714285714, 0.4285714285714286],
        [1.0, 0.5873015873015872, 0.4126984126984128],
        [1.0, 0.6031746031746031, 0.39682539682539686],
        [1.0, 0.6190476190476191, 0.38095238095238093],
        [1.0, 0.6349206349206349, 0.3650793650793651],
        [1.0, 0.6507936507936507, 0.3492063492063493],
        [1.0, 0.6666666666666666, 0.33333333333333337],
        [1.0, 0.6825396825396826, 0.31746031746031744],
        [1.0, 0.6984126984126984, 0.3015873015873016],
        [1.0, 0.7142857142857142, 0.2857142857142858],
        [1.0, 0.7301587301587301, 0.2698412698412699],
        [1.0, 0.746031746031746, 0.25396825396825395],
        [1.0, 0.7619047619047619, 0.23809523809523814],
        [1.0, 0.7777777777777777, 0.22222222222222232],
        [1.0, 0.7936507936507936, 0.2063492063492064],
        [1.0, 0.8095238095238095, 0.19047619047619047],
        [1.0, 0.8253968253968254, 0.17460317460317465],
        [1.0, 0.8412698412698412, 0.15873015873015883],
        [1.0, 0.8571428571428571, 0.1428571428571429],
        [1.0, 0.873015873015873, 0.12698412698412698],
        [1.0, 0.8888888888888888, 0.11111111111111116],
        [1.0, 0.9047619047619047, 0.09523809523809534],
        [1.0, 0.9206349206349206, 0.07936507936507942],
        [1.0, 0.9365079365079365, 0.06349206349206349],
        [1.0, 0.9523809523809523, 0.04761904761904767],
        [1.0, 0.9682539682539681, 0.031746031746031855],
        [1.0, 0.9841269841269841, 0.015873015873015928],
        [1.0, 1.0, 0.0],
    ]


def get_cool_base() -> list[RGBColor]:
    return [
        [0.0, 1.0, 1.0],
        [0.015873015873015872, 0.9841269841269842, 1.0],
        [0.031746031746031744, 0.9682539682539683, 1.0],
        [0.047619047619047616, 0.9523809523809523, 1.0],
        [0.06349206349206349, 0.9365079365079365, 1.0],
        [0.07936507936507936, 0.9206349206349207, 1.0],
        [0.09523809523809523, 0.9047619047619048, 1.0],
        [0.1111111111111111, 0.8888888888888888, 1.0],
        [0.12698412698412698, 0.873015873015873, 1.0],
        [0.14285714285714285, 0.8571428571428572, 1.0],
        [0.15873015873015872, 0.8412698412698413, 1.0],
        [0.1746031746031746, 0.8253968253968254, 1.0],
        [0.19047619047619047, 0.8095238095238095, 1.0],
        [0.20634920634920634, 0.7936507936507937, 1.0],
        [0.2222222222222222, 0.7777777777777778, 1.0],
        [0.23809523809523808, 0.7619047619047619, 1.0],
        [0.25396825396825395, 0.746031746031746, 1.0],
        [0.2698412698412698, 0.7301587301587302, 1.0],
        [0.2857142857142857, 0.7142857142857143, 1.0],
        [0.30158730158730157, 0.6984126984126984, 1.0],
        [0.31746031746031744, 0.6825396825396826, 1.0],
        [0.3333333333333333, 0.6666666666666667, 1.0],
        [0.3492063492063492, 0.6507936507936508, 1.0],
        [0.36507936507936506, 0.6349206349206349, 1.0],
        [0.38095238095238093, 0.6190476190476191, 1.0],
        [0.3968253968253968, 0.6031746031746033, 1.0],
        [0.4126984126984127, 0.5873015873015873, 1.0],
        [0.42857142857142855, 0.5714285714285714, 1.0],
        [0.4444444444444444, 0.5555555555555556, 1.0],
        [0.4603174603174603, 0.5396825396825398, 1.0],
        [0.47619047619047616, 0.5238095238095238, 1.0],
        [0.49206349206349204, 0.5079365079365079, 1.0],
        [0.5079365079365079, 0.4920634920634921, 1.0],
        [0.5238095238095237, 0.4761904761904763, 1.0],
        [0.5396825396825397, 0.46031746031746035, 1.0],
        [0.5555555555555556, 0.4444444444444444, 1.0],
        [0.5714285714285714, 0.4285714285714286, 1.0],
        [0.5873015873015872, 0.4126984126984128, 1.0],
        [0.6031746031746031, 0.39682539682539686, 1.0],
        [0.6190476190476191, 0.38095238095238093, 1.0],
        [0.6349206349206349, 0.3650793650793651, 1.0],
        [0.6507936507936507, 0.3492063492063493, 1.0],
        [0.6666666666666666, 0.33333333333333337, 1.0],
        [0.6825396825396826, 0.31746031746031744, 1.0],
        [0.6984126984126984, 0.3015873015873016, 1.0],
        [0.7142857142857142, 0.2857142857142858, 1.0],
        [0.7301587301587301, 0.2698412698412699, 1.0],
        [0.746031746031746, 0.25396825396825395, 1.0],
        [0.7619047619047619, 0.23809523809523814, 1.0],
        [0.7777777777777777, 0.22222222222222232, 1.0],
        [0.7936507936507936, 0.2063492063492064, 1.0],
        [0.8095238095238095, 0.19047619047619047, 1.0],
        [0.8253968253968254, 0.17460317460317465, 1.0],
        [0.8412698412698412, 0.15873015873015883, 1.0],
        [0.8571428571428571, 0.1428571428571429, 1.0],
        [0.873015873015873, 0.12698412698412698, 1.0],
        [0.8888888888888888, 0.11111111111111116, 1.0],
        [0.9047619047619047, 0.09523809523809534, 1.0],
        [0.9206349206349206, 0.07936507936507942, 1.0],
        [0.9365079365079365, 0.06349206349206349, 1.0],
        [0.9523809523809523, 0.04761904761904767, 1.0],
        [0.9682539682539681, 0.031746031746031855, 1.0],
        [0.9841269841269841, 0.015873015873015928, 1.0],
        [1.0, 0.0, 1.0],
    ]


def get_hsv_base() -> list[RGBColor]:
    return [
        [1.0, 0.0, 0.0],
        [1.0, 0.09375009375009374, 0.0],
        [1.0, 0.1875001875001875, 0.0],
        [1.0, 0.28125028125028123, 0.0],
        [1.0, 0.375000375000375, 0.0],
        [1.0, 0.4687504687504688, 0.0],
        [1.0, 0.5625005625005625, 0.0],
        [1.0, 0.6562506562506563, 0.0],
        [1.0, 0.75000075000075, 0.0],
        [1.0, 0.8437508437508436, 0.0],
        [0.9999996874996875, 0.937500625000625, 0.0],
        [0.9687489687489689, 1.0, 0.0],
        [0.8749988749988751, 1.0, 0.0],
        [0.7812487812487814, 1.0, 0.0],
        [0.6874986874986876, 1.0, 0.0],
        [0.5937485937485938, 1.0, 0.0],
        [0.4999984999985, 1.0, 0.0],
        [0.4062484062484063, 1.0, 0.0],
        [0.3124983124983125, 1.0, 0.0],
        [0.21874821874821881, 1.0, 0.0],
        [0.12499812499812502, 1.0, 0.0],
        [0.031249343749343742, 1.0, 1.3125013125182343e-06],
        [0.0, 1.0, 0.0625020624890686],
        [0.0, 1.0, 0.15625156561670206],
        [0.0, 1.0, 0.2500010687443355],
        [0.0, 1.0, 0.34375057187196895],
        [0.0, 1.0, 0.43750007499960236],
        [0.0, 1.0, 0.5312495781272358],
        [0.0, 1.0, 0.6249990812548692],
        [0.0, 1.0, 0.7187485843825027],
        [0.0, 1.0, 0.8124980875101362],
        [0.0, 1.0, 0.9062475906377696],
        [0.0, 1.0, 0.999997093765403],
        [0.0, 0.9062528125028132, 1.0],
        [0.0, 0.8125027187527188, 1.0],
        [0.0, 0.718752625002625, 1.0],
        [0.0, 0.6250025312525314, 1.0],
        [0.0, 0.5312524375024383, 1.0],
        [0.0, 0.437502343752344, 1.0],
        [0.0, 0.3437522500022503, 1.0],
        [0.0, 0.2500021562521566, 1.0],
        [0.0, 0.15625206250206347, 1.0],
        [0.0, 0.06250196875196912, 1.0],
        [0.031249374999375024, 1.2500012499527813e-06, 1.0],
        [0.12499821874821886, 0.0, 1.0],
        [0.21874831249831198, 0.0, 1.0],
        [0.3124984062484064, 0.0, 1.0],
        [0.4062484999985002, 0.0, 1.0],
        [0.49999859374859396, 0.0, 1.0],
        [0.593748687498687, 0.0, 1.0],
        [0.6874987812487816, 0.0, 1.0],
        [0.7812488749988753, 0.0, 1.0],
        [0.8749989687489691, 0.0, 1.0],
        [0.9687490624990622, 0.0, 1.0],
        [0.9999997187497188, 0.0, 0.9375005625005625],
        [1.0, 0.0, 0.8437507500007498],
        [1.0, 0.0, 0.7500006562506562],
        [1.0, 0.0, 0.656250562500563],
        [1.0, 0.0, 0.5625004687504687],
        [1.0, 0.0, 0.468750375000375],
        [1.0, 0.0, 0.3750002812502812],
        [1.0, 0.0, 0.28125018750018815],
        [1.0, 0.0, 0.1875000937500937],
        [1.0, 0.0, 0.09375],
    ]


def get_bgr_base() -> list[RGBColor]:
    return [
        [0.0, 0.0, 1.0],
        [0.031746031746031744, 0.0, 0.9682539682539683],
        [0.06349206349206349, 0.0, 0.9365079365079365],
        [0.09523809523809523, 0.0, 0.9047619047619048],
        [0.12698412698412698, 0.0, 0.873015873015873],
        [0.15873015873015872, 0.0, 0.8412698412698413],
        [0.19047619047619047, 0.0, 0.8095238095238095],
        [0.2222222222222222, 0.0, 0.7777777777777778],
        [0.25396825396825395, 0.0, 0.746031746031746],
        [0.2857142857142857, 0.0, 0.7142857142857143],
        [0.31746031746031744, 0.0, 0.6825396825396826],
        [0.3492063492063492, 0.0, 0.6507936507936508],
        [0.38095238095238093, 0.0, 0.6190476190476191],
        [0.4126984126984127, 0.0, 0.5873015873015873],
        [0.4444444444444444, 0.0, 0.5555555555555556],
        [0.47619047619047616, 0.0, 0.5238095238095238],
        [0.5079365079365079, 0.0, 0.4920634920634921],
        [0.5396825396825397, 0.0, 0.46031746031746035],
        [0.5714285714285714, 0.0, 0.4285714285714286],
        [0.6031746031746031, 0.0, 0.39682539682539686],
        [0.6349206349206349, 0.0, 0.3650793650793651],
        [0.6666666666666666, 0.0, 0.33333333333333337],
        [0.6984126984126984, 0.0, 0.3015873015873016],
        [0.7301587301587301, 0.0, 0.2698412698412699],
        [0.7619047619047619, 0.0, 0.23809523809523814],
        [0.7936507936507936, 0.0, 0.2063492063492064],
        [0.8253968253968254, 0.0, 0.17460317460317465],
        [0.8571428571428571, 0.0, 0.1428571428571429],
        [0.8888888888888888, 0.0, 0.11111111111111116],
        [0.9206349206349206, 0.0, 0.07936507936507942],
        [0.9523809523809523, 0.0, 0.04761904761904767],
        [0.9841269841269841, 0.0, 0.015873015873015928],
        [0.9841269841269842, 0.015873015873015872, 0.0],
        [0.9523809523809526, 0.047619047619047394, 0.0],
        [0.9206349206349207, 0.07936507936507936, 0.0],
        [0.8888888888888888, 0.1111111111111111, 0.0],
        [0.8571428571428572, 0.14285714285714285, 0.0],
        [0.8253968253968256, 0.17460317460317437, 0.0],
        [0.7936507936507937, 0.20634920634920634, 0.0],
        [0.7619047619047619, 0.23809523809523808, 0.0],
        [0.7301587301587302, 0.2698412698412698, 0.0],
        [0.6984126984126986, 0.30158730158730135, 0.0],
        [0.6666666666666667, 0.3333333333333333, 0.0],
        [0.6349206349206349, 0.36507936507936506, 0.0],
        [0.6031746031746033, 0.3968253968253968, 0.0],
        [0.5714285714285716, 0.4285714285714283, 0.0],
        [0.5396825396825398, 0.4603174603174603, 0.0],
        [0.5079365079365079, 0.49206349206349204, 0.0],
        [0.47619047619047616, 0.5238095238095238, 0.0],
        [0.44444444444444464, 0.5555555555555554, 0.0],
        [0.4126984126984127, 0.5873015873015873, 0.0],
        [0.38095238095238093, 0.6190476190476191, 0.0],
        [0.3492063492063492, 0.6507936507936508, 0.0],
        [0.31746031746031766, 0.6825396825396823, 0.0],
        [0.2857142857142857, 0.7142857142857143, 0.0],
        [0.25396825396825395, 0.746031746031746, 0.0],
        [0.2222222222222222, 0.7777777777777778, 0.0],
        [0.1904761904761907, 0.8095238095238093, 0.0],
        [0.15873015873015872, 0.8412698412698413, 0.0],
        [0.12698412698412698, 0.873015873015873, 0.0],
        [0.09523809523809523, 0.9047619047619048, 0.0],
        [0.06349206349206371, 0.9365079365079363, 0.0],
        [0.031746031746031744, 0.9682539682539683, 0.0],
        [0.0, 1.0, 0.0],
    ]


def get_pink_base() -> list[RGBColor]:
    return [
        [0.1178, 0.0, 0.0],
        [0.1958570548040548, 0.10286904261004261, 0.10286904261004261],
        [0.2506610896140896, 0.1454790653900654, 0.1454790653900654],
        [0.29546811656811656, 0.17817408269208268, 0.17817408269208268],
        [0.33432413915213915, 0.20573809713609714, 0.20573809713609714],
        [0.3691121589001589, 0.23002210977010978, 0.23002210977010978],
        [0.40089217663417664, 0.25197612114012113, 0.25197612114012113],
        [0.4303311928571929, 0.27216613153713154, 0.27216613153713154],
        [0.4578822078802079, 0.2909571412001412, 0.2909571412001412],
        [0.48386722192222187, 0.30860715023715024, 0.30860715023715024],
        [0.5085252351702352, 0.3253001587801588, 0.3253001587801588],
        [0.5320422477312478, 0.34117816687016683, 0.34117816687016683],
        [0.5545632596922597, 0.3563481746121746, 0.3563481746121746],
        [0.5762042711412712, 0.370899182013182, 0.370899182013182],
        [0.5970612821282821, 0.38490018914018914, 0.38490018914018914],
        [0.6172132927402928, 0.39841019599019595, 0.39841019599019595],
        [0.6367293029443029, 0.4114762026082026, 0.4114762026082026],
        [0.6556633128513129, 0.42413920904920904, 0.42413920904920904],
        [0.6740663224523225, 0.4364362152622152, 0.4364362152622152],
        [0.6919803317593318, 0.44839522133122134, 0.44839522133122134],
        [0.7094413408403408, 0.4600442272202272, 0.4600442272202272],
        [0.7264833496713496, 0.471405232953233, 0.471405232953233],
        [0.7431343583143583, 0.48249823856823854, 0.48249823856823854],
        [0.7594211595051595, 0.4933425567615568, 0.49334224405324406],
        [0.766356164952165, 0.517549555000555, 0.5039532493842493],
        [0.7732291703251704, 0.5406745543755543, 0.5143442546752547],
        [0.7800421756041757, 0.5628495546845547, 0.5245312597662597],
        [0.7867961807921808, 0.5841835557145558, 0.5345222648432649],
        [0.7934921859201859, 0.6047655573125573, 0.5443312697802698],
        [0.800132190994191, 0.6246695593815594, 0.5539662746302746],
        [0.806718195960196, 0.6439585618705619, 0.5634362794202794],
        [0.8132502008675463, 0.6626875645679968, 0.572750284066388],
        [0.8197297991324537, 0.6808994354320032, 0.5819137159336122],
        [0.8261598070998071, 0.6986374678594677, 0.5909367293097293],
        [0.832538815008815, 0.7159364983284983, 0.5998237422767423],
        [0.8388698227318228, 0.7328275270515271, 0.6085807548037548],
        [0.8451538303318303, 0.7493375542295543, 0.617212766935767],
        [0.8513918378118378, 0.7654925799695799, 0.6257267786357786],
        [0.8575838451998452, 0.7813126044996045, 0.63412579002479],
        [0.8637308524718524, 0.7968186278556278, 0.642415801039801],
        [0.8698348596078597, 0.8120286501696502, 0.6505998117678118],
        [0.8758968666358666, 0.8269596715176715, 0.6586818221958222],
        [0.8819168735798735, 0.8416246920346919, 0.6666668323148323],
        [0.8878958804198804, 0.8560397116997117, 0.6745558422198422],
        [0.8938348871588873, 0.8702157306557308, 0.6823548518188519],
        [0.8997348937998937, 0.8841637489357488, 0.6900658612018611],
        [0.9055969003459003, 0.8978957665557666, 0.6976908703748703],
        [0.9114209068159068, 0.9114207835997836, 0.7052338793118793],
        [0.9172079131949132, 0.9172079131949132, 0.7271656710196709],
        [0.9229579194999195, 0.9229579194999195, 0.7484547019537018],
        [0.9286729257049257, 0.9286729257049257, 0.7691557308867307],
        [0.9343529318399318, 0.9343529318399318, 0.7893137581037581],
        [0.939998937893938, 0.939998937893938, 0.8089687837947839],
        [0.9456109438799438, 0.9456109438799438, 0.828158808099808],
        [0.9511899497889498, 0.9511899497889498, 0.8469128312138313],
        [0.9567359556319557, 0.9567359556319557, 0.8652608532158531],
        [0.9622499614019614, 0.9622499614019614, 0.8832288742238742],
        [0.967732967101967, 0.967732967101967, 0.9008368943518943],
        [0.9731849727399727, 0.9731849727399727, 0.9181089136399135],
        [0.9786069783119783, 0.9786069783119783, 0.9350609321919323],
        [0.9839989838239838, 0.9839989838239838, 0.95171095004995],
        [0.9893609892759894, 0.9893609892759894, 0.9680749672719673],
        [0.9946949946659946, 0.9946949946659946, 0.984166983907984],
        [1.0, 1.0, 1.0],
    ]


def get_hot_base() -> list[RGBColor]:
    return [
        [0.0416, 0.0, 0.0],
        [0.0832696068869982, 0.0, 0.0],
        [0.12493921377399639, 0.0, 0.0],
        [0.16660882066099458, 0.0, 0.0],
        [0.20827842754799278, 0.0, 0.0],
        [0.24994803443499097, 0.0, 0.0],
        [0.2916176413219892, 0.0, 0.0],
        [0.33328724820898736, 0.0, 0.0],
        [0.37495685509598553, 0.0, 0.0],
        [0.4166264619829838, 0.0, 0.0],
        [0.458296068869982, 0.0, 0.0],
        [0.49996567575698014, 0.0, 0.0],
        [0.5416352826439783, 0.0, 0.0],
        [0.5833048895309766, 0.0, 0.0],
        [0.6249744964179746, 0.0, 0.0],
        [0.6666441033049729, 0.0, 0.0],
        [0.7083137101919711, 0.0, 0.0],
        [0.7499833170789694, 0.0, 0.0],
        [0.7916529239659675, 0.0, 0.0],
        [0.8333225308529657, 0.0, 0.0],
        [0.8749921377399639, 0.0, 0.0],
        [0.916661744626962, 0.0, 0.0],
        [0.9583313515139603, 0.0, 0.0],
        [1.0, 9.583317761411433e-07, 0.0],
        [1.0, 0.041667557290219495, 0.0],
        [1.0, 0.08333415624866285, 0.0],
        [1.0, 0.12500075520710618, 0.0],
        [1.0, 0.16666735416554954, 0.0],
        [1.0, 0.2083339531239929, 0.0],
        [1.0, 0.25000055208243627, 0.0],
        [1.0, 0.2916671510408796, 0.0],
        [1.0, 0.33333374999932297, 0.0],
        [1.0, 0.3750003489577663, 0.0],
        [1.0, 0.41666694791620934, 0.0],
        [1.0, 0.458333546874653, 0.0],
        [1.0, 0.5000001458330964, 0.0],
        [1.0, 0.5416667447915398, 0.0],
        [1.0, 0.5833333437499828, 0.0],
        [1.0, 0.6249999427084264, 0.0],
        [1.0, 0.6666665416668698, 0.0],
        [1.0, 0.7083331406253132, 0.0],
        [1.0, 0.7499997395837562, 0.0],
        [1.0, 0.7916663385421998, 0.0],
        [1.0, 0.8333329375006432, 0.0],
        [1.0, 0.8749995364590866, 0.0],
        [1.0, 0.9166661354175296, 0.0],
        [1.0, 0.9583327343759732, 0.0],
        [1.0, 0.9999993333344166, 0.0],
        [1.0, 1.0, 0.06249906249906237],
        [1.0, 1.0, 0.12499912499912444],
        [1.0, 1.0, 0.1874991874991874],
        [1.0, 1.0, 0.24999924999924988],
        [1.0, 1.0, 0.3124993124993124],
        [1.0, 1.0, 0.37499937499937447],
        [1.0, 1.0, 0.4374994374994374],
        [1.0, 1.0, 0.49999949999949994],
        [1.0, 1.0, 0.5624995624995625],
        [1.0, 1.0, 0.6249996249996245],
        [1.0, 1.0, 0.6874996874996875],
        [1.0, 1.0, 0.7499997499997499],
        [1.0, 1.0, 0.8124998124998125],
        [1.0, 1.0, 0.8749998749998745],
        [1.0, 1.0, 0.9374999374999375],
        [1.0, 1.0, 1.0],
    ]


def get_plasma_base() -> list[RGBColor]:
    return [
        [0.050383, 0.029803, 0.527975],
        [0.096379, 0.025165, 0.547103],
        [0.132381, 0.022258, 0.56325],
        [0.16407, 0.020171, 0.577478],
        [0.193374, 0.018354, 0.59033],
        [0.221197, 0.016497, 0.602083],
        [0.248032, 0.014439, 0.612868],
        [0.274191, 0.012109, 0.622722],
        [0.299855, 0.009561, 0.631624],
        [0.32515, 0.006915, 0.639512],
        [0.35015, 0.004382, 0.646298],
        [0.374897, 0.002245, 0.651876],
        [0.399411, 0.000859, 0.656133],
        [0.423689, 0.000646, 0.658956],
        [0.447714, 0.00208, 0.66024],
        [0.471457, 0.005678, 0.659897],
        [0.500678, 0.014055, 0.657088],
        [0.523633, 0.024532, 0.652901],
        [0.546157, 0.038954, 0.64701],
        [0.568201, 0.055778, 0.639477],
        [0.589719, 0.072878, 0.630408],
        [0.610667, 0.090204, 0.619951],
        [0.631017, 0.107699, 0.608287],
        [0.650746, 0.125309, 0.595617],
        [0.669845, 0.142992, 0.582154],
        [0.688318, 0.160709, 0.568103],
        [0.706178, 0.178437, 0.553657],
        [0.723444, 0.196158, 0.538981],
        [0.740143, 0.213864, 0.524216],
        [0.756304, 0.231555, 0.509468],
        [0.771958, 0.249237, 0.494813],
        [0.787133, 0.266922, 0.480307],
        [0.805467, 0.289057, 0.462415],
        [0.819651, 0.306812, 0.448306],
        [0.833422, 0.324635, 0.434366],
        [0.846788, 0.342551, 0.420579],
        [0.85975, 0.360588, 0.406917],
        [0.872303, 0.378774, 0.393355],
        [0.884436, 0.397139, 0.37986],
        [0.896131, 0.415712, 0.366407],
        [0.907365, 0.434524, 0.35297],
        [0.918109, 0.453603, 0.339529],
        [0.928329, 0.472975, 0.326067],
        [0.93799, 0.492667, 0.312575],
        [0.947051, 0.512699, 0.299049],
        [0.95547, 0.533093, 0.28549],
        [0.963203, 0.553865, 0.271909],
        [0.970205, 0.575028, 0.258325],
        [0.977856, 0.602051, 0.241387],
        [0.983041, 0.624131, 0.227937],
        [0.987332, 0.646633, 0.214648],
        [0.990681, 0.669558, 0.201642],
        [0.993032, 0.692907, 0.189084],
        [0.994324, 0.716681, 0.177208],
        [0.994495, 0.74088, 0.166335],
        [0.993482, 0.765499, 0.156891],
        [0.991209, 0.790537, 0.149377],
        [0.987621, 0.815978, 0.144363],
        [0.982653, 0.841812, 0.142303],
        [0.976265, 0.868016, 0.143351],
        [0.968443, 0.894564, 0.147014],
        [0.959276, 0.921407, 0.151566],
        [0.949151, 0.948435, 0.152178],
        [0.940015, 0.975158, 0.131326],
    ]


def get_viridis_base() -> list[RGBColor]:
    return [
        [0.267004, 0.004874, 0.329415],
        [0.272594, 0.025563, 0.353093],
        [0.277018, 0.050344, 0.375715],
        [0.280267, 0.073417, 0.397163],
        [0.282327, 0.094955, 0.417331],
        [0.283197, 0.11568, 0.436115],
        [0.282884, 0.13592, 0.453427],
        [0.281412, 0.155834, 0.469201],
        [0.278826, 0.17549, 0.483397],
        [0.275191, 0.194905, 0.496005],
        [0.270595, 0.214069, 0.507052],
        [0.265145, 0.232956, 0.516599],
        [0.258965, 0.251537, 0.524736],
        [0.252194, 0.269783, 0.531579],
        [0.244972, 0.287675, 0.53726],
        [0.237441, 0.305202, 0.541921],
        [0.227802, 0.326594, 0.546532],
        [0.220057, 0.343307, 0.549413],
        [0.212395, 0.359683, 0.55171],
        [0.204903, 0.375746, 0.553533],
        [0.197636, 0.391528, 0.554969],
        [0.190631, 0.407061, 0.556089],
        [0.183898, 0.422383, 0.556944],
        [0.177423, 0.437527, 0.557565],
        [0.171176, 0.45253, 0.557965],
        [0.165117, 0.467423, 0.558141],
        [0.159194, 0.482237, 0.558073],
        [0.153364, 0.497, 0.557724],
        [0.147607, 0.511733, 0.557049],
        [0.141935, 0.526453, 0.555991],
        [0.136408, 0.541173, 0.554483],
        [0.131172, 0.555899, 0.552459],
        [0.125394, 0.574318, 0.549086],
        [0.121831, 0.589055, 0.545623],
        [0.119738, 0.603785, 0.5414],
        [0.119699, 0.61849, 0.536347],
        [0.122312, 0.633153, 0.530398],
        [0.128087, 0.647749, 0.523491],
        [0.137339, 0.662252, 0.515571],
        [0.150148, 0.676631, 0.506589],
        [0.166383, 0.690856, 0.496502],
        [0.185783, 0.704891, 0.485273],
        [0.20803, 0.718701, 0.472873],
        [0.232815, 0.732247, 0.459277],
        [0.259857, 0.745492, 0.444467],
        [0.288921, 0.758394, 0.428426],
        [0.319809, 0.770914, 0.411152],
        [0.35236, 0.783011, 0.392636],
        [0.395174, 0.797475, 0.367757],
        [0.430983, 0.808473, 0.346476],
        [0.468053, 0.818921, 0.323998],
        [0.506271, 0.828786, 0.300362],
        [0.545524, 0.838039, 0.275626],
        [0.585678, 0.846661, 0.249897],
        [0.626579, 0.854645, 0.223353],
        [0.668054, 0.861999, 0.196293],
        [0.709898, 0.868751, 0.169257],
        [0.751884, 0.874951, 0.143228],
        [0.79376, 0.880678, 0.120005],
        [0.83527, 0.886029, 0.102646],
        [0.876168, 0.891125, 0.09525],
        [0.916242, 0.896091, 0.100717],
        [0.9553, 0.901065, 0.118128],
        [0.993248, 0.906157, 0.143936],
    ]


def get_cividis_base() -> list[RGBColor]:
    return [
        [0.0, 0.135112, 0.304751],
        [0.0, 0.146877, 0.330479],
        [0.0, 0.157932, 0.357521],
        [0.0, 0.168204, 0.385902],
        [0.0, 0.178802, 0.414764],
        [0.0, 0.188769, 0.439563],
        [0.017852, 0.198528, 0.441248],
        [0.068968, 0.209372, 0.438863],
        [0.103401, 0.220406, 0.43579],
        [0.130669, 0.231458, 0.43284],
        [0.154261, 0.242475, 0.43012],
        [0.17549, 0.253444, 0.42779],
        [0.195057, 0.264372, 0.425924],
        [0.213431, 0.275266, 0.42448],
        [0.230871, 0.286134, 0.423498],
        [0.247605, 0.296986, 0.422917],
        [0.267693, 0.310542, 0.422821],
        [0.28324, 0.32139, 0.423211],
        [0.298421, 0.332247, 0.423973],
        [0.313287, 0.34312, 0.42512],
        [0.327875, 0.354016, 0.42667],
        [0.342246, 0.364939, 0.428559],
        [0.356418, 0.375896, 0.430823],
        [0.37043, 0.38689, 0.433428],
        [0.384268, 0.397928, 0.436475],
        [0.397991, 0.409011, 0.439848],
        [0.411607, 0.420145, 0.443577],
        [0.42512, 0.431334, 0.447692],
        [0.438504, 0.44258, 0.452341],
        [0.451759, 0.453887, 0.457582],
        [0.464947, 0.465241, 0.463395],
        [0.478186, 0.476699, 0.468845],
        [0.495913, 0.491076, 0.471751],
        [0.51054, 0.502643, 0.47255],
        [0.525348, 0.514285, 0.47266],
        [0.540307, 0.526005, 0.472163],
        [0.555393, 0.537807, 0.471147],
        [0.570607, 0.549695, 0.469593],
        [0.585916, 0.561674, 0.467618],
        [0.601354, 0.573743, 0.465074],
        [0.616852, 0.585913, 0.462237],
        [0.632506, 0.59818, 0.458668],
        [0.648222, 0.610553, 0.454801],
        [0.664055, 0.623034, 0.450338],
        [0.679979, 0.635626, 0.445424],
        [0.695985, 0.648334, 0.440072],
        [0.712105, 0.66116, 0.434117],
        [0.728334, 0.674107, 0.427554],
        [0.748772, 0.69047, 0.418448],
        [0.765223, 0.703705, 0.410587],
        [0.781795, 0.717074, 0.401966],
        [0.79848, 0.73058, 0.392597],
        [0.815274, 0.744226, 0.382504],
        [0.832192, 0.758014, 0.371529],
        [0.849223, 0.771947, 0.359729],
        [0.866421, 0.786028, 0.346571],
        [0.88372, 0.800258, 0.332599],
        [0.901195, 0.814639, 0.317021],
        [0.918828, 0.829168, 0.29996],
        [0.93666, 0.843841, 0.280876],
        [0.954725, 0.858646, 0.259365],
        [0.973114, 0.87355, 0.234677],
        [0.992218, 0.888385, 0.205468],
        [0.995737, 0.909344, 0.217772],
    ]


def get_twilight_base() -> list[RGBColor]:
    return [
        [0.8857501584075443, 0.8500092494306783, 0.8879736506427196],
        [0.8669601550533358, 0.8510896085314068, 0.8790976697717334],
        [0.8403042080595778, 0.8425605950855084, 0.865250882410458],
        [0.8046916442981458, 0.8270983878056066, 0.8484244929697402],
        [0.7618690793798029, 0.8071194938764749, 0.830266486168872],
        [0.7150040307625213, 0.7842648709158119, 0.8131111655994991],
        [0.6672147980375281, 0.7595112803730375, 0.7986367465453776],
        [0.6209919306481755, 0.733369347989232, 0.7871899462469658],
        [0.5778116438998202, 0.7061310615715398, 0.7782534512102552],
        [0.5383401557517628, 0.677970811290954, 0.7711273443905772],
        [0.5028853540415561, 0.6489721673409526, 0.7652144671817491],
        [0.46808490970531597, 0.6153702869579029, 0.7594078989109274],
        [0.441912717666964, 0.5846144110017557, 0.7545183888441067],
        [0.42037822361396326, 0.5529928715810817, 0.7494412559451894],
        [0.40346600333905397, 0.5204784765384003, 0.7437421522356709],
        [0.390920175719949, 0.4870552025122304, 0.7369797713388125],
        [0.38222094988570376, 0.45272225034283325, 0.7287165614400378],
        [0.37662448930806297, 0.41749553747893614, 0.7185245473617611],
        [0.37324816143326384, 0.38140848555753937, 0.7059820097480604],
        [0.37115856636209105, 0.3445191141023017, 0.6906525358646499],
        [0.3694333459127623, 0.3069292355186234, 0.6720505284912062],
        [0.3668108554010799, 0.26404857724525643, 0.6464799165290824],
        [0.3628764356004103, 0.2258414929328703, 0.6188041741082302],
        [0.35641381143126327, 0.18837119869242164, 0.5855320765920552],
        [0.3463150717579309, 0.15312802296627787, 0.5457599924248665],
        [0.33146154891145124, 0.12244245263391232, 0.4991827458843107],
        [0.31129434479712365, 0.0986847719340522, 0.4471931371516162],
        [0.28669151388283165, 0.08242987384848069, 0.39331182532243375],
        [0.26003895187439957, 0.0722947810433622, 0.3417175669546984],
        [0.23433055727563878, 0.0669355996616394, 0.295740099298676],
        [0.21237411048541005, 0.06608502466175845, 0.2576516509356954],
        [0.1960826032659409, 0.07032122724242362, 0.22874673279569585],
        [0.19571853588267552, 0.07215778103960274, 0.21617499187076789],
        [0.21625279838647882, 0.06761633027051639, 0.22324568672294431],
        [0.2438114972024792, 0.06828985152901187, 0.23550427698321885],
        [0.277471508091428, 0.07258296989925478, 0.2512349399594277],
        [0.3153587000920581, 0.07942099731524319, 0.2681190407694196],
        [0.35573889947341125, 0.08803897435024335, 0.2840695897279818],
        [0.39719876876325577, 0.09830232096827862, 0.2974385647628578],
        [0.4385637818793154, 0.11077667828375456, 0.30712600062348083],
        [0.47877034239726296, 0.12642401401409453, 0.3127417273582196],
        [0.5169084880054446, 0.14598463068714407, 0.3147540759228004],
        [0.5566322903496934, 0.17269677158182117, 0.31423043195101424],
        [0.5889486193554471, 0.19997020971775276, 0.31288922211590126],
        [0.6185686525887719, 0.2297879924917992, 0.3120046383872893],
        [0.6456734938264543, 0.2615520316161528, 0.31248673753935263],
        [0.670399595476762, 0.29486126309253047, 0.3150381395687221],
        [0.6928154484676493, 0.32945984647042675, 0.3202754315314667],
        [0.7129346683977347, 0.36517570519856757, 0.3288027427038317],
        [0.7307482075484517, 0.401868526884681, 0.3412449533046818],
        [0.7462661578916344, 0.439392450449735, 0.3582343914230064],
        [0.7595711110534006, 0.4775687122205708, 0.380360657784311],
        [0.7708809196230247, 0.516168926434691, 0.40808667584648967],
        [0.7817418047898184, 0.5597509805259486, 0.44624687186865436],
        [0.7904577684772788, 0.5982813427706246, 0.48622257801969754],
        [0.7991624518501963, 0.6362837937202919, 0.5313449594256143],
        [0.8087321917008969, 0.6734621670219452, 0.5806983480557674],
        [0.8198723065045529, 0.7095047497878748, 0.6330385704006711],
        [0.8328327718577798, 0.7439727181745988, 0.6868223587464855],
        [0.847111955079651, 0.7761188023604716, 0.7401227576280706],
        [0.8610711702756552, 0.8047851790981223, 0.7903952966373629],
        [0.8721533197845432, 0.8284805282370967, 0.8330486712880828],
        [0.8811916987134195, 0.8442906671771735, 0.8635659516866988],
        [0.8857115512284565, 0.8500218611585632, 0.8857253899008712],
    ]


def get_turbo_base() -> list[RGBColor]:
    return [
        [0.18995, 0.07176, 0.23217],
        [0.2086, 0.11802, 0.34607],
        [0.225, 0.16354, 0.45096],
        [0.23915, 0.20833, 0.54686],
        [0.25107, 0.25237, 0.63374],
        [0.26074, 0.29568, 0.71162],
        [0.26816, 0.33825, 0.7805],
        [0.27334, 0.38008, 0.84037],
        [0.27628, 0.42118, 0.89123],
        [0.27698, 0.46153, 0.93309],
        [0.27543, 0.50115, 0.96594],
        [0.27106, 0.54015, 0.9893],
        [0.25862, 0.57958, 0.99876],
        [0.23874, 0.61931, 0.99485],
        [0.21382, 0.65886, 0.97959],
        [0.18625, 0.69775, 0.95498],
        [0.15173, 0.74472, 0.91416],
        [0.12698, 0.78037, 0.8759],
        [0.10738, 0.81381, 0.83484],
        [0.09532, 0.84455, 0.79299],
        [0.0932, 0.87211, 0.75237],
        [0.10342, 0.896, 0.715],
        [0.12733, 0.91701, 0.67627],
        [0.16319, 0.93609, 0.63137],
        [0.20877, 0.95304, 0.58199],
        [0.2618, 0.96765, 0.52981],
        [0.32006, 0.97974, 0.47654],
        [0.38127, 0.98909, 0.42386],
        [0.44321, 0.99551, 0.37345],
        [0.50362, 0.99879, 0.32701],
        [0.56026, 0.99873, 0.28623],
        [0.61088, 0.99514, 0.2528],
        [0.66428, 0.98524, 0.2237],
        [0.70553, 0.97255, 0.21032],
        [0.74617, 0.95593, 0.20406],
        [0.78563, 0.93579, 0.20336],
        [0.82333, 0.91253, 0.20663],
        [0.85868, 0.88655, 0.2123],
        [0.89112, 0.85826, 0.2188],
        [0.92004, 0.82806, 0.22456],
        [0.94489, 0.79634, 0.228],
        [0.96507, 0.76352, 0.22754],
        [0.98, 0.73, 0.22161],
        [0.98986, 0.69382, 0.21043],
        [0.99535, 0.65341, 0.19577],
        [0.99672, 0.60977, 0.17842],
        [0.99419, 0.56386, 0.15918],
        [0.98799, 0.51667, 0.13883],
        [0.97545, 0.4574, 0.11305],
        [0.96187, 0.41093, 0.0931],
        [0.94538, 0.36638, 0.07461],
        [0.92623, 0.32473, 0.05837],
        [0.90463, 0.28696, 0.04516],
        [0.88066, 0.25334, 0.03521],
        [0.8538, 0.2217, 0.02677],
        [0.82399, 0.19182, 0.01966],
        [0.79125, 0.16368, 0.01387],
        [0.75556, 0.13731, 0.00942],
        [0.71692, 0.11268, 0.00629],
        [0.67535, 0.0898, 0.00449],
        [0.63082, 0.06868, 0.00401],
        [0.58336, 0.04931, 0.00486],
        [0.53295, 0.03169, 0.00705],
        [0.4796, 0.01583, 0.01055],
    ]


def get_seismic_base() -> list[RGBColor]:
    return [
        [0.0, 0.0, 0.3],
        [0.0, 0.0, 0.34444444444444444],
        [0.0, 0.0, 0.38888888888888884],
        [0.0, 0.0, 0.4333333333333333],
        [0.0, 0.0, 0.47777777777777775],
        [0.0, 0.0, 0.5222222222222221],
        [0.0, 0.0, 0.5666666666666667],
        [0.0, 0.0, 0.611111111111111],
        [0.0, 0.0, 0.6555555555555554],
        [0.0, 0.0, 0.7],
        [0.0, 0.0, 0.7444444444444444],
        [0.0, 0.0, 0.7888888888888888],
        [0.0, 0.0, 0.8333333333333333],
        [0.0, 0.0, 0.8777777777777778],
        [0.0, 0.0, 0.922222222222222],
        [0.0, 0.0, 0.9666666666666666],
        [0.015873015873015872, 0.015873015873015872, 1.0],
        [0.07936507936507936, 0.07936507936507936, 1.0],
        [0.14285714285714285, 0.14285714285714285, 1.0],
        [0.20634920634920634, 0.20634920634920634, 1.0],
        [0.2698412698412698, 0.2698412698412698, 1.0],
        [0.3333333333333333, 0.3333333333333333, 1.0],
        [0.3968253968253968, 0.3968253968253968, 1.0],
        [0.4603174603174603, 0.4603174603174603, 1.0],
        [0.5238095238095238, 0.5238095238095238, 1.0],
        [0.5873015873015873, 0.5873015873015873, 1.0],
        [0.6507936507936508, 0.6507936507936508, 1.0],
        [0.7142857142857143, 0.7142857142857143, 1.0],
        [0.7777777777777778, 0.7777777777777778, 1.0],
        [0.8412698412698413, 0.8412698412698413, 1.0],
        [0.9047619047619048, 0.9047619047619048, 1.0],
        [0.9682539682539683, 0.9682539682539683, 1.0],
        [1.0, 0.9682539682539683, 0.9682539682539683],
        [1.0, 0.9047619047619052, 0.9047619047619052],
        [1.0, 0.8412698412698413, 0.8412698412698413],
        [1.0, 0.7777777777777778, 0.7777777777777778],
        [1.0, 0.7142857142857143, 0.7142857142857143],
        [1.0, 0.6507936507936513, 0.6507936507936513],
        [1.0, 0.5873015873015873, 0.5873015873015873],
        [1.0, 0.5238095238095238, 0.5238095238095238],
        [1.0, 0.46031746031746035, 0.46031746031746035],
        [1.0, 0.3968253968253973, 0.3968253968253973],
        [1.0, 0.33333333333333337, 0.33333333333333337],
        [1.0, 0.2698412698412699, 0.2698412698412699],
        [1.0, 0.2063492063492064, 0.2063492063492064],
        [1.0, 0.14285714285714335, 0.14285714285714335],
        [1.0, 0.07936507936507942, 0.07936507936507942],
        [1.0, 0.015873015873015928, 0.015873015873015928],
        [0.9761904761904762, 0.0, 0.0],
        [0.9444444444444446, 0.0, 0.0],
        [0.9126984126984127, 0.0, 0.0],
        [0.8809523809523809, 0.0, 0.0],
        [0.8492063492063492, 0.0, 0.0],
        [0.8174603174603177, 0.0, 0.0],
        [0.7857142857142857, 0.0, 0.0],
        [0.753968253968254, 0.0, 0.0],
        [0.7222222222222222, 0.0, 0.0],
        [0.6904761904761907, 0.0, 0.0],
        [0.6587301587301587, 0.0, 0.0],
        [0.626984126984127, 0.0, 0.0],
        [0.5952380952380952, 0.0, 0.0],
        [0.5634920634920637, 0.0, 0.0],
        [0.5317460317460317, 0.0, 0.0],
        [0.5, 0.0, 0.0],
    ]
