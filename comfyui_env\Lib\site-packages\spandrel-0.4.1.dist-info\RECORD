spandrel-0.4.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
spandrel-0.4.1.dist-info/METADATA,sha256=t0_ljQxbLi2_gDjhuQlBshzmV0WaZp9VdFx5HdwPLDo,15493
spandrel-0.4.1.dist-info/RECORD,,
spandrel-0.4.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel-0.4.1.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
spandrel-0.4.1.dist-info/top_level.txt,sha256=bNYky20zJCBLFqnR9qneKKk45heuhs9rCRGOrt3CFas,9
spandrel/__helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/__helpers/__pycache__/__init__.cpython-311.pyc,,
spandrel/__helpers/__pycache__/canonicalize.cpython-311.pyc,,
spandrel/__helpers/__pycache__/loader.cpython-311.pyc,,
spandrel/__helpers/__pycache__/main_registry.cpython-311.pyc,,
spandrel/__helpers/__pycache__/model_descriptor.cpython-311.pyc,,
spandrel/__helpers/__pycache__/registry.cpython-311.pyc,,
spandrel/__helpers/__pycache__/size_req.cpython-311.pyc,,
spandrel/__helpers/__pycache__/unpickler.cpython-311.pyc,,
spandrel/__helpers/canonicalize.py,sha256=CxYNBAK-9l0Xpapky652Ii7VwcYt8LoEVeWsb83b9ow,1421
spandrel/__helpers/loader.py,sha256=UPwpmrjtk2SsKxvLEddI-ZWSZwcIuQHgt2xgNfSu9cc,3801
spandrel/__helpers/main_registry.py,sha256=-on7J1oKBceDrHfEPl3WcD68MvP1BG1rWC1ZB9vbkpQ,3049
spandrel/__helpers/model_descriptor.py,sha256=GXobonsKX-Sv9YWqEaPsojmnc-GbBwbOJUiQHziy2Kg,19540
spandrel/__helpers/registry.py,sha256=mScIM53GFZJFnvTpymxBFb212RqdzwTWq1Ehz9cSW3o,6437
spandrel/__helpers/size_req.py,sha256=wd027UPsOuJ2pVVjXjh85mIzLTYC9bH8EmhRLq1JHVM,3026
spandrel/__helpers/unpickler.py,sha256=YA12ATt4jmjK_K3_v7g_cjU9X5Bi9SINEyvQYMgFcBs,976
spandrel/__init__.py,sha256=lAVAj1obl1LS4quzP-QQF2jVn12UNOv08CTBkHuP0jI,1219
spandrel/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/ATD/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/ATD/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/ATD/__arch/__pycache__/atd_arch.cpython-311.pyc,,
spandrel/architectures/ATD/__arch/atd_arch.py,sha256=RtE_wXupfplhR5G5TP8syddhkMN0iSm0Yqy_7L5b1-M,41313
spandrel/architectures/ATD/__init__.py,sha256=f0Dr6urEM09DXgQSaj3notmq4Q41_yAcGF7JCAtMaB0,6570
spandrel/architectures/ATD/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/CRAFT/__arch/CRAFT.py,sha256=u8vlBFE9iFG5FeIvLZZrHcrJFI0MUxqriph7Et3xmm8,30832
spandrel/architectures/CRAFT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/CRAFT/__arch/__pycache__/CRAFT.cpython-311.pyc,,
spandrel/architectures/CRAFT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/CRAFT/__init__.py,sha256=kNUU8c4mWH_vyLnF1JXvW36bIjMEbew4M2IlxxGbQ_8,4121
spandrel/architectures/CRAFT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Compact/__arch/SRVGG.py,sha256=r0UgOwQOlgsMNz51C8TH6ZrXZ5iH41MvnnsmpuyPVog,2846
spandrel/architectures/Compact/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/Compact/__arch/__pycache__/SRVGG.cpython-311.pyc,,
spandrel/architectures/Compact/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Compact/__init__.py,sha256=qbAYl1gpOdPs3cdj2T7XlYU0ybpn_BmwAlfJMXSTss4,1663
spandrel/architectures/Compact/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DAT/__arch/DAT.py,sha256=sOmn3ihV9kgNlQbbZ3YVjQMPiASMtRuzVyZNAu3GJgQ,36856
spandrel/architectures/DAT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DAT/__arch/__pycache__/DAT.cpython-311.pyc,,
spandrel/architectures/DAT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DAT/__init__.py,sha256=1ubQj7xqgPRTfYYkRm_cX8aS5XKVsi58neDXs7s7qaM,6662
spandrel/architectures/DAT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DCTLSA/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DCTLSA/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DCTLSA/__arch/__pycache__/dctlsa.cpython-311.pyc,,
spandrel/architectures/DCTLSA/__arch/dctlsa.py,sha256=7UobuYcjRb9irHNFTkeuoPsXIAM7FVcNxm_EzIjzmM0,15258
spandrel/architectures/DCTLSA/__init__.py,sha256=QXFNXalBraNUFE3qMYGOus9b-kCTISUq2QW4v84HX8k,2812
spandrel/architectures/DCTLSA/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DITN/__arch/DITN_Real.py,sha256=4OM73MOmiFS0bqVbmMqzEmztVDQh0HXcurbWtWxLemM,9495
spandrel/architectures/DITN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DITN/__arch/__pycache__/DITN_Real.cpython-311.pyc,,
spandrel/architectures/DITN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DITN/__init__.py,sha256=0HoaNfhxlFXfO4v6T-ObzUCsrRqPVntH-8a6mDKoZ-Q,2695
spandrel/architectures/DITN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DRCT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DRCT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DRCT/__arch/__pycache__/drct_arch.cpython-311.pyc,,
spandrel/architectures/DRCT/__arch/drct_arch.py,sha256=QVMVqVUg-HNUCKxmKvU56JHHTG6irQfTPNsszo9_igk,28544
spandrel/architectures/DRCT/__init__.py,sha256=_GJswqEV_r1Q3EUffqoWBV7Job7-ANGOranGQgoCHsM,5179
spandrel/architectures/DRCT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DRUNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DRUNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DRUNet/__arch/__pycache__/network_unet.cpython-311.pyc,,
spandrel/architectures/DRUNet/__arch/network_unet.py,sha256=bbG4zyJnyPRXZgZ0yzkkw_Kxxi4HaUx4wtf_K_yauTI,3641
spandrel/architectures/DRUNet/__init__.py,sha256=-JngKk2niHPzgoCcFHn9Bkr_pUHdKeybNdLiX5EO3P4,3202
spandrel/architectures/DRUNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DnCNN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/DnCNN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/DnCNN/__arch/__pycache__/network_dncnn.cpython-311.pyc,,
spandrel/architectures/DnCNN/__arch/network_dncnn.py,sha256=7PUtZa-5Ggz_ZjZ-d5nUcK-vWlGXQD3a7IH1B6kGi24,2900
spandrel/architectures/DnCNN/__init__.py,sha256=rN-O-b3bSdaLlNg_en7fWr_KIENOKqvy4N2Y_INepg8,3886
spandrel/architectures/DnCNN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/ESRGAN/__arch/RRDB.py,sha256=Mj8juvQCK3lIZhDantqB82nHH9zO02ORU952o85-5fU,4526
spandrel/architectures/ESRGAN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/ESRGAN/__arch/__pycache__/RRDB.cpython-311.pyc,,
spandrel/architectures/ESRGAN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/ESRGAN/__init__.py,sha256=7TRzMmwzXhrI6rgmnj-xDDfxmBLv-kMcWtChinqb5wk,7636
spandrel/architectures/ESRGAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/FBCNN/__arch/FBCNN.py,sha256=iOLnBdZiDlp4WXLy42IRq3Z-uvSCmcQRRcqDYLJgyVs,15982
spandrel/architectures/FBCNN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/FBCNN/__arch/__pycache__/FBCNN.cpython-311.pyc,,
spandrel/architectures/FBCNN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/FBCNN/__init__.py,sha256=YNzciiUnh9QPws0x6FVGgHA4PyDyPcAI8vjQu2Is1uo,2615
spandrel/architectures/FBCNN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/FFTformer/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/FFTformer/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/FFTformer/__arch/__pycache__/fftformer_arch.cpython-311.pyc,,
spandrel/architectures/FFTformer/__arch/fftformer_arch.py,sha256=JLEfcbFyL2kqtq7Bd-EJy261lK0co4ltpLht5Lg3EOE,11851
spandrel/architectures/FFTformer/__init__.py,sha256=D3y_vGHHVMRtAFwCncCL3yiaFIBvvvkpdeWvsqwcrtE,3632
spandrel/architectures/FFTformer/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/GFPGAN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/arcface_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/fused_act.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/gfpgan_bilinear_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/gfpganv1_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/gfpganv1_clean_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/stylegan2_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/stylegan2_bilinear_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/stylegan2_clean_arch.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/__pycache__/upfirdn2d.cpython-311.pyc,,
spandrel/architectures/GFPGAN/__arch/arcface_arch.py,sha256=1AwLakZlHBTcsk60RgZ42M5NY2P9_O5LShKyUp5Jj2c,8166
spandrel/architectures/GFPGAN/__arch/fused_act.py,sha256=m1VAq1kWxAEXAjeW-debjB7NdI2dbRh73Dk3OYkl0Bk,2299
spandrel/architectures/GFPGAN/__arch/gfpgan_bilinear_arch.py,sha256=btGvnRcmA4EPqsm3xhkkSI2o9IwWL17lrMUy1CQODJM,14296
spandrel/architectures/GFPGAN/__arch/gfpganv1_arch.py,sha256=b_JRK2W7yMeB9IrBMEsVrdDaplrD-A_voC0YtQk_qCo,19614
spandrel/architectures/GFPGAN/__arch/gfpganv1_clean_arch.py,sha256=e4emjgGYyG_0612CvM8jWBU5m7Yq3hhWrhXj7ujGC4A,13688
spandrel/architectures/GFPGAN/__arch/stylegan2_arch.py,sha256=WLKPj3wMCsE-ZqrDCZQgiID9-mnZ9T-UTNovmIPbEXo,28263
spandrel/architectures/GFPGAN/__arch/stylegan2_bilinear_arch.py,sha256=VVztMx2F7RQf_nLt4lnPwJjjmuqgOggB8kiqRh2cyVY,23081
spandrel/architectures/GFPGAN/__arch/stylegan2_clean_arch.py,sha256=zgy_JYh_b85HDqkRJKdkIGZE6f_78B9O-exGA2wyGHk,16045
spandrel/architectures/GFPGAN/__arch/upfirdn2d.py,sha256=OIs2L8svz8szh7wrjxN_KgRU-0VChvXGWoBNqJONzs0,5589
spandrel/architectures/GFPGAN/__init__.py,sha256=SrCFWKcQGjfxjztV4xGaDwLdrFsE5ygfrl5HVTMWL50,1801
spandrel/architectures/GFPGAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/GRL/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/config.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/grl.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/mixed_attn_block.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/mixed_attn_block_efficient.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/ops.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/swin_v1_block.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/__pycache__/upsample.cpython-311.pyc,,
spandrel/architectures/GRL/__arch/config.py,sha256=YiMgmGspwWj4_EY9rgU4m6GMWky9Ec12jP8j3i2kKOk,1225
spandrel/architectures/GRL/__arch/grl.py,sha256=SwyhDsbB8yQi5j63GPMxAREl-LxTeQajHx9gb8qt1_M,25468
spandrel/architectures/GRL/__arch/mixed_attn_block.py,sha256=hyOQk5Lx_rwxQsjBypBng80Nrq2Kv5ECemWo2D9CiUA,7043
spandrel/architectures/GRL/__arch/mixed_attn_block_efficient.py,sha256=j-NCLxWdaAaml4R58um8TQR4MlsUdiFldoyE_YygntM,20088
spandrel/architectures/GRL/__arch/ops.py,sha256=pPIPQZASlQj8DaDOEc2C4hZ0nEW1MZQzgEHmKdxL77Y,8423
spandrel/architectures/GRL/__arch/swin_v1_block.py,sha256=Yk4eWtddA9PHFiSe2A9jTNefte5SkPAObEObjP083ro,2021
spandrel/architectures/GRL/__arch/upsample.py,sha256=2N11fI6-_x_btrB7vm9t4IYc0y8misZFxXB5js6wlF4,1575
spandrel/architectures/GRL/__init__.py,sha256=4A7ECfT2kxKt4B5QVQPR7k-UKEwSWJH3xbQ4ERhKqKs,13418
spandrel/architectures/GRL/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/HAT/__arch/HAT.py,sha256=xFLqnUfgLtnIIgFWMJwX3NIWrNrp7Rt0VEY23uzV2oU,39478
spandrel/architectures/HAT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/HAT/__arch/__pycache__/HAT.cpython-311.pyc,,
spandrel/architectures/HAT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/HAT/__init__.py,sha256=0USeXfy7V63W_sX5FX2upeZYHrO1YXd7sfI7UMar5xk,8212
spandrel/architectures/HAT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/HVI_transform.py,sha256=obEjoLpjExyfoo2CqSHx05614G9gVsciNJdjQv-l8g8,3505
spandrel/architectures/HVICIDNet/__arch/LCA.py,sha256=WmgNg5xqP5MuE8TOWngCZa0Z5yQ5-EB45i59a73hnuk,3860
spandrel/architectures/HVICIDNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/HVICIDNet/__arch/__pycache__/HVI_transform.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/__pycache__/LCA.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/__pycache__/cidnet.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/__pycache__/transformer_utils.cpython-311.pyc,,
spandrel/architectures/HVICIDNet/__arch/cidnet.py,sha256=iaxoG1dLFgHisf369L5yWxWPBbhag4R4KxryeMr6FiY,4187
spandrel/architectures/HVICIDNet/__arch/transformer_utils.py,sha256=t6_qPnys-v1PILffZH2ACOfvVa9NJoWmDUJHTSVrERk,2827
spandrel/architectures/HVICIDNet/__init__.py,sha256=SvswIIqvV6BQLherh_R_YylBd7_CbIs_22z0X-Qicro,3160
spandrel/architectures/HVICIDNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/IPT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/IPT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/IPT/__arch/__pycache__/common.cpython-311.pyc,,
spandrel/architectures/IPT/__arch/__pycache__/ipt.cpython-311.pyc,,
spandrel/architectures/IPT/__arch/__pycache__/model.cpython-311.pyc,,
spandrel/architectures/IPT/__arch/common.py,sha256=SoNAFE7LcUYG2aspWmyy9lUE3ETL1-Lhfm9HOcBXs_Q,2645
spandrel/architectures/IPT/__arch/ipt.py,sha256=mEJlEm-9Tp1yIHtcrU55c9zN45DfxwCDbZsyLPMCkbI,11890
spandrel/architectures/IPT/__arch/model.py,sha256=AxwIG_hXTe7qqPvwGvsq8BOsL92MWWzepMihx68au0A,9255
spandrel/architectures/IPT/__init__.py,sha256=zPyRfF4MK90poCmPlOxu4uxrRLOon5wEYCsJeksC03M,5397
spandrel/architectures/IPT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/KBNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/KBNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/KBNet/__arch/__pycache__/kb_utils.cpython-311.pyc,,
spandrel/architectures/KBNet/__arch/__pycache__/kbnet_l.cpython-311.pyc,,
spandrel/architectures/KBNet/__arch/__pycache__/kbnet_s.cpython-311.pyc,,
spandrel/architectures/KBNet/__arch/kb_utils.py,sha256=cKU-a0Qn7dW5hHsGhCvQxUaEvfqipyTjmcohej1T9LY,4410
spandrel/architectures/KBNet/__arch/kbnet_l.py,sha256=z32RY3n2sPNrGjctwt9BhC9YiYs6TzgVcpTm4guppXg,11650
spandrel/architectures/KBNet/__arch/kbnet_s.py,sha256=WKt8rgaEdWOiJnTKXkzrbmC_UhMY79e9_bYOSN-FIIU,8368
spandrel/architectures/KBNet/__init__.py,sha256=n7f1mvUvqIGFd5cK-A-LNMMq2wz62rfXesggsoDRSNk,5573
spandrel/architectures/KBNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/LaMa/__arch/LaMa.py,sha256=nuOlPFZAvPRbqU8BHw2jB1q64SJofIy_4qWFhuqVMOg,20905
spandrel/architectures/LaMa/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/LaMa/__arch/__pycache__/LaMa.cpython-311.pyc,,
spandrel/architectures/LaMa/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/LaMa/__init__.py,sha256=UNl0gWbZbc16OvVbEwvgmoGBgo9qGWfEOOChM3PetLs,1538
spandrel/architectures/LaMa/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MMRealSR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/MMRealSR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MMRealSR/__arch/__pycache__/mmrealsr_arch.cpython-311.pyc,,
spandrel/architectures/MMRealSR/__arch/mmrealsr_arch.py,sha256=AZCspKqD6jKo0dLYHQwVjnriy4E3eI0uPgGZJzYm6-s,23388
spandrel/architectures/MMRealSR/__init__.py,sha256=OD9P5h3NYnlxQxRPGz91e9CRXUUp_1yqmUhbntf6exA,6268
spandrel/architectures/MMRealSR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MixDehazeNet/__arch/MixDehazeNet.py,sha256=1X2El0WYdJjmmK5Aoi4qiFPxp7v4xYzxLLo43j_hgVo,8456
spandrel/architectures/MixDehazeNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/MixDehazeNet/__arch/__pycache__/MixDehazeNet.cpython-311.pyc,,
spandrel/architectures/MixDehazeNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MixDehazeNet/__init__.py,sha256=AX3WyXalKtb7aTUW8daA8eKZDbbjNKWsDouniObGxVI,4290
spandrel/architectures/MixDehazeNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MoESR/__arch/MoESR.py,sha256=HCzAGG2koHm9aBjKWU0Km2YfnbVtwmTkIiEDNvJf1S0,9405
spandrel/architectures/MoESR/__arch/__pycache__/MoESR.cpython-311.pyc,,
spandrel/architectures/MoESR/__init__.py,sha256=gx8DhgaVzh3tFYQRl3ijFFfokSz-oE0HqRXoECVBhBQ,3141
spandrel/architectures/MoESR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MoSR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/MoSR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/MoSR/__arch/__pycache__/mosr_arch.cpython-311.pyc,,
spandrel/architectures/MoSR/__arch/mosr_arch.py,sha256=bBSmv4XWTSwcpoCEAsq-Nvwj6lreKHNAf8agrSlwUtI,5914
spandrel/architectures/MoSR/__init__.py,sha256=xMupHgAitvGaMAmRUaFLn959IxHwAncxdS8uJysWfsQ,3078
spandrel/architectures/MoSR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/NAFNet/__arch/NAFNet_arch.py,sha256=Fo6QUGqJGXxRqvE2GEEjCWWTGPNDSESUgr_9gYobTXg,6013
spandrel/architectures/NAFNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/NAFNet/__arch/__pycache__/NAFNet_arch.cpython-311.pyc,,
spandrel/architectures/NAFNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/NAFNet/__arch/__pycache__/arch_util.cpython-311.pyc,,
spandrel/architectures/NAFNet/__arch/arch_util.py,sha256=uqck1vLPrEuuSVpA0v-wy54S_zFWMP3i1awCkgWZl2Q,1477
spandrel/architectures/NAFNet/__init__.py,sha256=1BveucMPLaVu5E67amNxlivdr2TMPUz9r289YKuGZo4,2528
spandrel/architectures/NAFNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/ChannelAttention.py,sha256=DXvuY-ahJ58ty52CAXux8ctvk2DhcXbnbwdzevnGSso,3106
spandrel/architectures/OmniSR/__arch/OSA.py,sha256=YzjtWUQFrs1rHsco_dKymhK24nvE-b8GZj-Wckw3vsc,15054
spandrel/architectures/OmniSR/__arch/OSAG.py,sha256=s8SFztqA3dFyw7V-iXJuiNjvzgNZ-VVpLqdNIVhjD60,1714
spandrel/architectures/OmniSR/__arch/OmniSR.py,sha256=f4aQithNiU2qHhesYvedSrpRY96gZYfqvuEjeVdFjq4,2677
spandrel/architectures/OmniSR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/OmniSR/__arch/__pycache__/ChannelAttention.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/OSA.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/OSAG.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/OmniSR.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/esa.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/layernorm.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/__pycache__/pixelshuffle.cpython-311.pyc,,
spandrel/architectures/OmniSR/__arch/esa.py,sha256=zzEIFOzQ9S7E_D1XiSmk0sLJSOYi0WHTUlOc0RdDqEc,8315
spandrel/architectures/OmniSR/__arch/layernorm.py,sha256=bAllwJzSrqHlCXt9cftrXjj0hfuUEjp5izXUphbqWvo,2275
spandrel/architectures/OmniSR/__arch/pixelshuffle.py,sha256=jIjiKer0lcfD9flZ65Zy98rLDJkdJiOW9mmpZ7SvA9U,850
spandrel/architectures/OmniSR/__init__.py,sha256=oc1HpZ5wJEbhO-bfbhHOPeHRTOMBworxbnxRBtwVwd0,2955
spandrel/architectures/OmniSR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/PLKSR/__arch/PLKSR.py,sha256=Y4-cs7MXb7WU2PRvgBsx6geaBMg7O6ISo-YChhSMPQs,10610
spandrel/architectures/PLKSR/__arch/RealPLKSR.py,sha256=5CGw9f-_qkUgCa1a0oegUvgF3Na-BuTZ6x21tBy4TAU,5282
spandrel/architectures/PLKSR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/PLKSR/__arch/__pycache__/PLKSR.cpython-311.pyc,,
spandrel/architectures/PLKSR/__arch/__pycache__/RealPLKSR.cpython-311.pyc,,
spandrel/architectures/PLKSR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/PLKSR/__init__.py,sha256=tAdXBDe3Hf-oXlNPstz8XZvLIG38u0Jg3bis5ABXNwI,6354
spandrel/architectures/PLKSR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RCAN/__arch/__pycache__/rcan_arch.cpython-311.pyc,,
spandrel/architectures/RCAN/__arch/rcan_arch.py,sha256=6Ly8Y4OVx2_maiLRcS18PcQ-eFozRGEp97XJog4dRgo,9780
spandrel/architectures/RCAN/__init__.py,sha256=n64OrYTYNFM38yEhMnGTDn6HYU1IfeDBLI7jCJeWk9w,2864
spandrel/architectures/RCAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RGT/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/RGT/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RGT/__arch/__pycache__/rgt.cpython-311.pyc,,
spandrel/architectures/RGT/__arch/rgt.py,sha256=D_asEdlFyPQHYCvudAjl4HXndjyZYODocbpZg7T6DO0,29908
spandrel/architectures/RGT/__init__.py,sha256=YVRnZRwIzwcH4zh1iiSQ9LzLYBpXQoszSsAaO_5-E_s,5958
spandrel/architectures/RGT/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RealCUGAN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/RealCUGAN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RealCUGAN/__arch/__pycache__/upcunet_v3.cpython-311.pyc,,
spandrel/architectures/RealCUGAN/__arch/upcunet_v3.py,sha256=II0fe-QJ7vzmNdSyNEdu7nQe1y2c7sFg4cE_k77ZoiM,14848
spandrel/architectures/RealCUGAN/__init__.py,sha256=haP4P2LF6557snRj5L5Up2199ZYVpj94jnktzdNuD4Q,3997
spandrel/architectures/RealCUGAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RestoreFormer/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/RestoreFormer/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RestoreFormer/__arch/__pycache__/restoreformer_arch.cpython-311.pyc,,
spandrel/architectures/RestoreFormer/__arch/restoreformer_arch.py,sha256=Pnfft8v1vjOhbcxWsLeKaWC__mLXw1E7OeKA06_MMA8,23802
spandrel/architectures/RestoreFormer/__init__.py,sha256=drlqZwNQybfBqzYPhjTCsOtzh0wyCPm2aVeRhT8YKTo,3463
spandrel/architectures/RestoreFormer/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RetinexFormer/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/RetinexFormer/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/RetinexFormer/__arch/__pycache__/retinexformer_arch.cpython-311.pyc,,
spandrel/architectures/RetinexFormer/__arch/retinexformer_arch.py,sha256=wPMg2zHCu8sA4UbMPWRwmCrWF7GG7nBNRKUIX3RGAuE,10681
spandrel/architectures/RetinexFormer/__init__.py,sha256=3yfhL7xmf8h1uYgQp-MIlaSyt36RhB3ody0wMwAnXk8,4002
spandrel/architectures/RetinexFormer/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SAFMN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SAFMN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SAFMN/__arch/__pycache__/safmn.cpython-311.pyc,,
spandrel/architectures/SAFMN/__arch/safmn.py,sha256=-uAEHYpD05IT2slIoojNUGEHYd-EIHAO_uC2L4Z0m7A,5022
spandrel/architectures/SAFMN/__init__.py,sha256=xHaNyiayYtPkl0UEGInzkBRZQ8xJx3EvVF_CV5AhBks,2137
spandrel/architectures/SAFMN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SAFMNBCIE/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SAFMNBCIE/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SAFMNBCIE/__arch/__pycache__/safmn_bcie.cpython-311.pyc,,
spandrel/architectures/SAFMNBCIE/__arch/safmn_bcie.py,sha256=nawWRlCC8yZYz_9lVw3NN1NnykVaEpw3TX_DAeWTGgw,4171
spandrel/architectures/SAFMNBCIE/__init__.py,sha256=qfEe9KzrQ_2y0JUn_QC2vE9HCnRiZ3pmSHzM-gFcNf0,2651
spandrel/architectures/SAFMNBCIE/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SCUNet/__arch/SCUNet.py,sha256=BKBMWuHWPSyHsu-M72uguMCbfCmpHlQits11ZBMbMmY,13797
spandrel/architectures/SCUNet/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SCUNet/__arch/__pycache__/SCUNet.cpython-311.pyc,,
spandrel/architectures/SCUNet/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SCUNet/__init__.py,sha256=_dueqyQeI2xTMonupEFsdrEICbIaX3m5cFXH9UAzgzY,1943
spandrel/architectures/SCUNet/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SPAN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SPAN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SPAN/__arch/__pycache__/span.cpython-311.pyc,,
spandrel/architectures/SPAN/__arch/span.py,sha256=B0sPbP10dhFvYfQQMBzBLca2JCnMWucZ5Ge7G0LQk7A,9288
spandrel/architectures/SPAN/__init__.py,sha256=0dNo7gxManNBNWdKv7i0VQmLTDvq5GS9RkPC9Lx00TE,2324
spandrel/architectures/SPAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SeemoRe/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SeemoRe/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SeemoRe/__arch/__pycache__/seemore_arch.cpython-311.pyc,,
spandrel/architectures/SeemoRe/__arch/seemore_arch.py,sha256=ta30-pAd3OcnXRr7KTSqWs5PNCBrAZU28THAN2mi3cQ,13739
spandrel/architectures/SeemoRe/__init__.py,sha256=wovurNAg8ZJn9SMcOf1_ZKy4UeGjAmsU101v5XaONAc,5977
spandrel/architectures/SeemoRe/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SwiftSRGAN/__arch/SwiftSRGAN.py,sha256=Bw7EVgIy3HY711mtR--_-_D-TEnxOOBbFFEFWgHepwE,4115
spandrel/architectures/SwiftSRGAN/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SwiftSRGAN/__arch/__pycache__/SwiftSRGAN.cpython-311.pyc,,
spandrel/architectures/SwiftSRGAN/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SwiftSRGAN/__init__.py,sha256=dJhHAdNLSssRIRBzyUVPqd6ElAJBMeTmeW6Yjq_Z7gg,1780
spandrel/architectures/SwiftSRGAN/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Swin2SR/__arch/Swin2SR.py,sha256=_bvdzYOO47gR6IzAzUnRoaMWzVzIWZQrHe_Nom19mzo,46547
spandrel/architectures/Swin2SR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/Swin2SR/__arch/__pycache__/Swin2SR.cpython-311.pyc,,
spandrel/architectures/Swin2SR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Swin2SR/__init__.py,sha256=TJ9OzvybAqiLVJFBW6pQ7IV4oMPr_Q6a6M6xFYfrm1U,6592
spandrel/architectures/Swin2SR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SwinIR/__arch/SwinIR.py,sha256=5R2vmO9GYMjqNCHok1Ivu-tXoeCy8OOJjtCl5b_YAWU,38917
spandrel/architectures/SwinIR/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/SwinIR/__arch/__pycache__/SwinIR.cpython-311.pyc,,
spandrel/architectures/SwinIR/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/SwinIR/__init__.py,sha256=E8Exol6JygkDeR8efFL9zD3uHFtlmEOtGbL8fb8QKJ4,6106
spandrel/architectures/SwinIR/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Uformer/__arch/Uformer.py,sha256=qrM0naYnDnnus_CV0QmP8F8XulBET7n6gyTb3jf57dc,54675
spandrel/architectures/Uformer/__arch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/Uformer/__arch/__pycache__/Uformer.cpython-311.pyc,,
spandrel/architectures/Uformer/__arch/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/Uformer/__init__.py,sha256=844kEKkFsZ2EK0hKh02zXXKB1G4y1w0XbWQOTATEXMY,5768
spandrel/architectures/Uformer/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
spandrel/architectures/__arch_helpers/__pycache__/__init__.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/block.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/dpir_basic_block.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/dysample.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/__pycache__/padding.cpython-311.pyc,,
spandrel/architectures/__arch_helpers/block.py,sha256=4NwY4AjNWKlBQovbzKzlEBJMwKZfFoBvK3mu6CoAvo8,13495
spandrel/architectures/__arch_helpers/dpir_basic_block.py,sha256=LG-XW8KLKU1yWElyzn2KGWXc03FfTF68LUPenasznao,10601
spandrel/architectures/__arch_helpers/dysample.py,sha256=B4JR_puVZfLT5bnSmcTkG2ostd9tOp74OmrKxxJIztk,3056
spandrel/architectures/__arch_helpers/padding.py,sha256=azwwAAZ1fPaK37IG9TEQDbNcwOZXoa1xeGapV5oUI0k,770
spandrel/architectures/__init__.py,sha256=jXDeTC64KgfzfEgxDDD-E9j8tU01r4a1ZNp5WWPR7i0,143
spandrel/architectures/__pycache__/__init__.cpython-311.pyc,,
spandrel/util/__init__.py,sha256=I9SjUVV2M9EsfuQzIpt6CR6h3Iz5yr1KOdsU0pV8vqg,6720
spandrel/util/__pycache__/__init__.cpython-311.pyc,,
spandrel/util/timm/__drop.py,sha256=qslYFOof8yBrjVBAEoSAocWOYDIZYJChEpFvxeqZr18,7368
spandrel/util/timm/__helpers.py,sha256=478WrzOnSDpaad97neragwGczLBIO3C6JfCEkcF1dik,788
spandrel/util/timm/__init__.py,sha256=JIG2mLkdHSaCFYU4ucmjyhy5zI2tuIhHSUheam9TnOc,314
spandrel/util/timm/__pycache__/__drop.cpython-311.pyc,,
spandrel/util/timm/__pycache__/__helpers.cpython-311.pyc,,
spandrel/util/timm/__pycache__/__init__.cpython-311.pyc,,
spandrel/util/timm/__pycache__/__weight_init.cpython-311.pyc,,
spandrel/util/timm/__weight_init.py,sha256=GrJiLolNRvps3py643cLdVB86keFoOT55bA5dkPtITI,5088
