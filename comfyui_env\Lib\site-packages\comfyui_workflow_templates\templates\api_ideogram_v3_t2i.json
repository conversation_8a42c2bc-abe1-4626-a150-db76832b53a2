{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 64, "last_link_id": 30, "nodes": [{"id": 58, "type": "IdeogramV3", "pos": [1672.5926513671875, 1403.2564697265625], "size": [398.7581481933594, 333.124755859375], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 29}, {"name": "mask", "shape": 7, "type": "MASK", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28]}], "properties": {"Node name for S&R": "IdeogramV3"}, "widgets_values": ["Hyperrealistic style, solitary astronaut standing on barren red planet, NASA white spacesuit with red stripes, reflective helmet, orange-red rocky terrain, distant mountain silhouettes, low-saturation dark brown sky, floating dust particles, fine-grained sand texture, harsh side lighting, long shadows, subtle light reflections, vast interstellar silence, panoramic composition, cinematic color grading, 8K ultra-high definition rendering, somber atmospheric mood, profound isolation, existentialism, completely still environment, no signs of life, high contrast, sense of abandonment, low-angle perspective", "1:1", "Auto", "AUTO", 876565950, "randomize", 1, "QUALITY"]}, {"id": 59, "type": "SaveImage", "pos": [2112.443359375, 1401.7833251953125], "size": [270, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 28}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 60, "type": "LoadImage", "pos": [1300.7972412109375, 1406.413330078125], "size": [342.5999755859375, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29]}, {"name": "MASK", "type": "MASK", "links": [30]}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 64, "type": "<PERSON>downNote", "pos": [968.7691040039062, 1703.3802490234375], "size": [310.3110656738281, 282.41473388671875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 63, "type": "<PERSON>downNote", "pos": [960.*************, 1402.*************], "size": [320.********, 243.**************], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About Ideogram V3", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/ideogram/ideogram-v3) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/ideogram/ideogram-v3) \n\n There are two modes for the Ideogram V3 node.\n- Text - to - Image mode (when neither **image** nor **mask** is provided)\n- Image Editing mode (when both **image** and **mask** are provided)\n\nNote\n- **image** and **mask** are optional.\n- **image** and **mask** must be provided simultaneously.\n\n\n---\n\n\nIdogram V3节点有两种模式。\n- 文生图模式（当未提供**图像**和**遮罩**时）\n- 图像编辑模式（当同时提供**图像**和**遮罩**时）\n\n注意\n- **图像**和**遮罩**为可选参数。\n- **图像**和**遮罩**必须同时提供。"], "color": "#432", "bgcolor": "#653"}], "links": [[28, 58, 0, 59, 0, "IMAGE"], [29, 60, 0, 58, 0, "IMAGE"], [30, 60, 1, 58, 1, "MASK"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9090909090909095, "offset": [-771.2592210354912, -1120.087809788962]}, "frontendVersion": "1.18.5"}, "version": 0.4}