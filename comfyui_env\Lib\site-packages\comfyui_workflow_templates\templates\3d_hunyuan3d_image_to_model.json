{"id": "81ccfe3e-3540-49e3-88ae-4fde173b8c87", "revision": 0, "last_node_id": 90, "last_link_id": 179, "nodes": [{"id": 80, "type": "Hunyuan3Dv2Conditioning", "pos": [350, 210], "size": [310, 50], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 164}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [165]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [166]}], "properties": {"Node name for S&R": "Hunyuan3Dv2Conditioning"}, "widgets_values": []}, {"id": 54, "type": "ImageOnlyCheckpointLoader", "pos": [-391.26416015625, 66.70899200439453], "size": [369.6000061035156, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [155]}, {"label": "CLIP_VISION", "name": "CLIP_VISION", "type": "CLIP_VISION", "slot_index": 1, "links": [111]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": [158]}], "properties": {"Node name for S&R": "ImageOnlyCheckpointLoader", "models": [{"name": "hunyuan3d-dit-v2.safetensors", "url": "https://huggingface.co/tencent/Hunyuan3D-2/resolve/main/hunyuan3d-dit-v2-0/model.fp16.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["hunyuan3d-dit-v2.safetensors"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [350, 310], "size": [315, 262], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 156}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 165}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 166}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 143}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [130]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [188965118078112, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 56, "type": "LoadImage", "pos": [-390, 210], "size": [370, 340], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [145]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["front.jpg", "image"]}, {"id": 70, "type": "ModelSamplingAuraFlow", "pos": [30, 70], "size": [260, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 155}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [156]}], "properties": {"Node name for S&R": "ModelSamplingAuraFlow"}, "widgets_values": [1.0000000000000002]}, {"id": 51, "type": "CLIPVisionEncode", "pos": [28.491897583007812, 203.88284301757812], "size": [260, 80], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 111}, {"label": "image", "name": "image", "type": "IMAGE", "link": 145}], "outputs": [{"label": "CLIP_VISION_OUTPUT", "name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "slot_index": 0, "links": [164]}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["none"]}, {"id": 66, "type": "EmptyLatentHunyuan3Dv2", "pos": [30, 330], "size": [260.2345886230469, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [143]}], "properties": {"Node name for S&R": "EmptyLatentHunyuan3Dv2"}, "widgets_values": [3072, 1]}, {"id": 77, "type": "<PERSON>downNote", "pos": [-790, 70], "size": [380, 180], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tutorials](https://docs.comfy.org/tutorials/3d/hunyuan3D-2) | [教程](https://docs.comfy.org/zh-CN/tutorials/3d/hunyuan3D-2)\n\n## Model\nDownload [hunyuan3d-dit-v2/model.fp16.safetensors](https://huggingface.co/tencent/Hunyuan3D-2/blob/main/hunyuan3d-dit-v2-0/model.fp16.safetensors) and rename to **hunyuan3d-dit-v2.safetensorss**\n\nPut it in the **ComfyUI/models/checkpoints** directory"], "color": "#432", "bgcolor": "#653"}, {"id": 89, "type": "<PERSON>downNote", "pos": [350, 620], "size": [310, 130], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "CFG setting", "properties": {}, "widgets_values": ["Set the cfg value between 4 and 8. Thanks for the feedback from Man<PERSON>old. \n\n---\n\ncfg 设置在 4-8 之间，感谢来自 Manifold 的反馈"], "color": "#432", "bgcolor": "#653"}, {"id": 82, "type": "SaveGLB", "pos": [710, 310], "size": [532.7781982421875, 481.68194580078125], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "mesh", "name": "mesh", "type": "MESH", "link": 168}], "outputs": [], "properties": {"Node name for S&R": "SaveGLB", "Camera Info": {"position": {"x": 5.750074875413741, "y": 8.723870543067367, "z": 13.339892208367981}, "target": {"x": 0, "y": 2.5, "z": 0}, "zoom": 1, "cameraType": "perspective"}, "Background Color": "#141414"}, "widgets_values": ["mesh/ComfyUI", ""]}, {"id": 81, "type": "VoxelToMesh", "pos": [710, 170], "size": [270, 82], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "voxel", "type": "VOXEL", "link": 167}], "outputs": [{"name": "MESH", "type": "MESH", "links": [168]}], "properties": {"Node name for S&R": "VoxelToMesh"}, "widgets_values": ["surface net", 0.6]}, {"id": 61, "type": "VAEDecodeHunyuan3D", "pos": [710, 0], "size": [315, 102], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 130}, {"label": "vae", "name": "vae", "type": "VAE", "link": 158}], "outputs": [{"label": "VOXEL", "name": "VOXEL", "type": "VOXEL", "slot_index": 0, "links": [132, 167]}], "properties": {"Node name for S&R": "VAEDecodeHunyuan3D"}, "widgets_values": [8000, 256]}, {"id": 62, "type": "VoxelToMeshBasic", "pos": [1270, 180], "size": [210, 58], "flags": {}, "order": 11, "mode": 4, "inputs": [{"label": "voxel", "name": "voxel", "type": "VOXEL", "link": 132}], "outputs": [{"label": "MESH", "name": "MESH", "type": "MESH", "slot_index": 0, "links": [146]}], "properties": {"Node name for S&R": "VoxelToMeshBasic"}, "widgets_values": [0.6000000000000001]}, {"id": 67, "type": "SaveGLB", "pos": [1270, 310], "size": [532.7781982421875, 481.68194580078125], "flags": {}, "order": 13, "mode": 4, "inputs": [{"label": "mesh", "name": "mesh", "type": "MESH", "link": 146}], "outputs": [], "properties": {"Node name for S&R": "SaveGLB", "Camera Info": {"position": {"x": 14.57357202409431, "y": 4.578798810649397, "z": 1.4024088161106378}, "target": {"x": 0, "y": 2.5, "z": 0}, "zoom": 1, "cameraType": "perspective"}, "Background Color": "#141414"}, "widgets_values": ["mesh/ComfyUI", ""]}, {"id": 90, "type": "<PERSON>downNote", "pos": [-790, 300], "size": [380, 110], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "About Input Image", "properties": {}, "widgets_values": ["It is recommended to use images with a ratio close to 1:1\n\n---\n\n建议使用接近 1:1 比例的图片"], "color": "#432", "bgcolor": "#653"}], "links": [[111, 54, 1, 51, 0, "CLIP_VISION"], [130, 3, 0, 61, 0, "LATENT"], [132, 61, 0, 62, 0, "VOXEL"], [143, 66, 0, 3, 3, "LATENT"], [145, 56, 0, 51, 1, "IMAGE"], [146, 62, 0, 67, 0, "MESH"], [155, 54, 0, 70, 0, "MODEL"], [156, 70, 0, 3, 0, "MODEL"], [158, 54, 2, 61, 1, "VAE"], [164, 51, 0, 80, 0, "CLIP_VISION_OUTPUT"], [165, 80, 0, 3, 1, "CONDITIONING"], [166, 80, 1, 3, 2, "CONDITIONING"], [167, 61, 0, 81, 0, "VOXEL"], [168, 81, 0, 82, 0, "MESH"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7646251029937551, "offset": [995.2259637744157, 209.97467664783971]}, "frontendVersion": "1.20.4", "node_versions": {"comfy-core": "0.3.35"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}