{"last_node_id": 38, "last_link_id": 116, "nodes": [{"id": 11, "type": "DualCLIPLoader", "pos": [48, 288], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "shape": 3, "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 17, "type": "BasicScheduler", "pos": [480, 1008], "size": [315, 106], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 55, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "shape": 3, "links": [20]}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 16, "type": "KSamplerSelect", "pos": [480, 912], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "shape": 3, "links": [19]}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 26, "type": "FluxGuidance", "pos": [480, 144], "size": [317.4, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "shape": 3, "links": [42], "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5], "color": "#233", "bgcolor": "#355"}, {"id": 22, "type": "BasicGuider", "pos": [576, 48], "size": [222.35, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 54, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 42, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "shape": 3, "links": [30], "slot_index": 0}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 13, "type": "SamplerCustomAdvanced", "pos": [864, 192], "size": [272.36, 124.54], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 37, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 30, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 19, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 20, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 116, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "shape": 3, "links": [24], "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "shape": 3, "links": null}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 25, "type": "RandomNoise", "pos": [480, 768], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "shape": 3, "links": [37]}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [219670278747233, "randomize"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 8, "type": "VAEDecode", "pos": [866, 367], "size": [210, 46], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 24}, {"name": "vae", "type": "VAE", "link": 12}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 6, "type": "CLIPTextEncode", "pos": [384, 240], "size": [422.85, 164.31], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 10}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [41], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cute anime girl with massive fluffy fennec ears and a big fluffy tail blonde messy long hair blue eyes wearing a maid outfit with a long black gold leaf pattern dress and a white apron mouth open holding a fancy black forest cake with candles on top in the kitchen of an old dark Victorian mansion lit by candlelight with a bright window to the foggy forest and very expensive stuff everywhere"], "color": "#232", "bgcolor": "#353"}, {"id": 30, "type": "ModelSamplingFlux", "pos": [480, 1152], "size": [315, 130], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 56, "slot_index": 0}, {"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 115, "slot_index": 1}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 114, "slot_index": 2}], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [54, 55], "slot_index": 0}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, 1024, 1024]}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [480, 624], "size": [315, 106], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 112}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 113}], "outputs": [{"name": "LATENT", "type": "LATENT", "shape": 3, "links": [116], "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 34, "type": "PrimitiveNode", "pos": [432, 480], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "width"}, "links": [112, 115], "slot_index": 0}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 35, "type": "PrimitiveNode", "pos": [672, 480], "size": [210, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "widget": {"name": "height"}, "links": [113, 114], "slot_index": 0}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1024, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 12, "type": "UNETLoader", "pos": [48, 144], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "shape": 3, "links": [56], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 9, "type": "SaveImage", "pos": [1155, 196], "size": [985.3, 1060.38], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 37, "type": "Note", "pos": [480, 1344], "size": [315.0, 117.98], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["The reference sampling implementation auto adjusts the shift value based on the resolution, if you don't want this you can just bypass (CTRL-B) this ModelSamplingFlux node.\n"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "VAELoader", "pos": [48, 432], "size": [311.82, 60.43], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "shape": 3, "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 28, "type": "Note", "pos": [48, 576], "size": [336, 288], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["If you get an error in any of the nodes above make sure the files are in the correct directories.\n\nSee the top of the examples page for the links : https://comfyanonymous.github.io/ComfyUI_examples/flux/\n\nflux1-dev.safetensors goes in: ComfyUI/models/unet/\n\nt5xxl_fp16.safetensors and clip_l.safetensors go in: ComfyUI/models/clip/\n\nae.safetensors goes in: ComfyUI/models/vae/\n\n\nTip: You can set the weight_dtype above to one of the fp8 types if you have memory issues."], "color": "#432", "bgcolor": "#653"}, {"id": 38, "type": "<PERSON>downNote", "pos": [45, 930], "size": [225, 60], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/flux/#flux-dev-1)"], "color": "#432", "bgcolor": "#653"}], "links": [[9, 8, 0, 9, 0, "IMAGE"], [10, 11, 0, 6, 0, "CLIP"], [12, 10, 0, 8, 1, "VAE"], [19, 16, 0, 13, 2, "SAMPLER"], [20, 17, 0, 13, 3, "SIGMAS"], [24, 13, 0, 8, 0, "LATENT"], [30, 22, 0, 13, 1, "GUIDER"], [37, 25, 0, 13, 0, "NOISE"], [41, 6, 0, 26, 0, "CONDITIONING"], [42, 26, 0, 22, 1, "CONDITIONING"], [54, 30, 0, 22, 0, "MODEL"], [55, 30, 0, 17, 0, "MODEL"], [56, 12, 0, 30, 0, "MODEL"], [112, 34, 0, 27, 0, "INT"], [113, 35, 0, 27, 1, "INT"], [114, 35, 0, 30, 2, "INT"], [115, 34, 0, 30, 1, "INT"], [116, 27, 0, 13, 4, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [-0.18, 2.29]}, "groupNodes": {"EmptyLatentImage": {"nodes": [{"type": "PrimitiveNode", "pos": [432, 480], "size": {"0": 210, "1": 82}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [], "widget": {"name": "height"}, "slot_index": 0}], "title": "height", "properties": {"Run widget replace on values": false}, "color": "#323", "bgcolor": "#535", "index": 0}, {"type": "PrimitiveNode", "pos": [672, 480], "size": {"0": 210, "1": 82}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0, "widget": {"name": "width"}}], "title": "width", "properties": {"Run widget replace on values": false}, "color": "#323", "bgcolor": "#535", "index": 1}, {"type": "EmptySD3LatentImage", "pos": [480, 624], "size": {"0": 315, "1": 106}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": null, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": null, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1], "index": 2}], "links": [[1, 0, 2, 0, 34, "INT"], [0, 0, 2, 1, 35, "INT"]], "external": [[0, 0, "INT"], [1, 0, "INT"], [2, 0, "LATENT"]], "config": {"0": {"output": {"0": {"name": "height"}}, "input": {"value": {"visible": true}}}, "1": {"output": {"0": {"name": "width"}}, "input": {"value": {"visible": true}}}, "2": {"input": {"width": {"visible": false}, "height": {"visible": false}}}}}}}, "version": 0.4, "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true", "directory": "text_encoders"}, {"name": "ae.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true", "directory": "vae"}, {"name": "flux1-dev.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors?download=true", "directory": "diffusion_models"}]}