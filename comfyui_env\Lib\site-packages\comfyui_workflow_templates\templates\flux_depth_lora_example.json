{"id": "d2d1faaf-2721-471e-a202-305446a968ee", "revision": 0, "last_node_id": 40, "last_link_id": 76, "nodes": [{"id": 23, "type": "CLIPTextEncode", "pos": [126.71530151367188, 109.84258270263672], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [41]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a photograph of a shark in the sea"], "color": "#232", "bgcolor": "#353"}, {"id": 7, "type": "CLIPTextEncode", "pos": [130.87759399414062, 330.7156066894531], "size": [425.2799987792969, 180.61000061035156], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 63}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [68]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 35, "type": "InstructPixToPixConditioning", "pos": [142.3372039794922, 424.26947021484375], "size": [235.1999969482422, 86], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 67}, {"name": "negative", "type": "CONDITIONING", "link": 68}, {"name": "vae", "type": "VAE", "link": 69}, {"name": "pixels", "type": "IMAGE", "link": 71}], "outputs": [{"name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [64]}, {"name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [65]}, {"name": "latent", "type": "LATENT", "slot_index": 2, "links": [73]}], "properties": {"Node name for S&R": "InstructPixToPixConditioning"}, "widgets_values": []}, {"id": 8, "type": "VAEDecode", "pos": [967.6790161132812, 136.9744415283203], "size": [210, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 60}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [964.4608154296875, 356.83782958984375], "size": [722.4099731445312, 425.7699890136719], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 32, "type": "VAELoader", "pos": [-231.25802612304688, 291.9970703125], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "slot_index": 0, "links": [60, 69]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 34, "type": "DualCLIPLoader", "pos": [-231.25802612304688, 102.66471862792969], "size": [315, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [62, 63]}], "properties": {"Node name for S&R": "DualCLIPLoader", "models": [{"name": "t5xxl_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp16.safetensors?download=true", "directory": "text_encoders"}, {"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors?download=true", "directory": "text_encoders"}]}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 37, "type": "LoraLoaderModelOnly", "pos": [-231.25802612304688, -38.667640686035156], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 74}], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [76]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "models": [{"name": "flux1-depth-dev-lora.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-Depth-dev-lora/resolve/main/flux1-depth-dev-lora.safetensors?download=true", "directory": "loras"}]}, "widgets_values": ["flux1-depth-dev-lora.safetensors", 1], "color": "#322", "bgcolor": "#533"}, {"id": 31, "type": "UNETLoader", "pos": [-231.25802612304688, -180], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [74]}], "properties": {"Node name for S&R": "UNETLoader", "models": [{"name": "flux1-dev.safetensors", "url": "https://huggingface.co/black-forest-labs/FLUX.1-dev/resolve/main/flux1-dev.safetensors?download=true", "directory": "diffusion_models"}]}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 40, "type": "<PERSON>downNote", "pos": [126.40027618408203, -210.72119140625], "size": [225, 88], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/flux/#canny-and-depth)"], "color": "#432", "bgcolor": "#653"}, {"id": 26, "type": "FluxGuidance", "pos": [592.109619140625, 115.34029388427734], "size": [317.3999938964844, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 41}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [67]}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [10]}, {"id": 17, "type": "LoadImage", "pos": [-230.69815063476562, 410.7291259765625], "size": [315, 314], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [71]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["shark_depthmap.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [593.2842407226562, 411.6834716796875], "size": [315, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 76}, {"name": "positive", "type": "CONDITIONING", "link": 64}, {"name": "negative", "type": "CONDITIONING", "link": 65}, {"name": "latent_image", "type": "LATENT", "link": 73}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [91050358797301, "randomize", 20, 1, "euler", "normal", 1]}], "links": [[7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [41, 23, 0, 26, 0, "CONDITIONING"], [60, 32, 0, 8, 1, "VAE"], [62, 34, 0, 23, 0, "CLIP"], [63, 34, 0, 7, 0, "CLIP"], [64, 35, 0, 3, 1, "CONDITIONING"], [65, 35, 1, 3, 2, "CONDITIONING"], [67, 26, 0, 35, 0, "CONDITIONING"], [68, 7, 0, 35, 1, "CONDITIONING"], [69, 32, 0, 35, 2, "VAE"], [71, 17, 0, 35, 3, "IMAGE"], [73, 35, 2, 3, 3, "LATENT"], [74, 31, 0, 37, 0, "MODEL"], [76, 37, 0, 3, 0, "MODEL"]], "groups": [{"id": 1, "title": "Load Model Here", "bounding": [-241.25802612304688, -253.60000610351562, 335, 613.5970458984375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.65, "offset": [573.3175083315878, 364.3458045396041]}, "frontendVersion": "1.19.4"}, "version": 0.4}