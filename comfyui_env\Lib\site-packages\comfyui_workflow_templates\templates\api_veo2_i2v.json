{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 63, "last_link_id": 30, "nodes": [{"id": 62, "type": "SaveVideo", "pos": [2278.158203125, 1527.862060546875], "size": [270, 249.875], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 30}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 60, "type": "VeoVideoGenerationNode", "pos": [1847.100341796875, 1526.047119140625], "size": [406.6000061035156, 453.20001220703125], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 29}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [30]}], "properties": {"Node name for S&R": "VeoVideoGenerationNode"}, "widgets_values": ["3D elf character with mint-green curly hair dancing in gentle breeze, yellow and blue flowers on crown swaying with movements, blue eyes blinking expressively, gaze shifting from sky to flowers, pointed ears slightly twitching, green strap dress flowing elegantly with twirls, lace edges of dress sparkling with subtle glow, surrounding daisies and wildflowers moving with elf's dance steps, pollen sparkling in sunlight, flowers gently opening and closing as elf fingers lightly brush past them, dragonflies darting between flowers, blue sky with changing clouds, lighting gradually shifting from bright to soft dusk glow, camera orbiting around elf showing dynamic beauty from all angles.", "16:9", "", 5, false, "ALLOW", 736703634, "randomize"]}, {"id": 63, "type": "<PERSON>downNote", "pos": [1121.4820556640625, 1535.4681396484375], "size": [378.2833251953125, 274.91668701171875], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 61, "type": "LoadImage", "pos": [1521.***********, 1531.********], "size": [274.*********, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["elf.png", "image"]}], "links": [[29, 61, 0, 60, 0, "IMAGE"], [30, 60, 0, 62, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9090909090909095, "offset": [-1080.4772128075542, -1315.7268448064015]}, "frontendVersion": "1.18.5"}, "version": 0.4}