#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor nonzero_static_cpu(const at::Tensor & self, int64_t size, int64_t fill_value=-1);
TORCH_API at::Tensor & nonzero_static_out_cpu(const at::Tensor & self, int64_t size, int64_t fill_value, at::Tensor & out);
TORCH_API at::Tensor nonzero_static_cuda(const at::Tensor & self, int64_t size, int64_t fill_value=-1);
TORCH_API at::Tensor & nonzero_static_out_cuda(const at::Tensor & self, int64_t size, int64_t fill_value, at::Tensor & out);
} // namespace native
} // namespace at
