{"id": "ef49adbe-6e63-45c0-9f29-fa4677f627ac", "revision": 0, "last_node_id": 36, "last_link_id": 22, "nodes": [{"id": 8, "type": "LoadImage", "pos": [-6500, 1330], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: back", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["back.png", "image"]}, {"id": 7, "type": "LoadImage", "pos": [-6800, 1330], "size": [274.080078125, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: front", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["front.png", "image"]}, {"id": 9, "type": "LoadImage", "pos": [-6800, 1700], "size": [274.080078125, 314], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [7]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: left", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["front.png", "image"]}, {"id": 10, "type": "LoadImage", "pos": [-6500, 1700], "size": [274.080078125, 314], "flags": {}, "order": 3, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8]}, {"name": "MASK", "type": "MASK", "links": null}], "title": "Load Image: right", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["front.png", "image"]}, {"id": 5, "type": "<PERSON>downNote", "pos": [-5910, 1090], "size": [280, 150], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "Save location", "properties": {}, "widgets_values": ["Output model will be auto saved to \"ComfyUI/output/\"\n\nOr you can use the export function in the preview 3D node to download it.\n\n---\n\n输出模型将自动保存到 “ComfyUI/output/”。\n\n或者你可以使用 3D 预览节点中的导出功能来下载它。"], "color": "#432", "bgcolor": "#653"}, {"id": 4, "type": "<PERSON>downNote", "pos": [-7210, 1290], "size": [380, 320], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 20, "type": "<PERSON>downNote", "pos": [-6810, 1120], "size": [270, 120], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "title": "About Image Inputs", "properties": {}, "widgets_values": ["At least one additional view other than the front view is required as input.\n \n---\n\n除了正面视图之外，至少还需要额外一个其它视图输入"], "color": "#432", "bgcolor": "#653"}, {"id": 33, "type": "Preview3D", "pos": [-5010, 1290], "size": [860, 770], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 18}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 34, "type": "Preview3D", "pos": [-4120, 1290], "size": [860, 770], "flags": {}, "order": 19, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 19}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 35, "type": "Preview3D", "pos": [-3220, 1290], "size": [860, 770], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 20}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 36, "type": "Preview3D", "pos": [-2330, 1280], "size": [860, 770], "flags": {}, "order": 18, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 21}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 12.519027944116628, "y": 12.519027944116628, "z": 12.51902794411663}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 6, "type": "TripoMultiviewToModelNode", "pos": [-6200, 1290], "size": [270, 384], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 5}, {"name": "image_left", "shape": 7, "type": "IMAGE", "link": 7}, {"name": "image_back", "shape": 7, "type": "IMAGE", "link": 6}, {"name": "image_right", "shape": 7, "type": "IMAGE", "link": 8}], "outputs": [{"name": "model_file", "type": "STRING", "links": [17]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": [14, 15, 16, 22]}], "properties": {"Node name for S&R": "TripoMultiviewToModelNode"}, "widgets_values": ["v2.5-20250123", "default", true, true, 42, 42, "standard", "original_image", -1, false], "color": "#432", "bgcolor": "#653"}, {"id": 32, "type": "Preview3D", "pos": [-5910, 1290], "size": [860, 770], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 17}], "outputs": [], "properties": {"Node name for S&R": "Preview3D", "Camera Info": {"position": {"x": 7.153772791183921, "y": 7.153772791183921, "z": 7.153772791183922}, "target": {"x": 0, "y": 0, "z": 0}, "zoom": 1, "cameraType": "perspective"}}, "widgets_values": ["", ""]}, {"id": 23, "type": "TripoRigNode", "pos": [-5010, 1160], "size": [222.439453125, 46], "flags": {}, "order": 11, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "MODEL_TASK_ID", "link": 14}], "outputs": [{"name": "model_file", "type": "STRING", "links": [18]}, {"name": "rig task_id", "type": "RIG_TASK_ID", "links": [11]}], "properties": {"Node name for S&R": "TripoRigNode"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 31, "type": "<PERSON>downNote", "pos": [-5010, 950], "size": [290, 160], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Save location", "properties": {}, "widgets_values": ["After the task generated by the base model is completed, <PERSON><PERSON> provides relevant APIs for subsequent model processing. You can enable them as needed. Just use the right - click and set the **Mode** of the corresponding part to **Always**.\n\n---\n\n在基础模型生成的任务执行完成后， Tripo 提供了后续的模型处理的相关 API 你可以按需启用，只需要使用右键，将对应的部分的 **模式** 设置为 **总是** 即可"], "color": "#432", "bgcolor": "#653"}, {"id": 24, "type": "TripoRetargetNode", "pos": [-4110, 1110], "size": [310.453125, 78], "flags": {}, "order": 16, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "RIG_TASK_ID", "link": 11}], "outputs": [{"name": "model_file", "type": "STRING", "links": [19]}, {"name": "retarget task_id", "type": "RETARGET_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoRetargetNode"}, "widgets_values": ["preset:walk"], "color": "#432", "bgcolor": "#653"}, {"id": 30, "type": "<PERSON>downNote", "pos": [-3220, 990], "size": [240, 90], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "About Refine Draft model", "properties": {}, "widgets_values": ["It is only available when using the model of version v1.4.\n\n---\n\n只有在使用模型 v1.4 版本的模型时才可用"], "color": "#322", "bgcolor": "#533"}, {"id": 28, "type": "TripoRefineNode", "pos": [-3220, 1140], "size": [196.41796875, 46], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "model_task_id", "type": "MODEL_TASK_ID", "link": 15}], "outputs": [{"name": "model_file", "type": "STRING", "links": [20]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoRefineNode"}, "widgets_values": [], "color": "#432", "bgcolor": "#653"}, {"id": 26, "type": "TripoTextureNode", "pos": [-2330, 1020], "size": [270, 174], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "model_task_id", "type": "MODEL_TASK_ID", "link": 16}], "outputs": [{"name": "model_file", "type": "STRING", "links": [21]}, {"name": "model task_id", "type": "MODEL_TASK_ID", "links": null}], "properties": {"Node name for S&R": "TripoTextureNode"}, "widgets_values": [true, true, 42, "standard", "original_image"], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "TripoConversionNode", "pos": [-4690, 960], "size": [270, 154], "flags": {}, "order": 14, "mode": 4, "inputs": [{"name": "original_model_task_id", "type": "MODEL_TASK_ID,RIG_TASK_ID,RETARGET_TASK_ID", "link": 22}], "outputs": [], "properties": {"Node name for S&R": "TripoConversionNode"}, "widgets_values": ["GLTF", false, -1, 4096, "JPEG"], "color": "#432", "bgcolor": "#653"}], "links": [[5, 8, 0, 6, 0, "IMAGE"], [6, 7, 0, 6, 2, "IMAGE"], [7, 9, 0, 6, 1, "IMAGE"], [8, 10, 0, 6, 3, "IMAGE"], [11, 23, 1, 24, 0, "RIG_TASK_ID"], [14, 6, 1, 23, 0, "MODEL_TASK_ID"], [15, 6, 1, 28, 0, "MODEL_TASK_ID"], [16, 6, 1, 26, 0, "MODEL_TASK_ID"], [17, 6, 0, 32, 1, "STRING"], [18, 23, 0, 33, 1, "STRING"], [19, 24, 0, 34, 1, "STRING"], [20, 28, 0, 35, 1, "STRING"], [21, 26, 0, 36, 1, "STRING"], [22, 6, 1, 29, 0, "MODEL_TASK_ID,RIG_TASK_ID,RETARGET_TASK_ID"]], "groups": [{"id": 1, "title": "Group", "bounding": [-6810, 1260, 594.080078125, 767.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6115909044842043, "offset": [6494.6278058485395, -677.6490677901852]}, "frontendVersion": "1.21.0"}, "version": 0.4}