{"last_link_id": 174, "nodes": [{"outputs": [{"name": "images", "slot_index": 0, "links": [29], "label": "images", "type": "IMAGE", "localized_name": "images"}], "color": "#322", "widgets_values": [false, 272, 272, 144, 128, "default"], "inputs": [{"name": "vae", "link": 28, "label": "vae", "type": "WANVAE", "localized_name": "vae"}, {"name": "samples", "link": 27, "label": "samples", "type": "LATENT", "localized_name": "samples"}], "flags": {}, "type": "WanVideoDecode", "mode": 0, "bgcolor": "#533", "size": [315, 190], "pos": [3276.1640625, -412.4971923828125], "id": 29, "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoDecode"}, "order": 27}, {"mode": 0, "outputs": [{"name": "Filenames", "label": "Filenames", "type": "VHS_FILENAMES", "localized_name": "Filenames"}], "size": [411.2537536621094, 334], "pos": [3678.5166015625, -434.7030944824219], "widgets_values": {"save_output": true, "filename_prefix": "AnimateDiff", "loop_count": 0, "pix_fmt": "yuv420p", "save_metadata": true, "crf": 19, "trim_to_audio": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00002_hkgiy_1750959922.mp4", "workflow": "AnimateDiff_00002.png", "fullpath": "/data/ComfyUI/personal/445aa9a89d7fa5567450f91c998092da/output/AnimateDiff_00002.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}, "format": "video/h264-mp4", "frame_rate": 16, "pingpong": false}, "inputs": [{"name": "images", "link": 29, "label": "images", "type": "IMAGE", "localized_name": "images"}, {"shape": 7, "name": "audio", "label": "audio", "type": "AUDIO", "localized_name": "audio"}, {"shape": 7, "name": "meta_batch", "label": "meta_batch", "type": "VHS_BatchManager", "localized_name": "meta_batch"}, {"shape": 7, "name": "vae", "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {}, "id": 30, "type": "VHS_VideoCombine", "properties": {"cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "Node name for S&R": "VHS_VideoCombine"}, "order": 28}, {"outputs": [{"name": "block_swap_args", "slot_index": 0, "links": [74], "label": "block_swap_args", "type": "BLOCKSWAPARGS", "localized_name": "block_swap_args"}], "color": "#223", "widgets_values": [30, false, false, true, 15], "inputs": [], "flags": {}, "type": "WanVideoBlockSwap", "mode": 0, "bgcolor": "#335", "size": [315, 154], "pos": [1601.1962890625, -362.6029357910156], "id": 51, "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "Node name for S&R": "WanVideoBlockSwap"}, "order": 0}, {"outputs": [{"name": "value", "links": [149, 159, 172], "label": "value", "type": "INT", "localized_name": "value"}], "color": "#1b4669", "widgets_values": [480], "inputs": [], "flags": {}, "type": "INTConstant", "mode": 0, "bgcolor": "#29699c", "size": [210, 58], "pos": [314.93658447265625, -287.9034118652344], "id": 112, "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "order": 1}, {"outputs": [{"name": "value", "links": [150, 160, 173], "label": "value", "type": "INT", "localized_name": "value"}], "color": "#1b4669", "widgets_values": [832], "inputs": [], "flags": {}, "type": "INTConstant", "mode": 0, "bgcolor": "#29699c", "size": [210, 58], "pos": [310.989501953125, -152.80226135253906], "id": 113, "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "order": 2}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "有趣的80后程序员：accvid+vace首尾帧工作流", "mode": 0, "bgcolor": "#fff0", "size": [743.150390625, 36], "pos": [1314.078125, -1299.660400390625], "id": 117, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 3}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "https://www.youtube.com/@sunleyan_2021", "mode": 0, "bgcolor": "#fff0", "size": [690.943359375, 36], "pos": [1664.68505859375, -1219.1610107421875], "id": 118, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 4}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "bilibli：有趣的80后程序员", "mode": 0, "bgcolor": "#fff0", "size": [408.076171875, 36], "pos": [1325.7381591796875, -1145.500244140625], "id": 119, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 5}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "youtube：AI老腊肉", "mode": 0, "bgcolor": "#fff0", "size": [306.123046875, 36], "pos": [1323.974853515625, -1219.7181396484375], "id": 120, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 6}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "抖音：有趣的80后程序员", "mode": 0, "bgcolor": "#fff0", "size": [400.04296875, 36], "pos": [1327.5889892578125, -1059.7745361328125], "id": 121, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 7}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "工作流下载地址：", "mode": 0, "bgcolor": "#fff0", "size": [288, 36], "pos": [1328.071044921875, -974.3994750976562], "id": 122, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 8}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "https://github.com/amao2001/ganloss-latent-space", "mode": 0, "bgcolor": "#fff0", "size": [806.484375, 36], "pos": [1643.7666015625, -971.1257934570312], "id": 123, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 9}, {"outputs": [], "color": "#fff0", "inputs": [], "flags": {"allow_interaction": true}, "type": "Label (rgthree)", "title": "https://space.bilibili.com/1078072406", "mode": 0, "bgcolor": "#fff0", "size": [590.37890625, 36], "pos": [1763.0946044921875, -1148.11865234375], "id": 124, "properties": {"padding": 0, "backgroundColor": "transparent", "fontFamily": "<PERSON><PERSON>", "borderRadius": 0, "textAlign": "left", "fontSize": 36, "fontColor": "#ffffff"}, "order": 10}, {"outputs": [{"name": "value", "links": [151, 167], "label": "value", "type": "INT", "localized_name": "value"}], "color": "#1b4669", "widgets_values": [81], "inputs": [], "flags": {}, "type": "INTConstant", "mode": 0, "bgcolor": "#29699c", "size": [210, 58], "pos": [310.8360595703125, -12.240076065063477], "id": 114, "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "Node name for S&R": "INTConstant"}, "order": 11}, {"mode": 0, "outputs": [{"name": "IMAGE", "links": [138], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "size": [315, 314], "pos": [619.4679565429688, -284.1726379394531], "widgets_values": ["44e65e1c9389fcbf6839e8f25ea4011391760f1bde25e15cf7b297bd816c3395.png", "image"], "inputs": [], "flags": {}, "id": 106, "type": "LoadImage", "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "LoadImage"}, "order": 12}, {"mode": 0, "outputs": [{"name": "IMAGE", "links": [171], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "size": [315, 314], "pos": [616.78759765625, 90.14111328125], "widgets_values": ["c5c478b4dcf5d2dafc9aca794e8c163233db9da8c26cb135189457a3f7aef933.png", "image"], "inputs": [], "flags": {}, "id": 131, "type": "LoadImage", "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "LoadImage"}, "order": 13}, {"mode": 0, "outputs": [{"name": "samples", "slot_index": 0, "links": [27], "label": "samples", "type": "LATENT", "localized_name": "samples"}], "size": [327.28460693359375, 598], "pos": [3271.638671875, -159.8827667236328], "widgets_values": [8, 1.0000000000000002, 5.000000000000001, 571801377108057, "randomize", true, "unipc", 0, 1, "", "comfy"], "inputs": [{"name": "model", "link": 2, "label": "model", "type": "WANVIDEOMODEL", "localized_name": "model"}, {"name": "text_embeds", "link": 169, "label": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "localized_name": "text_embeds"}, {"shape": 7, "name": "image_embeds", "link": 170, "label": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "localized_name": "image_embeds"}, {"shape": 7, "name": "samples", "label": "samples", "type": "LATENT", "localized_name": "samples"}, {"shape": 7, "name": "feta_args", "label": "feta_args", "type": "FETAARGS", "localized_name": "feta_args"}, {"shape": 7, "name": "context_options", "label": "context_options", "type": "WANVIDCONTEXT", "localized_name": "context_options"}, {"shape": 7, "name": "cache_args", "label": "cache_args", "type": "CACHEARGS", "localized_name": "cache_args"}, {"shape": 7, "name": "flowedit_args", "label": "flowedit_args", "type": "FLOWEDITARGS", "localized_name": "flowedit_args"}, {"shape": 7, "name": "slg_args", "link": 73, "label": "slg_args", "type": "SLGARGS", "localized_name": "slg_args"}, {"shape": 7, "name": "loop_args", "label": "loop_args", "type": "LOOPARGS", "localized_name": "loop_args"}, {"shape": 7, "name": "experimental_args", "link": 71, "label": "experimental_args", "type": "EXPERIMENTALARGS", "localized_name": "experimental_args"}, {"shape": 7, "name": "sigmas", "label": "sigmas", "type": "SIGMAS", "localized_name": "sigmas"}, {"shape": 7, "name": "unianimate_poses", "label": "unianimate_poses", "type": "UNIANIMATE_POSE", "localized_name": "unianimate_poses"}, {"shape": 7, "name": "fantasytalking_embeds", "label": "fantasytalking_embeds", "type": "FANTASYTALKING_EMBEDS", "localized_name": "fantasytalking_embeds"}, {"shape": 7, "name": "uni3c_embeds", "label": "uni3c_embeds", "type": "UNI3C_EMBEDS", "localized_name": "uni3c_embeds"}, {"shape": 7, "name": "multitalk_embeds", "label": "multitalk_embeds", "type": "MULTITALK_EMBEDS", "localized_name": "multitalk_embeds"}], "flags": {}, "id": 4, "type": "WanVideoSampler", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoSampler"}, "order": 26}, {"mode": 0, "outputs": [{"name": "IMAGE", "links": [140, 164], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "width", "label": "width", "type": "INT", "localized_name": "width"}, {"name": "height", "label": "height", "type": "INT", "localized_name": "height"}], "size": [315, 266], "pos": [964.8585815429688, -251.75120544433594], "widgets_values": [480, 720, "lanc<PERSON>s", "crop", "0, 0, 0", "center", 2, "cpu"], "inputs": [{"name": "image", "link": 138, "label": "image", "type": "IMAGE", "localized_name": "image"}, {"widget": {"name": "width"}, "name": "width", "link": 159, "label": "width", "type": "INT"}, {"widget": {"name": "height"}, "name": "height", "link": 160, "label": "height", "type": "INT"}], "flags": {}, "id": 108, "type": "ImageResizeKJv2", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ImageResizeKJv2"}, "order": 20}, {"mode": 0, "outputs": [{"name": "IMAGE", "links": [174], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "width", "label": "width", "type": "INT", "localized_name": "width"}, {"name": "height", "label": "height", "type": "INT", "localized_name": "height"}], "size": [315, 266], "pos": [966.1918334960938, 87.43974304199219], "widgets_values": [480, 720, "lanc<PERSON>s", "crop", "0, 0, 0", "center", 2, "cpu"], "inputs": [{"name": "image", "link": 171, "label": "image", "type": "IMAGE", "localized_name": "image"}, {"widget": {"name": "width"}, "name": "width", "link": 172, "label": "width", "type": "INT"}, {"widget": {"name": "height"}, "name": "height", "link": 173, "label": "height", "type": "INT"}], "flags": {}, "id": 132, "type": "ImageResizeKJv2", "properties": {"cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d", "Node name for S&R": "ImageResizeKJv2"}, "order": 21}, {"outputs": [{"name": "model", "slot_index": 0, "links": [2], "label": "model", "type": "WANVIDEOMODEL", "localized_name": "model"}], "color": "#223", "widgets_values": ["Wan2_1-T2V-14B_fp8_e4m3fn.safetensors", "fp16", "fp8_e4m3fn", "offload_device", "sageattn"], "inputs": [{"shape": 7, "name": "compile_args", "label": "compile_args", "type": "WANCOMPILEARGS", "localized_name": "compile_args"}, {"shape": 7, "name": "block_swap_args", "link": 74, "label": "block_swap_args", "type": "BLOCKSWAPARGS", "localized_name": "block_swap_args"}, {"shape": 7, "name": "lora", "link": 168, "label": "lora", "type": "WANVIDLORA", "localized_name": "lora"}, {"shape": 7, "name": "vram_management_args", "label": "vram_management_args", "type": "VRAM_MANAGEMENTARGS", "localized_name": "vram_management_args"}, {"shape": 7, "name": "vace_model", "link": 70, "label": "vace_model", "type": "VACEPATH", "localized_name": "vace_model"}, {"shape": 7, "name": "fantasytalking_model", "label": "fantasytalking_model", "type": "FANTASYTALKINGMODEL", "localized_name": "fantasytalking_model"}, {"shape": 7, "name": "multitalk_model", "label": "multitalk_model", "type": "MULTITALKMODEL", "localized_name": "multitalk_model"}], "flags": {}, "type": "WanVideoModelLoader", "mode": 0, "bgcolor": "#335", "size": [386.96685791015625, 274], "pos": [2019.9093017578125, -358.9687194824219], "id": 1, "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoModelLoader"}, "order": 22}, {"mode": 0, "outputs": [{"name": "lora", "links": [168], "label": "lora", "type": "WANVIDLORA", "localized_name": "lora"}], "size": [296.3883056640625, 140.13658142089844], "pos": [1618.7957763671875, -148.35723876953125], "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1, false], "inputs": [{"shape": 7, "name": "prev_lora", "label": "prev_lora", "type": "WANVIDLORA", "localized_name": "prev_lora"}, {"shape": 7, "name": "blocks", "label": "blocks", "type": "SELECTEDBLOCKS", "localized_name": "blocks"}], "flags": {}, "id": 130, "type": "WanVideoLoraSelect", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "8479624614ec0d52e982bbbab633736fb1a15eef", "Node name for S&R": "WanVideoLoraSelect"}, "order": 14}, {"mode": 0, "outputs": [{"name": "vace_model", "links": [70], "label": "vace_model", "type": "VACEPATH", "localized_name": "vace_model"}], "size": [294.4861755371094, 58], "pos": [1621.4315185546875, 46.402069091796875], "widgets_values": ["Wan2_1-VACE_module_14B_fp8_e4m3fn.safetensors"], "inputs": [], "flags": {}, "id": 55, "type": "WanVideoVACEModelSelect", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "8479624614ec0d52e982bbbab633736fb1a15eef", "Node name for S&R": "WanVideoVACEModelSelect"}, "order": 15}, {"outputs": [{"name": "wan_t5_model", "slot_index": 0, "links": [3], "label": "wan_t5_model", "type": "WANTEXTENCODER", "localized_name": "wan_t5_model"}], "color": "#332922", "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "inputs": [], "flags": {}, "type": "LoadWanVideoT5TextEncoder", "mode": 0, "bgcolor": "#593930", "size": [377.1661376953125, 130], "pos": [2033.919677734375, -38.219478607177734], "id": 2, "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "LoadWanVideoT5TextEncoder"}, "order": 16}, {"outputs": [{"name": "vae", "slot_index": 0, "links": [4, 28], "label": "vae", "type": "WANVAE", "localized_name": "vae"}], "color": "#322", "widgets_values": ["wan_2.1_vae.safetensors", "bf16"], "inputs": [], "flags": {}, "type": "WanVideoVAELoader", "mode": 0, "bgcolor": "#533", "size": [372.7727966308594, 82], "pos": [2037.063232421875, 141.04141235351562], "id": 3, "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "Node name for S&R": "WanVideoVAELoader"}, "order": 17}, {"mode": 0, "outputs": [{"name": "images", "links": [142], "label": "images", "type": "IMAGE", "localized_name": "images"}, {"name": "masks", "links": [144], "label": "masks", "type": "MASK", "localized_name": "masks"}], "size": [403.1999816894531, 142], "pos": [2468.184814453125, 19.50440216064453], "widgets_values": [65, 0.5], "inputs": [{"shape": 7, "name": "start_image", "link": 140, "label": "start_image", "type": "IMAGE", "localized_name": "start_image"}, {"shape": 7, "name": "end_image", "link": 174, "label": "end_image", "type": "IMAGE", "localized_name": "end_image"}, {"shape": 7, "name": "control_images", "label": "control_images", "type": "IMAGE", "localized_name": "control_images"}, {"shape": 7, "name": "inpaint_mask", "label": "inpaint_mask", "type": "MASK", "localized_name": "inpaint_mask"}, {"widget": {"name": "num_frames"}, "name": "num_frames", "link": 167, "label": "num_frames", "type": "INT"}], "flags": {}, "id": 105, "type": "WanVideoVACEStartToEndFrame", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "8479624614ec0d52e982bbbab633736fb1a15eef", "Node name for S&R": "WanVideoVACEStartToEndFrame"}, "order": 24}, {"mode": 0, "outputs": [{"name": "vace_embeds", "links": [170], "label": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "localized_name": "vace_embeds"}], "size": [315, 334], "pos": [2507.144775390625, -364.3578186035156], "widgets_values": [832, 480, 81, 1.0000000000000002, 0, 1, false], "inputs": [{"name": "vae", "link": 4, "label": "vae", "type": "WANVAE", "localized_name": "vae"}, {"shape": 7, "name": "input_frames", "link": 142, "label": "input_frames", "type": "IMAGE", "localized_name": "input_frames"}, {"shape": 7, "name": "ref_images", "link": 164, "label": "ref_images", "type": "IMAGE", "localized_name": "ref_images"}, {"shape": 7, "name": "input_masks", "link": 144, "label": "input_masks", "type": "MASK", "localized_name": "input_masks"}, {"shape": 7, "name": "prev_vace_embeds", "label": "prev_vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "localized_name": "prev_vace_embeds"}, {"widget": {"name": "width"}, "name": "width", "link": 149, "label": "width", "type": "INT"}, {"widget": {"name": "height"}, "name": "height", "link": 150, "label": "height", "type": "INT"}, {"widget": {"name": "num_frames"}, "name": "num_frames", "link": 151, "label": "num_frames", "type": "INT"}], "flags": {}, "id": 7, "type": "WanVideoVACEEncode", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ce6522c2279c30ed6ac157cbfacd8e15cb1cfae2", "Node name for S&R": "WanVideoVACEEncode"}, "order": 25}, {"mode": 0, "outputs": [{"name": "text_embeds", "links": [169], "label": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "localized_name": "text_embeds"}], "size": [334.9443664550781, 200.62786865234375], "pos": [2876.015869140625, -228.92562866210938], "widgets_values": ["", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true], "inputs": [{"name": "t5", "link": 3, "label": "t5", "type": "WANTEXTENCODER", "localized_name": "t5"}, {"shape": 7, "name": "model_to_offload", "label": "model_to_offload", "type": "WANVIDEOMODEL", "localized_name": "model_to_offload"}], "flags": {}, "id": 6, "type": "WanVideoTextEncode", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ce6522c2279c30ed6ac157cbfacd8e15cb1cfae2", "Node name for S&R": "WanVideoTextEncode"}, "order": 23}, {"mode": 0, "outputs": [{"name": "slg_args", "links": [73], "label": "slg_args", "type": "SLGARGS", "localized_name": "slg_args"}], "size": [315, 106], "pos": [2890.232421875, -401.44036865234375], "widgets_values": ["9,10", 0.20000000000000004, 0.5000000000000001], "inputs": [], "flags": {}, "id": 58, "type": "WanVideoSLG", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "8479624614ec0d52e982bbbab633736fb1a15eef", "Node name for S&R": "WanVideoSLG"}, "order": 18}, {"mode": 0, "outputs": [{"name": "exp_args", "links": [71], "label": "exp_args", "type": "EXPERIMENTALARGS", "localized_name": "exp_args"}], "size": [327.5999755859375, 226], "pos": [2906.658935546875, 17.70732307434082], "widgets_values": ["", true, false, 0, false, 1, 1.25, 20], "inputs": [], "flags": {}, "id": 56, "type": "WanVideoExperimentalArgs", "properties": {"cnr_id": "ComfyUI-WanVideoWrapper", "ver": "8479624614ec0d52e982bbbab633736fb1a15eef", "Node name for S&R": "WanVideoExperimentalArgs"}, "order": 19}], "extra": {"VHS_KeepIntermediate": true, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "frontendVersion": "1.22.2", "ds": {"offset": [-1363.792256673177, 552.8482324812147], "scale": 0.45}}, "groups": [{"color": "#3f789e", "font_size": 24, "flags": {}, "id": 2, "title": "采样", "bounding": [1328.8487548828125, -475.53460693359375, 2278.169677734375, 943.5680541992188]}, {"color": "#3f789e", "font_size": 24, "flags": {}, "id": 3, "title": "配置", "bounding": [300.8360595703125, -361.50335693359375, 234.10052490234375, 417.2632751464844]}, {"color": "#3f789e", "font_size": 24, "flags": {}, "id": 4, "title": "数据处理", "bounding": [609.4679565429688, -357.7726745605469, 697.514404296875, 796.1755981445312]}], "links": [[2, 1, 0, 4, 0, "WANVIDEOMODEL"], [3, 2, 0, 6, 0, "WANTEXTENCODER"], [4, 3, 0, 7, 0, "WANVAE"], [27, 4, 0, 29, 1, "LATENT"], [28, 3, 0, 29, 0, "WANVAE"], [29, 29, 0, 30, 0, "IMAGE"], [70, 55, 0, 1, 4, "VACEPATH"], [71, 56, 0, 4, 10, "EXPERIMENTALARGS"], [73, 58, 0, 4, 8, "SLGARGS"], [74, 51, 0, 1, 1, "BLOCKSWAPARGS"], [138, 106, 0, 108, 0, "IMAGE"], [140, 108, 0, 105, 0, "IMAGE"], [142, 105, 0, 7, 1, "IMAGE"], [144, 105, 1, 7, 3, "MASK"], [149, 112, 0, 7, 5, "INT"], [150, 113, 0, 7, 6, "INT"], [151, 114, 0, 7, 7, "INT"], [159, 112, 0, 108, 1, "INT"], [160, 113, 0, 108, 2, "INT"], [164, 108, 0, 7, 2, "IMAGE"], [167, 114, 0, 105, 4, "INT"], [168, 130, 0, 1, 2, "WANVIDLORA"], [169, 6, 0, 4, 1, "WANVIDEOTEXTEMBEDS"], [170, 7, 0, 4, 2, "WANVIDIMAGE_EMBEDS"], [171, 131, 0, 132, 0, "IMAGE"], [172, 112, 0, 132, 1, "INT"], [173, 113, 0, 132, 2, "INT"], [174, 132, 0, 105, 1, "IMAGE"]], "id": "c8b75117-7a2c-45b9-b5d5-d0dccab7a91f", "config": {}, "version": 0.4, "last_node_id": 132, "revision": 0}