{"id": "232828dd-a063-4f3c-8463-e1d361bdf7c4", "revision": 0, "last_node_id": 9, "last_link_id": 5, "nodes": [{"id": 8, "type": "SaveVideo", "pos": [187.66612243652344, -488.1309509277344], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 5}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"filename_prefix": true, "format": true, "codec": true}}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 7, "type": "MinimaxTextToVideoNode", "pos": [-246.4221954345703, -488.3314208984375], "size": [400, 200], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [5]}], "properties": {"Node name for S&R": "MinimaxTextToVideoNode", "cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"prompt_text": true, "model": true, "seed": true}}, "widgets_values": ["A stunning young woman with flowing hair standing in a golden wheat field under a vivid blue sky with soft white clouds. Her hair flutters gently in the breeze. The sunlight casts a warm, golden glow across the landscape. She wears a light, flowing white dress that moves with the wind. The scene is captured in ultra high definition, with crisp details, cinematic lighting, shallow depth of field, and a soft focus background — realistic, serene, and beautifully composed.\n\n", "T2V-01", 0, "randomize"], "color": "#322", "bgcolor": "#533"}, {"id": 9, "type": "<PERSON>downNote", "pos": [-638.970458984375, -485.8924865722656], "size": [371.3343811035156, 255.60939025878906], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[5, 7, 0, 8, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.****************, "offset": [924.************, 751.5719515041739]}, "frontendVersion": "1.19.1", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}