{"id": "56bd4347-9347-49da-a167-3db05d3bcc28", "revision": 0, "last_node_id": 29, "last_link_id": 21, "nodes": [{"id": 27, "type": "LumaConceptsNode", "pos": [-437.24200439453125, 360.16375732421875], "size": [270, 130], "flags": {}, "order": 0, "mode": 4, "inputs": [{"name": "luma_concepts", "shape": 7, "type": "LUMA_CONCEPTS", "link": null}], "outputs": [{"name": "luma_concepts", "type": "LUMA_CONCEPTS", "links": [19]}], "properties": {"Node name for S&R": "LumaConceptsNode"}, "widgets_values": ["None", "None", "None", "None"]}, {"id": 25, "type": "<PERSON>downNote", "pos": [580, 10], "size": [378.2833251953125, 274.91668701171875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 24, "type": "LumaConceptsNode", "pos": [-142.*************, 359.**************], "size": [270, 130], "flags": {}, "order": 3, "mode": 4, "inputs": [{"name": "luma_concepts", "shape": 7, "type": "LUMA_CONCEPTS", "link": 19}], "outputs": [{"name": "luma_concepts", "type": "LUMA_CONCEPTS", "links": [20]}], "properties": {"Node name for S&R": "LumaConceptsNode"}, "widgets_values": ["zoom_in", "orbit_left", "None", "None"]}, {"id": 26, "type": "<PERSON>downNote", "pos": [150, 10], "size": [386.8224792480469, 292.3617248535156], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About Luma Image to Video", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/luma/luma-text-to-video) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/luma/luma-text-to-video) \n\n\n1 - \"luma_concepts\" is mainly used to control camera movement.\n\n2 - \"seed\" is only used to determine whether a node should be re - run, but the actual result has nothing to do with the seed.\n\n3 - When using the Ray 1.6 model, the duration and resolution parameters will not take effect.\n\n---\n\n\n1 - “luma_concepts” 主要用于控制相机运动\n\n2 - “seed” 仅用于确定节点是否应重新运行，但实际结果与种子无关 \n\n3 - 当使用 Ray 1.6 模型时，duration 和 resolution 参数将不会生效。"], "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "SaveVideo", "pos": [578.6544799804688, 353.26629638671875], "size": [474.9286193847656, 604.5714721679688], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 21}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["ComfyUI_video_luma", "auto", "auto"]}, {"id": 29, "type": "LumaVideoNode", "pos": [146.05001831054688, 357.5299987792969], "size": [413.30999755859375, 319.88800048828125], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "luma_concepts", "shape": 7, "type": "LUMA_CONCEPTS", "link": 20}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [21]}], "properties": {"Node name for S&R": "LumaVideoNode"}, "widgets_values": ["A violinist performing on a rainy street at night, amber streetlights illuminating her and the violin, gentle rain falling around her and she is wet", "ray-2", "16:9", "720p", "5s", false, 963813844488719, "randomize"]}], "links": [[19, 27, 0, 24, 0, "LUMA_CONCEPTS"], [20, 24, 0, 29, 0, "LUMA_CONCEPTS"], [21, 29, 0, 8, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.19.0"}, "version": 0.4}