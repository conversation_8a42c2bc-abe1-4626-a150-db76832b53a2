{"last_node_id": 33, "last_link_id": 56, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1060, 160], "size": [315, 474], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 19}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 55}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 56}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [776509832134660, "randomize", 20, 6, "euler", "normal", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [730, 420], "size": [300, 110], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [270, 150], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 21}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [53]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Masterpiece,best quality,high definition,high level of detail,3D,3D style,cute Q-version,<PERSON><PERSON>,a vibrant product photo created for innovative advertising,featuring a little boy with black hair and a big laugh,<PERSON>,holding a wooden crate,wooden crate,pasture,blue sky,white clouds,brick paved path,with pleasant houses in the background,chimneys,big trees,maple leaves,maple trees,and a lot of wheat,wheat,wheat ears along the roadside,The atmosphere of autumn,yellow grass and leaves,fallen leaves,fences,barriers,orange and yellow colors,autumn,windows,doors,gravel roads,captured using a Sony Alpha A7R IV camera with a 35mm f/1.4 lens,aperture set to f/2.8,shutter speed of 1/100 second,"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [270, 360], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 20}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [54]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis)"]}, {"id": 8, "type": "VAEDecode", "pos": [1420, 160], "size": [210, 46], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 7}, {"label": "vae", "name": "vae", "type": "VAE", "link": 14}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [9]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1420, 250], "size": [580, 650], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}, {"id": 11, "type": "LoadImage", "pos": [-160, 400], "size": [387.97003173828125, 465.5097961425781], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [52]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1, "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["scribble_input.png", "image", ""]}, {"id": 12, "type": "ControlNetLoader", "pos": [281.73468017578125, 636.84765625], "size": [340, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "CONTROL_NET", "name": "CONTROL_NET", "type": "CONTROL_NET", "slot_index": 0, "links": [51]}], "properties": {"Node name for S&R": "ControlNetLoader", "models": [{"name": "control_v11p_sd15_scribble_fp16.safetensors", "url": "https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors?download=true", "directory": "controlnet"}]}, "widgets_values": ["control_v11p_sd15_scribble_fp16.safetensors"]}, {"id": 13, "type": "VAELoader", "pos": [-160, 290], "size": [380, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [14]}], "properties": {"Node name for S&R": "VAELoader", "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"]}, {"id": 14, "type": "CheckpointLoaderSimple", "pos": [-160, 150], "size": [380, 100], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [19]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [20, 21]}, {"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 2, "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "dreamCreationVirtual3DECommerce_v10.safetensors", "url": "https://civitai.com/api/download/models/731340?type=Model&format=SafeTensor&size=full&fp=fp16", "directory": "checkpoints"}]}, "widgets_values": ["dreamCreationVirtual3DECommerce_v10.safetensors"]}, {"id": 32, "type": "ControlNetApplyAdvanced", "pos": [721.2879028320312, -52.88718032836914], "size": [315, 186], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 53}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 54}, {"label": "control_net", "name": "control_net", "type": "CONTROL_NET", "link": 51}, {"label": "image", "name": "image", "type": "IMAGE", "link": 52}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [55]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [56]}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [1, 0, 1]}, {"id": 33, "type": "<PERSON>downNote", "pos": [-160, 920], "size": [336, 152], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [ControlNet - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/controlnet/) — Overview\n> \n> [ControlNet Usage - docs.comfy.org](https://docs.comfy.org/tutorials/controlnet/controlnet) — Explanation of concepts and step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [9, 8, 0, 9, 0, "IMAGE"], [14, 13, 0, 8, 1, "VAE"], [19, 14, 0, 3, 0, "MODEL"], [20, 14, 1, 7, 0, "CLIP"], [21, 14, 1, 6, 0, "CLIP"], [51, 12, 0, 32, 2, "CONTROL_NET"], [52, 11, 0, 32, 3, "IMAGE"], [53, 6, 0, 32, 0, "CONDITIONING"], [54, 7, 0, 32, 1, "CONDITIONING"], [55, 32, 0, 3, 1, "CONDITIONING"], [56, 32, 1, 3, 2, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"node_versions": {"comfy-core": "0.3.14"}}, "version": 0.4}