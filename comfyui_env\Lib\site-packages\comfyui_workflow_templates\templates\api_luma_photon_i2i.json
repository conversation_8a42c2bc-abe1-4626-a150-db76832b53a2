{"id": "7a9b9238-d961-4305-9894-bf4ae73f5bd2", "revision": 0, "last_node_id": 7, "last_link_id": 3, "nodes": [{"id": 3, "type": "LoadImage", "pos": [-136.33944702148438, 845.408447265625], "size": [270, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 7, "type": "<PERSON>downNote", "pos": [-474.4827575683594, 1097.0435791015625], "size": [315.338134765625, 273.75128173828125], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 6, "type": "<PERSON>downNote", "pos": [-472.*************, 854.*************], "size": [309.*************, 194.**************], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About Luma Image to Image", "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/luma/luma-image-to-image) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/luma/luma-image-to-image) \n\n1 - \"image\" is required, and only single - image input is supported.\n\n2 - The more \"image_weight\", the freer the output result.\n\n---\n\n1 - “image” 是必须的，仅支持单张图片输入\n\n2 - “image_weight” 越大输出的结果越自由"], "color": "#432", "bgcolor": "#653"}, {"id": 5, "type": "SaveImage", "pos": [621.649658203125, 855.6395263671875], "size": [270, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "outputs": [], "properties": {}, "widgets_values": ["Luma/img2img/00"]}, {"id": 2, "type": "LumaImageModifyNode", "pos": [176.6533660888672, 850.8272094726562], "size": [400, 200], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3]}], "properties": {"Node name for S&R": "LumaImageModifyNode"}, "widgets_values": ["a photorealistic high definition of a Ramphastos dicolorus eye's iris on a green vivid background", 0, "photon-1", 318243184397756, "randomize"]}], "links": [[1, 3, 0, 2, 0, "IMAGE"], [3, 2, 0, 5, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.5"}, "version": 0.4}