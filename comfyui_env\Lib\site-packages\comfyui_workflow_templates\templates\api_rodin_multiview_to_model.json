{"id": "cb4a348c-918b-42f8-b0a9-ff75063a3171", "revision": 0, "last_node_id": 65, "last_link_id": 79, "nodes": [{"id": 46, "type": "<PERSON>downNote", "pos": [-2400, 640], "size": [380.9487609863281, 309.4496765136719], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "Note", "pos": [-2010, 640], "size": [236.*************, 309.**********], "flags": {"collapsed": false}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "Tips", "properties": {}, "widgets_values": ["Try to provide the following types of images:\n- Provide images from multiple angles.\n- Images with a solid color background.\n\nThis helps to generate a model that better meets the requirements. Of course, thanks to <PERSON><PERSON>'s excellent product capabilities, the subject in the unprocessed image can also achieve a good model output.  \n\n---\n\n尽量提供下面的图片类型：\n- 提供多角度\n- 纯色背景的图片\n\n这有助于生成更符合要求的模型，当然依托于 Rodin 的优秀的产品能力，未经处理的图片中的主体也获得不错的模型输出"], "color": "#432", "bgcolor": "#653"}, {"id": 41, "type": "LoadImage", "pos": [-2380, 1150], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [74]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["front.jpg", "image"]}, {"id": 56, "type": "LoadImage", "pos": [-1790, 1150], "size": [274.080078125, 314], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [57]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["back.jpg", "image"]}, {"id": 57, "type": "ImageBatch", "pos": [-1660, 1050], "size": [140, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 73}, {"name": "image2", "type": "IMAGE", "link": 57}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [59, 70, 71, 72]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 40, "type": "LoadImage", "pos": [-2090, 1150], "size": [274.080078125, 314.00006103515625], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [75]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["left.jpg", "image"]}, {"id": 55, "type": "Note", "pos": [-1077.4710693359375, 790.9989013671875], "size": [294.3723449707031, 104.65211486816406], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Tips", "properties": {}, "widgets_values": ["The larger the Polygon_count, the more detailed the model.\n\nPolygon_count 越大模型越精细"], "color": "#432", "bgcolor": "#653"}, {"id": 58, "type": "ImageBatch", "pos": [-1960, 1050], "size": [140, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 74}, {"name": "image2", "type": "IMAGE", "link": 75}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [73]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 60, "type": "<PERSON>downNote", "pos": [-510, 670], "size": [360, 130], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["Rodin provides model generation APIs with different levels of precision. You can set the corresponding nodes to the \"always\" mode according to your requirements.  \n\n---\n\nRodin 提供了不同精度的模型生成 API，你可以按需要将对应的节点设置为 always 模式"], "color": "#432", "bgcolor": "#653"}, {"id": 48, "type": "<PERSON>downNote", "pos": [-1910, 1820], "size": [409.3424987792969, 141.7489013671875], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "File location", "properties": {}, "widgets_values": ["Output model will be auto saved to \"ComfyUI/output/Rodin3D\"\n\nOr you can use the export function in the preview 3D node to download it.\n\n---\n\n输出模型将自动保存到 “ComfyUI/output/Rodin3D”。\n\n或者你可以使用 3D 预览节点中的导出功能来下载它。"], "color": "#432", "bgcolor": "#653"}, {"id": 28, "type": "Rodin3D_Regular", "pos": [-1450, 790], "size": [344.2574157714844, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "Images", "type": "IMAGE", "link": 59}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [76]}], "properties": {"Node name for S&R": "Rodin3D_Regular"}, "widgets_values": [0, "PBR", "200K-Triangle"], "color": "#432", "bgcolor": "#653"}, {"id": 31, "type": "Rodin3D_Sketch", "pos": [-515.5814819335938, 852.9099731445312], "size": [354.0360107421875, 58], "flags": {"collapsed": false}, "order": 11, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 70}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [77]}], "properties": {"Node name for S&R": "Rodin3D_Sketch"}, "widgets_values": [0], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "Rodin3D_Detail", "pos": [414.8179626464844, 820.849365234375], "size": [350.0273742675781, 106], "flags": {}, "order": 12, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 71}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [78]}], "properties": {"Node name for S&R": "Rodin3D_Detail"}, "widgets_values": [0, "PBR", "200K-Triangle"], "color": "#432", "bgcolor": "#653"}, {"id": 30, "type": "Rodin3D_Smooth", "pos": [1329.1629638671875, 817.242431640625], "size": [343.4781188964844, 106], "flags": {}, "order": 13, "mode": 4, "inputs": [{"name": "Images", "type": "IMAGE", "link": 72}], "outputs": [{"name": "3D Model Path", "type": "STRING", "links": [79]}], "properties": {"Node name for S&R": "Rodin3D_Smooth"}, "widgets_values": [0, "PBR", "18K-Quad"], "color": "#432", "bgcolor": "#653"}, {"id": 61, "type": "Preview3D", "pos": [-1450, 1010], "size": [880, 890], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 76}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 62, "type": "Preview3D", "pos": [-520, 1010], "size": [880, 890], "flags": {}, "order": 15, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 77}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 63, "type": "Preview3D", "pos": [410, 1010], "size": [880, 890], "flags": {}, "order": 16, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 78}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}, {"id": 64, "type": "Preview3D", "pos": [1330, 1010], "size": [880, 890], "flags": {}, "order": 17, "mode": 4, "inputs": [{"name": "camera_info", "shape": 7, "type": "LOAD3D_CAMERA", "link": null}, {"name": "model_file", "type": "STRING", "widget": {"name": "model_file"}, "link": 79}], "outputs": [], "properties": {"Node name for S&R": "Preview3D"}, "widgets_values": ["", ""]}], "links": [[57, 56, 0, 57, 1, "IMAGE"], [59, 57, 0, 28, 0, "IMAGE"], [70, 57, 0, 31, 0, "IMAGE"], [71, 57, 0, 29, 0, "IMAGE"], [72, 57, 0, 30, 0, "IMAGE"], [73, 58, 0, 57, 0, "IMAGE"], [74, 41, 0, 58, 0, "IMAGE"], [75, 40, 0, 58, 1, "IMAGE"], [76, 28, 0, 61, 1, "STRING"], [77, 31, 0, 62, 1, "STRING"], [78, 29, 0, 63, 1, "STRING"], [79, 30, 0, 64, 1, "STRING"]], "groups": [{"id": 1, "title": "Example - Maximum input images: 5", "bounding": [-2410, 970, 920, 520], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8954302432553175, "offset": [2528.1401735681125, -624.1011140421733]}, "frontendVersion": "1.21.0"}, "version": 0.4}