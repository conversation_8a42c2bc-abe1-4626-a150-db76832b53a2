{"id": "1bb851bd-6a15-4b6e-b8c1-8fef98f0652f", "revision": 0, "last_node_id": 8, "last_link_id": 5, "nodes": [{"id": 2, "type": "LoadImage", "pos": [-748.9365844726562, 14.468798637390137], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_263067_.png", "image"]}, {"id": 3, "type": "LoadImage", "pos": [-460.9363098144531, 14.468798637390137], "size": [274.080078125, 314], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [4]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_263067_.png", "image"]}, {"id": 4, "type": "SaveVideo", "pos": [275.063720703125, -337.53143310546875], "size": [384, 160], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "video", "type": "VIDEO", "link": 5}], "outputs": [], "properties": {"Node name for S&R": "SaveVideo"}, "widgets_values": ["video/ComfyUI", "auto", "auto"]}, {"id": 5, "type": "LoadImage", "pos": [-748.9365844726562, -337.53143310546875], "size": [274.080078125, 314], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["mr_crazey_<PERSON>_photograph_of_a_girl_leaning_on_the_wall_of_a_build_6350bf9d-c9b1-47e7-bb4e-a57f66bcf130.png", "image"]}, {"id": 6, "type": "LoadImage", "pos": [-460.9363098144531, -337.53143310546875], "size": [274.080078125, 314], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["u8355824572_3D_cartoon_Buddha_statue_Cyberpunk._A_Bodhisattva_w_9bc69043-9d52-4162-b59f-2cc6ae239382.png", "image"]}, {"id": 1, "type": "PikaScenesV2_2", "pos": [-140.9361572265625, -337.53143310546875], "size": [400, 366], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image_ingredient_1", "shape": 7, "type": "IMAGE", "link": 1}, {"name": "image_ingredient_2", "shape": 7, "type": "IMAGE", "link": 2}, {"name": "image_ingredient_3", "shape": 7, "type": "IMAGE", "link": 3}, {"name": "image_ingredient_4", "shape": 7, "type": "IMAGE", "link": 4}, {"name": "image_ingredient_5", "shape": 7, "type": "IMAGE", "link": null}], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [5]}], "properties": {"Node name for S&R": "PikaScenesV2_2"}, "widgets_values": ["A girl on the street meets a machine budda", "", 2600589, "randomize", "1080p", 5, "creative", 1.7777777777777777]}, {"id": 8, "type": "<PERSON>downNote", "pos": [-1182.4033203125, -329.3785400390625], "size": [389.0563049316406, 249.69961547851562], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 5, 0, 1, 0, "IMAGE"], [2, 6, 0, 1, 1, "IMAGE"], [3, 2, 0, 1, 2, "IMAGE"], [4, 3, 0, 1, 3, "IMAGE"], [5, 1, 0, 4, 0, "VIDEO"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.18.5"}, "version": 0.4}