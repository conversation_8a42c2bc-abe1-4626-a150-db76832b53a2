{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 22, "last_link_id": 8, "nodes": [{"id": 20, "type": "SaveImage", "pos": [1040, 220], "size": [315, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 8}], "outputs": [], "properties": {}, "widgets_values": ["DALL-E2"]}, {"id": 10, "type": "OpenAIDalle2", "pos": [620, 220], "size": [400, 270], "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": null}, {"name": "mask", "shape": 7, "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8]}], "properties": {"Node name for S&R": "OpenAIDalle2"}, "widgets_values": ["A bustling ancient Roman marketplace filled with people in traditional togas, vendors selling fruits, spices, and pottery. There are marble columns and arches in the background, and a chariot being pulled by horses passing through the crowd. The sky is a clear blue, and the sunlight casts realistic shadows on the ground. The painting style is reminiscent of classical Roman art, with detailed textures and a warm color palette", 263985102, "randomize", "1024x1024", 1]}, {"id": 21, "type": "<PERSON>downNote", "pos": [240, 220], "size": [344.50994873046875, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["[Tuotrial](https://docs.comfy.org/tutorials/api-nodes/openai/dall-e-2) | [教程](https://docs.comfy.org/zh-CN/tutorials/api-nodes/openai/dall-e-2)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 22, "type": "<PERSON>downNote", "pos": [246.8136444091797, 358.99835205078125], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[8, 10, 0, 20, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.****************, "offset": [-166.************, -72.09134083731502]}, "frontendVersion": "1.18.5", "node_versions": {"comfy-core": "0.3.29"}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}