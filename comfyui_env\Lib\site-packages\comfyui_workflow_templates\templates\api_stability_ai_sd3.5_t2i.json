{"id": "e62e2c49-db9e-49bc-a71b-3871b67cc7da", "revision": 0, "last_node_id": 5, "last_link_id": 3, "nodes": [{"id": 2, "type": "SaveImage", "pos": [2189.6669921875, 1334.1549072265625], "size": [315, 365], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "widget_ue_connectable": {"filename_prefix": true}}, "widgets_values": ["ComfyUI"]}, {"id": 4, "type": "LoadImage", "pos": [1346.583740234375, 1338.996337890625], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "LoadImage"}, "widgets_values": ["002.jpg", "image"]}, {"id": 3, "type": "<PERSON>downNote", "pos": [1005.7901611328125, 1342.15771484375], "size": [315.338134765625, 273.75128173828125], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 1, "type": "StabilityStableImageSD_3_5Node", "pos": [1675.************, 1335.************], "size": [484.**********, 357.***********], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 2}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": 3}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.29", "Node name for S&R": "StabilityStableImageSD_3_5Node", "widget_ue_connectable": {"prompt": true, "model": true, "aspect_ratio": true, "style_preset": true, "cfg_scale": true, "seed": true, "image_denoise": true}}, "widgets_values": ["a serene and peaceful scene of a glass cup filled with water and white daisies, the glass cup is positioned in the center of the image, with the flowers scattered around it, to the left of the cup, the camera is positioned directly, capturing the delicate petals and golden centers of the flowers, the background is blurred, with soft sunlight filtering through the leaves, creating a warm and inviting atmosphere, the overall effect is one of tranquility and natural beauty", "sd3.5-large", "1:1", "cinematic", 4, 0, 1, [false, true]], "color": "#432", "bgcolor": "#653"}, {"id": 5, "type": "PrimitiveStringMultiline", "pos": [1348.482666015625, 1712.058837890625], "size": [274.6571044921875, 184.1536865234375], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [3]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.31", "Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": [""]}], "links": [[1, 1, 0, 2, 0, "IMAGE"], [2, 4, 0, 1, 0, "IMAGE"], [3, 5, 0, 1, 1, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": [-618.4652809968507, -1097.8925369209926]}, "frontendVersion": "1.18.9", "ue_links": [], "links_added_by_ue": []}, "version": 0.4}