#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API resize_as_sparse_ {
  using schema = const at::Tensor & (const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as_sparse_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "resize_as_sparse_(Tensor(a!) self, Tensor the_template) -> Tensor(a!)";
  static const at::Tensor & call(const at::Tensor & self, const at::Tensor & the_template);
  static const at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template);
};

struct TORCH_API resize_as_sparse_out {
  using schema = const at::Tensor & (const at::Tensor &, const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as_sparse";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "resize_as_sparse.out(Tensor self, Tensor the_template, *, Tensor(a!) out) -> Tensor(a!)";
  static const at::Tensor & call(const at::Tensor & self, const at::Tensor & the_template, const at::Tensor & out);
  static const at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template, const at::Tensor & out);
};

struct TORCH_API resize_as_sparse {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as_sparse";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "resize_as_sparse(Tensor self, Tensor the_template) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & the_template);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template);
};

}} // namespace at::_ops
