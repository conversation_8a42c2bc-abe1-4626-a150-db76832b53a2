{"version": 3, "file": "WelcomeView-BN8-B3IA.js", "sources": ["../../src/views/WelcomeView.vue"], "sourcesContent": ["<template>\n  <BaseViewTemplate dark>\n    <div class=\"flex flex-col items-center justify-center gap-8 p-8\">\n      <!-- Header -->\n      <h1 class=\"animated-gradient-text text-glow select-none\">\n        {{ $t('welcome.title') }}\n      </h1>\n\n      <!-- Get Started Button -->\n      <Button\n        :label=\"$t('welcome.getStarted')\"\n        icon=\"pi pi-arrow-right\"\n        icon-pos=\"right\"\n        size=\"large\"\n        rounded\n        class=\"p-4 text-lg fade-in-up\"\n        @click=\"navigateTo('/install')\"\n      />\n    </div>\n  </BaseViewTemplate>\n</template>\n\n<script setup lang=\"ts\">\nimport Button from 'primevue/button'\nimport { useRouter } from 'vue-router'\n\nimport BaseViewTemplate from '@/views/templates/BaseViewTemplate.vue'\n\nconst router = useRouter()\nconst navigateTo = async (path: string) => {\n  await router.push(path)\n}\n</script>\n\n<style scoped>\n.animated-gradient-text {\n  @apply font-bold;\n  font-size: clamp(2rem, 8vw, 4rem);\n  background: linear-gradient(to right, #12c2e9, #c471ed, #f64f59, #12c2e9);\n  background-size: 300% auto;\n  background-clip: text;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  animation: gradient 8s linear infinite;\n}\n\n.text-glow {\n  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));\n}\n\n@keyframes gradient {\n  0% {\n    background-position: 0% center;\n  }\n\n  100% {\n    background-position: 300% center;\n  }\n}\n\n.fade-in-up {\n  animation: fadeInUp 1.5s ease-out;\n  animation-fill-mode: both;\n}\n\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;AA4BA,UAAM,SAAS;AACT,UAAA,aAAa,8BAAO,SAAiB;AACnC,YAAA,OAAO,KAAK,IAAI;AAAA,IAAA,GADL;;;;;;;;;;;;;;;;;;;;;;;"}