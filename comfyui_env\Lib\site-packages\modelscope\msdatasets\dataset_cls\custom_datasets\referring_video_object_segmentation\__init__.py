# Copyright (c) Alibaba, Inc. and its affiliates.
from typing import TYPE_CHECKING

from modelscope.utils.import_utils import LazyImportModule

if TYPE_CHECKING:
    from .referring_video_object_segmentation_dataset import ReferringVideoObjectSegmentationDataset
else:
    _import_structure = {
        'referring_video_object_segmentation_dataset':
        ['MovieSceneSegmentationDataset'],
    }
    import sys

    sys.modules[__name__] = LazyImportModule(
        __name__,
        globals()['__file__'],
        _import_structure,
        module_spec=__spec__,
        extra_objects={},
    )
