#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/sigmoid_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_sigmoid_out : public at::meta::structured_sigmoid {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_sigmoid(const at::Tensor & self);
TORCH_API at::Tensor & mkldnn_sigmoid_(at::Tensor & self);
TORCH_API at::Tensor sigmoid_quantized_cpu(const at::Tensor & self);
} // namespace native
} // namespace at
