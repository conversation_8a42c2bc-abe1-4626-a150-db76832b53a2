{"version": 3, "file": "vendor-vue-i18n-CdFxvEOa.js", "sources": ["../../node_modules/@vue/devtools-api/lib/esm/env.js", "../../node_modules/@vue/devtools-api/lib/esm/const.js", "../../node_modules/@vue/devtools-api/lib/esm/time.js", "../../node_modules/@vue/devtools-api/lib/esm/proxy.js", "../../node_modules/@vue/devtools-api/lib/esm/index.js", "../../node_modules/@intlify/shared/dist/shared.mjs", "../../node_modules/@intlify/message-compiler/dist/message-compiler.esm-browser.js", "../../node_modules/@intlify/core-base/dist/core-base.mjs", "../../node_modules/vue-i18n/dist/vue-i18n.mjs"], "sourcesContent": ["export function getDevtoolsGlobalHook() {\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n    // @ts-expect-error navigator and windows are not available in all environments\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\n        ? window\n        : typeof globalThis !== 'undefined'\n            ? globalThis\n            : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';\n", "export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n", "let supported;\nlet perf;\nexport function isPerformanceSupported() {\n    var _a;\n    if (supported !== undefined) {\n        return supported;\n    }\n    if (typeof window !== 'undefined' && window.performance) {\n        supported = true;\n        perf = window.performance;\n    }\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n        supported = true;\n        perf = globalThis.perf_hooks.performance;\n    }\n    else {\n        supported = false;\n    }\n    return supported;\n}\nexport function now() {\n    return isPerformanceSupported() ? perf.now() : Date.now();\n}\n", "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n    constructor(plugin, hook) {\n        this.target = null;\n        this.targetQueue = [];\n        this.onQueue = [];\n        this.plugin = plugin;\n        this.hook = hook;\n        const defaultSettings = {};\n        if (plugin.settings) {\n            for (const id in plugin.settings) {\n                const item = plugin.settings[id];\n                defaultSettings[id] = item.defaultValue;\n            }\n        }\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n        let currentSettings = Object.assign({}, defaultSettings);\n        try {\n            const raw = localStorage.getItem(localSettingsSaveId);\n            const data = JSON.parse(raw);\n            Object.assign(currentSettings, data);\n        }\n        catch (e) {\n            // noop\n        }\n        this.fallbacks = {\n            getSettings() {\n                return currentSettings;\n            },\n            setSettings(value) {\n                try {\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n                }\n                catch (e) {\n                    // noop\n                }\n                currentSettings = value;\n            },\n            now() {\n                return now();\n            },\n        };\n        if (hook) {\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n                if (pluginId === this.plugin.id) {\n                    this.fallbacks.setSettings(value);\n                }\n            });\n        }\n        this.proxiedOn = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target.on[prop];\n                }\n                else {\n                    return (...args) => {\n                        this.onQueue.push({\n                            method: prop,\n                            args,\n                        });\n                    };\n                }\n            },\n        });\n        this.proxiedTarget = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target[prop];\n                }\n                else if (prop === 'on') {\n                    return this.proxiedOn;\n                }\n                else if (Object.keys(this.fallbacks).includes(prop)) {\n                    return (...args) => {\n                        this.targetQueue.push({\n                            method: prop,\n                            args,\n                            resolve: () => { },\n                        });\n                        return this.fallbacks[prop](...args);\n                    };\n                }\n                else {\n                    return (...args) => {\n                        return new Promise((resolve) => {\n                            this.targetQueue.push({\n                                method: prop,\n                                args,\n                                resolve,\n                            });\n                        });\n                    };\n                }\n            },\n        });\n    }\n    async setRealTarget(target) {\n        this.target = target;\n        for (const item of this.onQueue) {\n            this.target.on[item.method](...item.args);\n        }\n        for (const item of this.targetQueue) {\n            item.resolve(await this.target[item.method](...item.args));\n        }\n    }\n}\n", "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy) {\n            setupFn(proxy.proxiedTarget);\n        }\n    }\n}\n", "/*!\n  * shared v9.14.3\n  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>\n  * Released under the MIT License.\n  */\n/**\n * Original Utilities\n * written by ka<PERSON><PERSON> kawa<PERSON>\n */\nconst inBrowser = typeof window !== 'undefined';\nlet mark;\nlet measure;\nif ((process.env.NODE_ENV !== 'production')) {\n    const perf = inBrowser && window.performance;\n    if (perf &&\n        perf.mark &&\n        perf.measure &&\n        perf.clearMarks &&\n        // @ts-ignore browser compat\n        perf.clearMeasures) {\n        mark = (tag) => {\n            perf.mark(tag);\n        };\n        measure = (name, startTag, endTag) => {\n            perf.measure(name, startTag, endTag);\n            perf.clearMarks(startTag);\n            perf.clearMarks(endTag);\n        };\n    }\n}\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n    if (args.length === 1 && isObject(args[0])) {\n        args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n        args = {};\n    }\n    return message.replace(RE_ARGS, (match, identifier) => {\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\n    });\n}\nconst makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });\nconst friendlyJSONstringify = (json) => JSON.stringify(json)\n    .replace(/\\u2028/g, '\\\\u2028')\n    .replace(/\\u2029/g, '\\\\u2029')\n    .replace(/\\u0027/g, '\\\\u0027');\nconst isNumber = (val) => typeof val === 'number' && isFinite(val);\nconst isDate = (val) => toTypeString(val) === '[object Date]';\nconst isRegExp = (val) => toTypeString(val) === '[object RegExp]';\nconst isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;\nconst assign = Object.assign;\nconst _create = Object.create;\nconst create = (obj = null) => _create(obj);\nlet _globalThis;\nconst getGlobalThis = () => {\n    // prettier-ignore\n    return (_globalThis ||\n        (_globalThis =\n            typeof globalThis !== 'undefined'\n                ? globalThis\n                : typeof self !== 'undefined'\n                    ? self\n                    : typeof window !== 'undefined'\n                        ? window\n                        : typeof global !== 'undefined'\n                            ? global\n                            : create()));\n};\nfunction escapeHtml(rawText) {\n    return rawText\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;');\n}\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn(obj, key) {\n    return hasOwnProperty.call(obj, key);\n}\n/* eslint-enable */\n/**\n * Useful Utilities By Evan you\n * Modified by kazuya kawaguchi\n * MIT License\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\n */\nconst isArray = Array.isArray;\nconst isFunction = (val) => typeof val === 'function';\nconst isString = (val) => typeof val === 'string';\nconst isBoolean = (val) => typeof val === 'boolean';\nconst isSymbol = (val) => typeof val === 'symbol';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = (val) => val !== null && typeof val === 'object';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isPromise = (val) => {\n    return isObject(val) && isFunction(val.then) && isFunction(val.catch);\n};\nconst objectToString = Object.prototype.toString;\nconst toTypeString = (value) => objectToString.call(value);\nconst isPlainObject = (val) => {\n    if (!isObject(val))\n        return false;\n    const proto = Object.getPrototypeOf(val);\n    return proto === null || proto.constructor === Object;\n};\n// for converting list and named values to displayed strings.\nconst toDisplayString = (val) => {\n    return val == null\n        ? ''\n        : isArray(val) || (isPlainObject(val) && val.toString === objectToString)\n            ? JSON.stringify(val, null, 2)\n            : String(val);\n};\nfunction join(items, separator = '') {\n    return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');\n}\nconst RANGE = 2;\nfunction generateCodeFrame(source, start = 0, end = source.length) {\n    const lines = source.split(/\\r?\\n/);\n    let count = 0;\n    const res = [];\n    for (let i = 0; i < lines.length; i++) {\n        count += lines[i].length + 1;\n        if (count >= start) {\n            for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\n                if (j < 0 || j >= lines.length)\n                    continue;\n                const line = j + 1;\n                res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\n                const lineLength = lines[j].length;\n                if (j === i) {\n                    // push underline\n                    const pad = start - (count - lineLength) + 1;\n                    const length = Math.max(1, end > count ? lineLength - pad : end - start);\n                    res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\n                }\n                else if (j > i) {\n                    if (end > count) {\n                        const length = Math.max(Math.min(end - count, lineLength), 1);\n                        res.push(`   |  ` + '^'.repeat(length));\n                    }\n                    count += lineLength + 1;\n                }\n            }\n            break;\n        }\n    }\n    return res.join('\\n');\n}\nfunction incrementer(code) {\n    let current = code;\n    return () => ++current;\n}\n\nfunction warn(msg, err) {\n    if (typeof console !== 'undefined') {\n        console.warn(`[intlify] ` + msg);\n        /* istanbul ignore if */\n        if (err) {\n            console.warn(err.stack);\n        }\n    }\n}\nconst hasWarned = {};\nfunction warnOnce(msg) {\n    if (!hasWarned[msg]) {\n        hasWarned[msg] = true;\n        warn(msg);\n    }\n}\n\n/**\n * Event emitter, forked from the below:\n * - original repository url: https://github.com/developit/mitt\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\n * - author: Jason Miller (https://github.com/developit)\n * - license: MIT\n */\n/**\n * Create a event emitter\n *\n * @returns An event emitter\n */\nfunction createEmitter() {\n    const events = new Map();\n    const emitter = {\n        events,\n        on(event, handler) {\n            const handlers = events.get(event);\n            const added = handlers && handlers.push(handler);\n            if (!added) {\n                events.set(event, [handler]);\n            }\n        },\n        off(event, handler) {\n            const handlers = events.get(event);\n            if (handlers) {\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n            }\n        },\n        emit(event, payload) {\n            (events.get(event) || [])\n                .slice()\n                .map(handler => handler(payload));\n            (events.get('*') || [])\n                .slice()\n                .map(handler => handler(event, payload));\n        }\n    };\n    return emitter;\n}\n\nconst isNotObjectOrIsArray = (val) => !isObject(val) || isArray(val);\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction deepCopy(src, des) {\n    // src and des should both be objects, and none of them can be a array\n    if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\n        throw new Error('Invalid value');\n    }\n    const stack = [{ src, des }];\n    while (stack.length) {\n        const { src, des } = stack.pop();\n        // using `Object.keys` which skips prototype properties\n        Object.keys(src).forEach(key => {\n            if (key === '__proto__') {\n                return;\n            }\n            // if src[key] is an object/array, set des[key]\n            // to empty object/array to prevent setting by reference\n            if (isObject(src[key]) && !isObject(des[key])) {\n                des[key] = Array.isArray(src[key]) ? [] : create();\n            }\n            if (isNotObjectOrIsArray(des[key]) || isNotObjectOrIsArray(src[key])) {\n                // replace with src[key] when:\n                // src[key] or des[key] is not an object, or\n                // src[key] or des[key] is an array\n                des[key] = src[key];\n            }\n            else {\n                // src[key] and des[key] are both objects, merge them\n                stack.push({ src: src[key], des: des[key] });\n            }\n        });\n    }\n}\n\nexport { assign, create, createEmitter, deepCopy, escapeHtml, format, friendlyJSONstringify, generateCodeFrame, generateFormatCacheKey, getGlobalThis, hasOwn, inBrowser, incrementer, isArray, isBoolean, isDate, isEmptyObject, isFunction, isNumber, isObject, isPlainObject, isPromise, isRegExp, isString, isSymbol, join, makeSymbol, mark, measure, objectToString, toDisplayString, toTypeString, warn, warnOnce };\n", "/*!\n  * message-compiler v9.14.3\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nconst LOCATION_STUB = {\n    start: { line: 1, column: 1, offset: 0 },\n    end: { line: 1, column: 1, offset: 0 }\n};\nfunction createPosition(line, column, offset) {\n    return { line, column, offset };\n}\nfunction createLocation(start, end, source) {\n    const loc = { start, end };\n    if (source != null) {\n        loc.source = source;\n    }\n    return loc;\n}\n\n/**\n * Original Utilities\n * written by kazuya kawaguchi\n */\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n    if (args.length === 1 && isObject(args[0])) {\n        args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n        args = {};\n    }\n    return message.replace(RE_ARGS, (match, identifier) => {\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\n    });\n}\nconst assign = Object.assign;\nconst isString = (val) => typeof val === 'string';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = (val) => val !== null && typeof val === 'object';\nfunction join(items, separator = '') {\n    return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');\n}\n\nconst CompileWarnCodes = {\n    USE_MODULO_SYNTAX: 1,\n    __EXTEND_POINT__: 2\n};\n/** @internal */\nconst warnMessages = {\n    [CompileWarnCodes.USE_MODULO_SYNTAX]: `Use modulo before '{{0}}'.`\n};\nfunction createCompileWarn(code, loc, ...args) {\n    const msg = format(warnMessages[code] || '', ...(args || [])) ;\n    const message = { message: String(msg), code };\n    if (loc) {\n        message.location = loc;\n    }\n    return message;\n}\n\nconst CompileErrorCodes = {\n    // tokenizer error codes\n    EXPECTED_TOKEN: 1,\n    INVALID_TOKEN_IN_PLACEHOLDER: 2,\n    UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\n    UNKNOWN_ESCAPE_SEQUENCE: 4,\n    INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\n    UNBALANCED_CLOSING_BRACE: 6,\n    UNTERMINATED_CLOSING_BRACE: 7,\n    EMPTY_PLACEHOLDER: 8,\n    NOT_ALLOW_NEST_PLACEHOLDER: 9,\n    INVALID_LINKED_FORMAT: 10,\n    // parser error codes\n    MUST_HAVE_MESSAGES_IN_PLURAL: 11,\n    UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\n    UNEXPECTED_EMPTY_LINKED_KEY: 13,\n    UNEXPECTED_LEXICAL_ANALYSIS: 14,\n    // generator error codes\n    UNHANDLED_CODEGEN_NODE_TYPE: 15,\n    // minifier error codes\n    UNHANDLED_MINIFIER_NODE_TYPE: 16,\n    // Special value for higher-order compilers to pick up the last code\n    // to avoid collision of error codes. This should always be kept as the last\n    // item.\n    __EXTEND_POINT__: 17\n};\n/** @internal */\nconst errorMessages = {\n    // tokenizer error messages\n    [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,\n    [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,\n    [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,\n    [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\\\{0}`,\n    [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,\n    [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,\n    [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,\n    [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,\n    [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,\n    [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,\n    // parser error messages\n    [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,\n    [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,\n    // generator error messages\n    [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,\n    // minimizer error messages\n    [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`\n};\nfunction createCompileError(code, loc, options = {}) {\n    const { domain, messages, args } = options;\n    const msg = format((messages || errorMessages)[code] || '', ...(args || []))\n        ;\n    const error = new SyntaxError(String(msg));\n    error.code = code;\n    if (loc) {\n        error.location = loc;\n    }\n    error.domain = domain;\n    return error;\n}\n/** @internal */\nfunction defaultOnError(error) {\n    throw error;\n}\n\n// eslint-disable-next-line no-useless-escape\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\nconst detectHtmlTag = (source) => RE_HTML_TAG.test(source);\n\nconst CHAR_SP = ' ';\nconst CHAR_CR = '\\r';\nconst CHAR_LF = '\\n';\nconst CHAR_LS = String.fromCharCode(0x2028);\nconst CHAR_PS = String.fromCharCode(0x2029);\nfunction createScanner(str) {\n    const _buf = str;\n    let _index = 0;\n    let _line = 1;\n    let _column = 1;\n    let _peekOffset = 0;\n    const isCRLF = (index) => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\n    const isLF = (index) => _buf[index] === CHAR_LF;\n    const isPS = (index) => _buf[index] === CHAR_PS;\n    const isLS = (index) => _buf[index] === CHAR_LS;\n    const isLineEnd = (index) => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\n    const index = () => _index;\n    const line = () => _line;\n    const column = () => _column;\n    const peekOffset = () => _peekOffset;\n    const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\n    const currentChar = () => charAt(_index);\n    const currentPeek = () => charAt(_index + _peekOffset);\n    function next() {\n        _peekOffset = 0;\n        if (isLineEnd(_index)) {\n            _line++;\n            _column = 0;\n        }\n        if (isCRLF(_index)) {\n            _index++;\n        }\n        _index++;\n        _column++;\n        return _buf[_index];\n    }\n    function peek() {\n        if (isCRLF(_index + _peekOffset)) {\n            _peekOffset++;\n        }\n        _peekOffset++;\n        return _buf[_index + _peekOffset];\n    }\n    function reset() {\n        _index = 0;\n        _line = 1;\n        _column = 1;\n        _peekOffset = 0;\n    }\n    function resetPeek(offset = 0) {\n        _peekOffset = offset;\n    }\n    function skipToPeek() {\n        const target = _index + _peekOffset;\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while (target !== _index) {\n            next();\n        }\n        _peekOffset = 0;\n    }\n    return {\n        index,\n        line,\n        column,\n        peekOffset,\n        charAt,\n        currentChar,\n        currentPeek,\n        next,\n        peek,\n        reset,\n        resetPeek,\n        skipToPeek\n    };\n}\n\nconst EOF = undefined;\nconst DOT = '.';\nconst LITERAL_DELIMITER = \"'\";\nconst ERROR_DOMAIN$3 = 'tokenizer';\nfunction createTokenizer(source, options = {}) {\n    const location = options.location !== false;\n    const _scnr = createScanner(source);\n    const currentOffset = () => _scnr.index();\n    const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\n    const _initLoc = currentPosition();\n    const _initOffset = currentOffset();\n    const _context = {\n        currentType: 14 /* TokenTypes.EOF */,\n        offset: _initOffset,\n        startLoc: _initLoc,\n        endLoc: _initLoc,\n        lastType: 14 /* TokenTypes.EOF */,\n        lastOffset: _initOffset,\n        lastStartLoc: _initLoc,\n        lastEndLoc: _initLoc,\n        braceNest: 0,\n        inLinked: false,\n        text: ''\n    };\n    const context = () => _context;\n    const { onError } = options;\n    function emitError(code, pos, offset, ...args) {\n        const ctx = context();\n        pos.column += offset;\n        pos.offset += offset;\n        if (onError) {\n            const loc = location ? createLocation(ctx.startLoc, pos) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$3,\n                args\n            });\n            onError(err);\n        }\n    }\n    function getToken(context, type, value) {\n        context.endLoc = currentPosition();\n        context.currentType = type;\n        const token = { type };\n        if (location) {\n            token.loc = createLocation(context.startLoc, context.endLoc);\n        }\n        if (value != null) {\n            token.value = value;\n        }\n        return token;\n    }\n    const getEndToken = (context) => getToken(context, 14 /* TokenTypes.EOF */);\n    function eat(scnr, ch) {\n        if (scnr.currentChar() === ch) {\n            scnr.next();\n            return ch;\n        }\n        else {\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n            return '';\n        }\n    }\n    function peekSpaces(scnr) {\n        let buf = '';\n        while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\n            buf += scnr.currentPeek();\n            scnr.peek();\n        }\n        return buf;\n    }\n    function skipSpaces(scnr) {\n        const buf = peekSpaces(scnr);\n        scnr.skipToPeek();\n        return buf;\n    }\n    function isIdentifierStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            cc === 95 // _\n        );\n    }\n    function isNumberStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function isNamedIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isListIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\n        const ret = isNumberStart(ch);\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLiteralStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === LITERAL_DELIMITER;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDotStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 8 /* TokenTypes.LinkedAlias */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \".\" /* TokenChars.LinkedDot */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedModifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 9 /* TokenTypes.LinkedDot */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDelimiterStart(scnr, context) {\n        const { currentType } = context;\n        if (!(currentType === 8 /* TokenTypes.LinkedAlias */ ||\n            currentType === 12 /* TokenTypes.LinkedModifier */)) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \":\" /* TokenChars.LinkedDelimiter */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedReferStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 10 /* TokenTypes.LinkedDelimiter */) {\n            return false;\n        }\n        const fn = () => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return isIdentifierStart(scnr.peek());\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"%\" /* TokenChars.Modulo */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \":\" /* TokenChars.LinkedDelimiter */ ||\n                ch === \".\" /* TokenChars.LinkedDot */ ||\n                ch === CHAR_SP ||\n                !ch) {\n                return false;\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn();\n            }\n            else {\n                // other characters\n                return isTextStart(scnr, false);\n            }\n        };\n        const ret = fn();\n        scnr.resetPeek();\n        return ret;\n    }\n    function isPluralStart(scnr) {\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \"|\" /* TokenChars.Pipe */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function detectModuloStart(scnr) {\n        const spaces = peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \"%\" /* TokenChars.Modulo */ &&\n            scnr.peek() === \"{\" /* TokenChars.BraceLeft */;\n        scnr.resetPeek();\n        return {\n            isModulo: ret,\n            hasSpace: spaces.length > 0\n        };\n    }\n    function isTextStart(scnr, reset = true) {\n        const fn = (hasSpace = false, prev = '', detectModulo = false) => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return prev === \"%\" /* TokenChars.Modulo */ ? false : hasSpace;\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ || !ch) {\n                return prev === \"%\" /* TokenChars.Modulo */ ? true : hasSpace;\n            }\n            else if (ch === \"%\" /* TokenChars.Modulo */) {\n                scnr.peek();\n                return fn(hasSpace, \"%\" /* TokenChars.Modulo */, true);\n            }\n            else if (ch === \"|\" /* TokenChars.Pipe */) {\n                return prev === \"%\" /* TokenChars.Modulo */ || detectModulo\n                    ? true\n                    : !(prev === CHAR_SP || prev === CHAR_LF);\n            }\n            else if (ch === CHAR_SP) {\n                scnr.peek();\n                return fn(true, CHAR_SP, detectModulo);\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn(true, CHAR_LF, detectModulo);\n            }\n            else {\n                return true;\n            }\n        };\n        const ret = fn();\n        reset && scnr.resetPeek();\n        return ret;\n    }\n    function takeChar(scnr, fn) {\n        const ch = scnr.currentChar();\n        if (ch === EOF) {\n            return EOF;\n        }\n        if (fn(ch)) {\n            scnr.next();\n            return ch;\n        }\n        return null;\n    }\n    function isIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 // $\n        );\n    }\n    function takeIdentifierChar(scnr) {\n        return takeChar(scnr, isIdentifier);\n    }\n    function isNamedIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 || // $\n            cc === 45 // -\n        );\n    }\n    function takeNamedIdentifierChar(scnr) {\n        return takeChar(scnr, isNamedIdentifier);\n    }\n    function isDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function takeDigit(scnr) {\n        return takeChar(scnr, isDigit);\n    }\n    function isHexDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 48 && cc <= 57) || // 0-9\n            (cc >= 65 && cc <= 70) || // A-F\n            (cc >= 97 && cc <= 102)); // a-f\n    }\n    function takeHexDigit(scnr) {\n        return takeChar(scnr, isHexDigit);\n    }\n    function getDigits(scnr) {\n        let ch = '';\n        let num = '';\n        while ((ch = takeDigit(scnr))) {\n            num += ch;\n        }\n        return num;\n    }\n    function readModulo(scnr) {\n        skipSpaces(scnr);\n        const ch = scnr.currentChar();\n        if (ch !== \"%\" /* TokenChars.Modulo */) {\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n        }\n        scnr.next();\n        return \"%\" /* TokenChars.Modulo */;\n    }\n    function readText(scnr) {\n        let buf = '';\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"}\" /* TokenChars.BraceRight */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                !ch) {\n                break;\n            }\n            else if (ch === \"%\" /* TokenChars.Modulo */) {\n                if (isTextStart(scnr)) {\n                    buf += ch;\n                    scnr.next();\n                }\n                else {\n                    break;\n                }\n            }\n            else if (ch === CHAR_SP || ch === CHAR_LF) {\n                if (isTextStart(scnr)) {\n                    buf += ch;\n                    scnr.next();\n                }\n                else if (isPluralStart(scnr)) {\n                    break;\n                }\n                else {\n                    buf += ch;\n                    scnr.next();\n                }\n            }\n            else {\n                buf += ch;\n                scnr.next();\n            }\n        }\n        return buf;\n    }\n    function readNamedIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let name = '';\n        while ((ch = takeNamedIdentifierChar(scnr))) {\n            name += ch;\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return name;\n    }\n    function readListIdentifier(scnr) {\n        skipSpaces(scnr);\n        let value = '';\n        if (scnr.currentChar() === '-') {\n            scnr.next();\n            value += `-${getDigits(scnr)}`;\n        }\n        else {\n            value += getDigits(scnr);\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return value;\n    }\n    function isLiteral(ch) {\n        return ch !== LITERAL_DELIMITER && ch !== CHAR_LF;\n    }\n    function readLiteral(scnr) {\n        skipSpaces(scnr);\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        let ch = '';\n        let literal = '';\n        while ((ch = takeChar(scnr, isLiteral))) {\n            if (ch === '\\\\') {\n                literal += readEscapeSequence(scnr);\n            }\n            else {\n                literal += ch;\n            }\n        }\n        const current = scnr.currentChar();\n        if (current === CHAR_LF || current === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\n            // TODO: Is it correct really?\n            if (current === CHAR_LF) {\n                scnr.next();\n                // eslint-disable-next-line no-useless-escape\n                eat(scnr, `\\'`);\n            }\n            return literal;\n        }\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        return literal;\n    }\n    function readEscapeSequence(scnr) {\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case '\\\\':\n            case `\\'`: // eslint-disable-line no-useless-escape\n                scnr.next();\n                return `\\\\${ch}`;\n            case 'u':\n                return readUnicodeEscapeSequence(scnr, ch, 4);\n            case 'U':\n                return readUnicodeEscapeSequence(scnr, ch, 6);\n            default:\n                emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\n                return '';\n        }\n    }\n    function readUnicodeEscapeSequence(scnr, unicode, digits) {\n        eat(scnr, unicode);\n        let sequence = '';\n        for (let i = 0; i < digits; i++) {\n            const ch = takeHexDigit(scnr);\n            if (!ch) {\n                emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\n                break;\n            }\n            sequence += ch;\n        }\n        return `\\\\${unicode}${sequence}`;\n    }\n    function isInvalidIdentifier(ch) {\n        return (ch !== \"{\" /* TokenChars.BraceLeft */ &&\n            ch !== \"}\" /* TokenChars.BraceRight */ &&\n            ch !== CHAR_SP &&\n            ch !== CHAR_LF);\n    }\n    function readInvalidIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let identifiers = '';\n        while ((ch = takeChar(scnr, isInvalidIdentifier))) {\n            identifiers += ch;\n        }\n        return identifiers;\n    }\n    function readLinkedModifier(scnr) {\n        let ch = '';\n        let name = '';\n        while ((ch = takeIdentifierChar(scnr))) {\n            name += ch;\n        }\n        return name;\n    }\n    function readLinkedRefer(scnr) {\n        const fn = (buf) => {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"%\" /* TokenChars.Modulo */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \"(\" /* TokenChars.ParenLeft */ ||\n                ch === \")\" /* TokenChars.ParenRight */ ||\n                !ch) {\n                return buf;\n            }\n            else if (ch === CHAR_SP) {\n                return buf;\n            }\n            else if (ch === CHAR_LF || ch === DOT) {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n            else {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n        };\n        return fn('');\n    }\n    function readPlural(scnr) {\n        skipSpaces(scnr);\n        const plural = eat(scnr, \"|\" /* TokenChars.Pipe */);\n        skipSpaces(scnr);\n        return plural;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInPlaceholder(scnr, context) {\n        let token = null;\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                if (context.braceNest >= 1) {\n                    emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 2 /* TokenTypes.BraceLeft */, \"{\" /* TokenChars.BraceLeft */);\n                skipSpaces(scnr);\n                context.braceNest++;\n                return token;\n            case \"}\" /* TokenChars.BraceRight */:\n                if (context.braceNest > 0 &&\n                    context.currentType === 2 /* TokenTypes.BraceLeft */) {\n                    emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n                context.braceNest--;\n                context.braceNest > 0 && skipSpaces(scnr);\n                if (context.inLinked && context.braceNest === 0) {\n                    context.inLinked = false;\n                }\n                return token;\n            case \"@\" /* TokenChars.LinkedAlias */:\n                if (context.braceNest > 0) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                }\n                token = readTokenInLinked(scnr, context) || getEndToken(context);\n                context.braceNest = 0;\n                return token;\n            default: {\n                let validNamedIdentifier = true;\n                let validListIdentifier = true;\n                let validLiteral = true;\n                if (isPluralStart(scnr)) {\n                    if (context.braceNest > 0) {\n                        emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    }\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (context.braceNest > 0 &&\n                    (context.currentType === 5 /* TokenTypes.Named */ ||\n                        context.currentType === 6 /* TokenTypes.List */ ||\n                        context.currentType === 7 /* TokenTypes.Literal */)) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    context.braceNest = 0;\n                    return readToken(scnr, context);\n                }\n                if ((validNamedIdentifier = isNamedIdentifierStart(scnr, context))) {\n                    token = getToken(context, 5 /* TokenTypes.Named */, readNamedIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validListIdentifier = isListIdentifierStart(scnr, context))) {\n                    token = getToken(context, 6 /* TokenTypes.List */, readListIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validLiteral = isLiteralStart(scnr, context))) {\n                    token = getToken(context, 7 /* TokenTypes.Literal */, readLiteral(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\n                    // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\n                    token = getToken(context, 13 /* TokenTypes.InvalidPlace */, readInvalidIdentifier(scnr));\n                    emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\n                    skipSpaces(scnr);\n                    return token;\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInLinked(scnr, context) {\n        const { currentType } = context;\n        let token = null;\n        const ch = scnr.currentChar();\n        if ((currentType === 8 /* TokenTypes.LinkedAlias */ ||\n            currentType === 9 /* TokenTypes.LinkedDot */ ||\n            currentType === 12 /* TokenTypes.LinkedModifier */ ||\n            currentType === 10 /* TokenTypes.LinkedDelimiter */) &&\n            (ch === CHAR_LF || ch === CHAR_SP)) {\n            emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n        }\n        switch (ch) {\n            case \"@\" /* TokenChars.LinkedAlias */:\n                scnr.next();\n                token = getToken(context, 8 /* TokenTypes.LinkedAlias */, \"@\" /* TokenChars.LinkedAlias */);\n                context.inLinked = true;\n                return token;\n            case \".\" /* TokenChars.LinkedDot */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 9 /* TokenTypes.LinkedDot */, \".\" /* TokenChars.LinkedDot */);\n            case \":\" /* TokenChars.LinkedDelimiter */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 10 /* TokenTypes.LinkedDelimiter */, \":\" /* TokenChars.LinkedDelimiter */);\n            default:\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (isLinkedDotStart(scnr, context) ||\n                    isLinkedDelimiterStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return readTokenInLinked(scnr, context);\n                }\n                if (isLinkedModifierStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return getToken(context, 12 /* TokenTypes.LinkedModifier */, readLinkedModifier(scnr));\n                }\n                if (isLinkedReferStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                        // scan the placeholder\n                        return readTokenInPlaceholder(scnr, context) || token;\n                    }\n                    else {\n                        return getToken(context, 11 /* TokenTypes.LinkedKey */, readLinkedRefer(scnr));\n                    }\n                }\n                if (currentType === 8 /* TokenTypes.LinkedAlias */) {\n                    emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n                }\n                context.braceNest = 0;\n                context.inLinked = false;\n                return readToken(scnr, context);\n        }\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readToken(scnr, context) {\n        let token = { type: 14 /* TokenTypes.EOF */ };\n        if (context.braceNest > 0) {\n            return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n        }\n        if (context.inLinked) {\n            return readTokenInLinked(scnr, context) || getEndToken(context);\n        }\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n            case \"}\" /* TokenChars.BraceRight */:\n                emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\n                scnr.next();\n                return getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n            case \"@\" /* TokenChars.LinkedAlias */:\n                return readTokenInLinked(scnr, context) || getEndToken(context);\n            default: {\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                const { isModulo, hasSpace } = detectModuloStart(scnr);\n                if (isModulo) {\n                    return hasSpace\n                        ? getToken(context, 0 /* TokenTypes.Text */, readText(scnr))\n                        : getToken(context, 4 /* TokenTypes.Modulo */, readModulo(scnr));\n                }\n                if (isTextStart(scnr)) {\n                    return getToken(context, 0 /* TokenTypes.Text */, readText(scnr));\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    function nextToken() {\n        const { currentType, offset, startLoc, endLoc } = _context;\n        _context.lastType = currentType;\n        _context.lastOffset = offset;\n        _context.lastStartLoc = startLoc;\n        _context.lastEndLoc = endLoc;\n        _context.offset = currentOffset();\n        _context.startLoc = currentPosition();\n        if (_scnr.currentChar() === EOF) {\n            return getToken(_context, 14 /* TokenTypes.EOF */);\n        }\n        return readToken(_scnr, _context);\n    }\n    return {\n        nextToken,\n        currentOffset,\n        currentPosition,\n        context\n    };\n}\n\nconst ERROR_DOMAIN$2 = 'parser';\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\n    switch (match) {\n        case `\\\\\\\\`:\n            return `\\\\`;\n        // eslint-disable-next-line no-useless-escape\n        case `\\\\\\'`:\n            // eslint-disable-next-line no-useless-escape\n            return `\\'`;\n        default: {\n            const codePoint = parseInt(codePoint4 || codePoint6, 16);\n            if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\n                return String.fromCodePoint(codePoint);\n            }\n            // invalid ...\n            // Replace them with U+FFFD REPLACEMENT CHARACTER.\n            return '�';\n        }\n    }\n}\nfunction createParser(options = {}) {\n    const location = options.location !== false;\n    const { onError, onWarn } = options;\n    function emitError(tokenzer, code, start, offset, ...args) {\n        const end = tokenzer.currentPosition();\n        end.offset += offset;\n        end.column += offset;\n        if (onError) {\n            const loc = location ? createLocation(start, end) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$2,\n                args\n            });\n            onError(err);\n        }\n    }\n    function emitWarn(tokenzer, code, start, offset, ...args) {\n        const end = tokenzer.currentPosition();\n        end.offset += offset;\n        end.column += offset;\n        if (onWarn) {\n            const loc = location ? createLocation(start, end) : null;\n            onWarn(createCompileWarn(code, loc, args));\n        }\n    }\n    function startNode(type, offset, loc) {\n        const node = { type };\n        if (location) {\n            node.start = offset;\n            node.end = offset;\n            node.loc = { start: loc, end: loc };\n        }\n        return node;\n    }\n    function endNode(node, offset, pos, type) {\n        if (type) {\n            node.type = type;\n        }\n        if (location) {\n            node.end = offset;\n            if (node.loc) {\n                node.loc.end = pos;\n            }\n        }\n    }\n    function parseText(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(3 /* NodeTypes.Text */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseList(tokenizer, index) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(5 /* NodeTypes.List */, offset, loc);\n        node.index = parseInt(index, 10);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseNamed(tokenizer, key, modulo) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(4 /* NodeTypes.Named */, offset, loc);\n        node.key = key;\n        if (modulo === true) {\n            node.modulo = true;\n        }\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLiteral(tokenizer, value) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(9 /* NodeTypes.Literal */, offset, loc);\n        node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinkedModifier(tokenizer) {\n        const token = tokenizer.nextToken();\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get linked dot loc\n        const node = startNode(8 /* NodeTypes.LinkedModifier */, offset, loc);\n        if (token.type !== 12 /* TokenTypes.LinkedModifier */) {\n            // empty modifier\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\n            node.value = '';\n            endNode(node, offset, loc);\n            return {\n                nextConsumeToken: token,\n                node\n            };\n        }\n        // check token\n        if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        node.value = token.value || '';\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node\n        };\n    }\n    function parseLinkedKey(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(7 /* NodeTypes.LinkedKey */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinked(tokenizer) {\n        const context = tokenizer.context();\n        const linkedNode = startNode(6 /* NodeTypes.Linked */, context.offset, context.startLoc);\n        let token = tokenizer.nextToken();\n        if (token.type === 9 /* TokenTypes.LinkedDot */) {\n            const parsed = parseLinkedModifier(tokenizer);\n            linkedNode.modifier = parsed.node;\n            token = parsed.nextConsumeToken || tokenizer.nextToken();\n        }\n        // asset check token\n        if (token.type !== 10 /* TokenTypes.LinkedDelimiter */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        token = tokenizer.nextToken();\n        // skip brace left\n        if (token.type === 2 /* TokenTypes.BraceLeft */) {\n            token = tokenizer.nextToken();\n        }\n        switch (token.type) {\n            case 11 /* TokenTypes.LinkedKey */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\n                break;\n            case 5 /* TokenTypes.Named */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseNamed(tokenizer, token.value || '');\n                break;\n            case 6 /* TokenTypes.List */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseList(tokenizer, token.value || '');\n                break;\n            case 7 /* TokenTypes.Literal */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLiteral(tokenizer, token.value || '');\n                break;\n            default: {\n                // empty key\n                emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\n                const nextContext = tokenizer.context();\n                const emptyLinkedKeyNode = startNode(7 /* NodeTypes.LinkedKey */, nextContext.offset, nextContext.startLoc);\n                emptyLinkedKeyNode.value = '';\n                endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\n                linkedNode.key = emptyLinkedKeyNode;\n                endNode(linkedNode, nextContext.offset, nextContext.startLoc);\n                return {\n                    nextConsumeToken: token,\n                    node: linkedNode\n                };\n            }\n        }\n        endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node: linkedNode\n        };\n    }\n    function parseMessage(tokenizer) {\n        const context = tokenizer.context();\n        const startOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? tokenizer.currentOffset()\n            : context.offset;\n        const startLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.endLoc\n            : context.startLoc;\n        const node = startNode(2 /* NodeTypes.Message */, startOffset, startLoc);\n        node.items = [];\n        let nextToken = null;\n        let modulo = null;\n        do {\n            const token = nextToken || tokenizer.nextToken();\n            nextToken = null;\n            switch (token.type) {\n                case 0 /* TokenTypes.Text */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseText(tokenizer, token.value || ''));\n                    break;\n                case 6 /* TokenTypes.List */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseList(tokenizer, token.value || ''));\n                    break;\n                case 4 /* TokenTypes.Modulo */:\n                    modulo = true;\n                    break;\n                case 5 /* TokenTypes.Named */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseNamed(tokenizer, token.value || '', !!modulo));\n                    if (modulo) {\n                        emitWarn(tokenizer, CompileWarnCodes.USE_MODULO_SYNTAX, context.lastStartLoc, 0, getTokenCaption(token));\n                        modulo = null;\n                    }\n                    break;\n                case 7 /* TokenTypes.Literal */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseLiteral(tokenizer, token.value || ''));\n                    break;\n                case 8 /* TokenTypes.LinkedAlias */: {\n                    const parsed = parseLinked(tokenizer);\n                    node.items.push(parsed.node);\n                    nextToken = parsed.nextConsumeToken || null;\n                    break;\n                }\n            }\n        } while (context.currentType !== 14 /* TokenTypes.EOF */ &&\n            context.currentType !== 1 /* TokenTypes.Pipe */);\n        // adjust message node loc\n        const endOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastOffset\n            : tokenizer.currentOffset();\n        const endLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastEndLoc\n            : tokenizer.currentPosition();\n        endNode(node, endOffset, endLoc);\n        return node;\n    }\n    function parsePlural(tokenizer, offset, loc, msgNode) {\n        const context = tokenizer.context();\n        let hasEmptyMessage = msgNode.items.length === 0;\n        const node = startNode(1 /* NodeTypes.Plural */, offset, loc);\n        node.cases = [];\n        node.cases.push(msgNode);\n        do {\n            const msg = parseMessage(tokenizer);\n            if (!hasEmptyMessage) {\n                hasEmptyMessage = msg.items.length === 0;\n            }\n            node.cases.push(msg);\n        } while (context.currentType !== 14 /* TokenTypes.EOF */);\n        if (hasEmptyMessage) {\n            emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseResource(tokenizer) {\n        const context = tokenizer.context();\n        const { offset, startLoc } = context;\n        const msgNode = parseMessage(tokenizer);\n        if (context.currentType === 14 /* TokenTypes.EOF */) {\n            return msgNode;\n        }\n        else {\n            return parsePlural(tokenizer, offset, startLoc, msgNode);\n        }\n    }\n    function parse(source) {\n        const tokenizer = createTokenizer(source, assign({}, options));\n        const context = tokenizer.context();\n        const node = startNode(0 /* NodeTypes.Resource */, context.offset, context.startLoc);\n        if (location && node.loc) {\n            node.loc.source = source;\n        }\n        node.body = parseResource(tokenizer);\n        if (options.onCacheKey) {\n            node.cacheKey = options.onCacheKey(source);\n        }\n        // assert whether achieved to EOF\n        if (context.currentType !== 14 /* TokenTypes.EOF */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    return { parse };\n}\nfunction getTokenCaption(token) {\n    if (token.type === 14 /* TokenTypes.EOF */) {\n        return 'EOF';\n    }\n    const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\n    return name.length > 10 ? name.slice(0, 9) + '…' : name;\n}\n\nfunction createTransformer(ast, options = {} // eslint-disable-line\n) {\n    const _context = {\n        ast,\n        helpers: new Set()\n    };\n    const context = () => _context;\n    const helper = (name) => {\n        _context.helpers.add(name);\n        return name;\n    };\n    return { context, helper };\n}\nfunction traverseNodes(nodes, transformer) {\n    for (let i = 0; i < nodes.length; i++) {\n        traverseNode(nodes[i], transformer);\n    }\n}\nfunction traverseNode(node, transformer) {\n    // TODO: if we need pre-hook of transform, should be implemented to here\n    switch (node.type) {\n        case 1 /* NodeTypes.Plural */:\n            traverseNodes(node.cases, transformer);\n            transformer.helper(\"plural\" /* HelperNameMap.PLURAL */);\n            break;\n        case 2 /* NodeTypes.Message */:\n            traverseNodes(node.items, transformer);\n            break;\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            traverseNode(linked.key, transformer);\n            transformer.helper(\"linked\" /* HelperNameMap.LINKED */);\n            transformer.helper(\"type\" /* HelperNameMap.TYPE */);\n            break;\n        }\n        case 5 /* NodeTypes.List */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"list\" /* HelperNameMap.LIST */);\n            break;\n        case 4 /* NodeTypes.Named */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"named\" /* HelperNameMap.NAMED */);\n            break;\n    }\n    // TODO: if we need post-hook of transform, should be implemented to here\n}\n// transform AST\nfunction transform(ast, options = {} // eslint-disable-line\n) {\n    const transformer = createTransformer(ast);\n    transformer.helper(\"normalize\" /* HelperNameMap.NORMALIZE */);\n    // traverse\n    ast.body && traverseNode(ast.body, transformer);\n    // set meta information\n    const context = transformer.context();\n    ast.helpers = Array.from(context.helpers);\n}\n\nfunction optimize(ast) {\n    const body = ast.body;\n    if (body.type === 2 /* NodeTypes.Message */) {\n        optimizeMessageNode(body);\n    }\n    else {\n        body.cases.forEach(c => optimizeMessageNode(c));\n    }\n    return ast;\n}\nfunction optimizeMessageNode(message) {\n    if (message.items.length === 1) {\n        const item = message.items[0];\n        if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n            message.static = item.value;\n            delete item.value; // optimization for size\n        }\n    }\n    else {\n        const values = [];\n        for (let i = 0; i < message.items.length; i++) {\n            const item = message.items[i];\n            if (!(item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */)) {\n                break;\n            }\n            if (item.value == null) {\n                break;\n            }\n            values.push(item.value);\n        }\n        if (values.length === message.items.length) {\n            message.static = join(values);\n            for (let i = 0; i < message.items.length; i++) {\n                const item = message.items[i];\n                if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n                    delete item.value; // optimization for size\n                }\n            }\n        }\n    }\n}\n\nconst ERROR_DOMAIN$1 = 'minifier';\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction minify(node) {\n    node.t = node.type;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */: {\n            const resource = node;\n            minify(resource.body);\n            resource.b = resource.body;\n            delete resource.body;\n            break;\n        }\n        case 1 /* NodeTypes.Plural */: {\n            const plural = node;\n            const cases = plural.cases;\n            for (let i = 0; i < cases.length; i++) {\n                minify(cases[i]);\n            }\n            plural.c = cases;\n            delete plural.cases;\n            break;\n        }\n        case 2 /* NodeTypes.Message */: {\n            const message = node;\n            const items = message.items;\n            for (let i = 0; i < items.length; i++) {\n                minify(items[i]);\n            }\n            message.i = items;\n            delete message.items;\n            if (message.static) {\n                message.s = message.static;\n                delete message.static;\n            }\n            break;\n        }\n        case 3 /* NodeTypes.Text */:\n        case 9 /* NodeTypes.Literal */:\n        case 8 /* NodeTypes.LinkedModifier */:\n        case 7 /* NodeTypes.LinkedKey */: {\n            const valueNode = node;\n            if (valueNode.value) {\n                valueNode.v = valueNode.value;\n                delete valueNode.value;\n            }\n            break;\n        }\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            minify(linked.key);\n            linked.k = linked.key;\n            delete linked.key;\n            if (linked.modifier) {\n                minify(linked.modifier);\n                linked.m = linked.modifier;\n                delete linked.modifier;\n            }\n            break;\n        }\n        case 5 /* NodeTypes.List */: {\n            const list = node;\n            list.i = list.index;\n            delete list.index;\n            break;\n        }\n        case 4 /* NodeTypes.Named */: {\n            const named = node;\n            named.k = named.key;\n            delete named.key;\n            break;\n        }\n        default:\n            {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN$1,\n                    args: [node.type]\n                });\n            }\n    }\n    delete node.type;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference types=\"source-map-js\" />\nconst ERROR_DOMAIN = 'parser';\nfunction createCodeGenerator(ast, options) {\n    const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;\n    const location = options.location !== false;\n    const _context = {\n        filename,\n        code: '',\n        column: 1,\n        line: 1,\n        offset: 0,\n        map: undefined,\n        breakLineCode,\n        needIndent: _needIndent,\n        indentLevel: 0\n    };\n    if (location && ast.loc) {\n        _context.source = ast.loc.source;\n    }\n    const context = () => _context;\n    function push(code, node) {\n        _context.code += code;\n    }\n    function _newline(n, withBreakLine = true) {\n        const _breakLineCode = withBreakLine ? breakLineCode : '';\n        push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\n    }\n    function indent(withNewLine = true) {\n        const level = ++_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function deindent(withNewLine = true) {\n        const level = --_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function newline() {\n        _newline(_context.indentLevel);\n    }\n    const helper = (key) => `_${key}`;\n    const needIndent = () => _context.needIndent;\n    return {\n        context,\n        push,\n        indent,\n        deindent,\n        newline,\n        helper,\n        needIndent\n    };\n}\nfunction generateLinkedNode(generator, node) {\n    const { helper } = generator;\n    generator.push(`${helper(\"linked\" /* HelperNameMap.LINKED */)}(`);\n    generateNode(generator, node.key);\n    if (node.modifier) {\n        generator.push(`, `);\n        generateNode(generator, node.modifier);\n        generator.push(`, _type`);\n    }\n    else {\n        generator.push(`, undefined, _type`);\n    }\n    generator.push(`)`);\n}\nfunction generateMessageNode(generator, node) {\n    const { helper, needIndent } = generator;\n    generator.push(`${helper(\"normalize\" /* HelperNameMap.NORMALIZE */)}([`);\n    generator.indent(needIndent());\n    const length = node.items.length;\n    for (let i = 0; i < length; i++) {\n        generateNode(generator, node.items[i]);\n        if (i === length - 1) {\n            break;\n        }\n        generator.push(', ');\n    }\n    generator.deindent(needIndent());\n    generator.push('])');\n}\nfunction generatePluralNode(generator, node) {\n    const { helper, needIndent } = generator;\n    if (node.cases.length > 1) {\n        generator.push(`${helper(\"plural\" /* HelperNameMap.PLURAL */)}([`);\n        generator.indent(needIndent());\n        const length = node.cases.length;\n        for (let i = 0; i < length; i++) {\n            generateNode(generator, node.cases[i]);\n            if (i === length - 1) {\n                break;\n            }\n            generator.push(', ');\n        }\n        generator.deindent(needIndent());\n        generator.push(`])`);\n    }\n}\nfunction generateResource(generator, node) {\n    if (node.body) {\n        generateNode(generator, node.body);\n    }\n    else {\n        generator.push('null');\n    }\n}\nfunction generateNode(generator, node) {\n    const { helper } = generator;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */:\n            generateResource(generator, node);\n            break;\n        case 1 /* NodeTypes.Plural */:\n            generatePluralNode(generator, node);\n            break;\n        case 2 /* NodeTypes.Message */:\n            generateMessageNode(generator, node);\n            break;\n        case 6 /* NodeTypes.Linked */:\n            generateLinkedNode(generator, node);\n            break;\n        case 8 /* NodeTypes.LinkedModifier */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 7 /* NodeTypes.LinkedKey */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 5 /* NodeTypes.List */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"list\" /* HelperNameMap.LIST */)}(${node.index}))`, node);\n            break;\n        case 4 /* NodeTypes.Named */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"named\" /* HelperNameMap.NAMED */)}(${JSON.stringify(node.key)}))`, node);\n            break;\n        case 9 /* NodeTypes.Literal */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 3 /* NodeTypes.Text */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        default:\n            {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN,\n                    args: [node.type]\n                });\n            }\n    }\n}\n// generate code from AST\nconst generate = (ast, options = {} // eslint-disable-line\n) => {\n    const mode = isString(options.mode) ? options.mode : 'normal';\n    const filename = isString(options.filename)\n        ? options.filename\n        : 'message.intl';\n    const sourceMap = !!options.sourceMap;\n    // prettier-ignore\n    const breakLineCode = options.breakLineCode != null\n        ? options.breakLineCode\n        : mode === 'arrow'\n            ? ';'\n            : '\\n';\n    const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\n    const helpers = ast.helpers || [];\n    const generator = createCodeGenerator(ast, {\n        mode,\n        filename,\n        sourceMap,\n        breakLineCode,\n        needIndent\n    });\n    generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\n    generator.indent(needIndent);\n    if (helpers.length > 0) {\n        generator.push(`const { ${join(helpers.map(s => `${s}: _${s}`), ', ')} } = ctx`);\n        generator.newline();\n    }\n    generator.push(`return `);\n    generateNode(generator, ast);\n    generator.deindent(needIndent);\n    generator.push(`}`);\n    delete ast.helpers;\n    const { code, map } = generator.context();\n    return {\n        ast,\n        code,\n        map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\n    };\n};\n\nfunction baseCompile(source, options = {}) {\n    const assignedOptions = assign({}, options);\n    const jit = !!assignedOptions.jit;\n    const enalbeMinify = !!assignedOptions.minify;\n    const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;\n    // parse source codes\n    const parser = createParser(assignedOptions);\n    const ast = parser.parse(source);\n    if (!jit) {\n        // transform ASTs\n        transform(ast, assignedOptions);\n        // generate javascript codes\n        return generate(ast, assignedOptions);\n    }\n    else {\n        // optimize ASTs\n        enambeOptimize && optimize(ast);\n        // minimize ASTs\n        enalbeMinify && minify(ast);\n        // In JIT mode, no ast transform, no code generation.\n        return { ast, code: '' };\n    }\n}\n\nexport { CompileErrorCodes, CompileWarnCodes, ERROR_DOMAIN$2 as ERROR_DOMAIN, LOCATION_STUB, baseCompile, createCompileError, createCompileWarn, createLocation, createParser, createPosition, defaultOnError, detectHtmlTag, errorMessages, warnMessages };\n", "/*!\n  * core-base v9.14.3\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, isObject, isFunction, isString, create, isNumber, isPlainObject, assign, join, toDisplayString, isArray, incrementer, format as format$1, isPromise, isBoolean, warn, isRegExp, warnOnce, hasOwn, escapeHtml, inBrowser, mark, measure, isEmptyObject, generateCode<PERSON>rame, generateFormatCacheKey, isDate } from '@intlify/shared';\nimport { CompileWarnCodes, CompileErrorCodes, createCompileError, detectHtmlTag, defaultOnError, baseCompile as baseCompile$1 } from '@intlify/message-compiler';\nexport { CompileErrorCodes, createCompileError } from '@intlify/message-compiler';\n\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n    }\n    if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n    }\n    if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n    }\n}\n\nconst pathStateMachine =  [];\npathStateMachine[0 /* States.BEFORE_PATH */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [0 /* States.BEFORE_PATH */],\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[1 /* States.IN_PATH */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */],\n    [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[2 /* States.BEFORE_IDENT */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [2 /* States.BEFORE_IDENT */],\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */]\n};\npathStateMachine[3 /* States.IN_IDENT */] = {\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */, 1 /* Actions.PUSH */],\n    [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */, 1 /* Actions.PUSH */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */, 1 /* Actions.PUSH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */, 1 /* Actions.PUSH */]\n};\npathStateMachine[4 /* States.IN_SUB_PATH */] = {\n    [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */],\n    [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [\n        4 /* States.IN_SUB_PATH */,\n        2 /* Actions.INC_SUB_PATH_DEPTH */\n    ],\n    [\"]\" /* PathCharTypes.RIGHT_BRACKET */]: [1 /* States.IN_PATH */, 3 /* Actions.PUSH_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */]\n};\npathStateMachine[5 /* States.IN_SINGLE_QUOTE */] = {\n    [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */]\n};\npathStateMachine[6 /* States.IN_DOUBLE_QUOTE */] = {\n    [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */]\n};\n/**\n * Check if an expression is a literal value.\n */\nconst literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\nfunction isLiteral(exp) {\n    return literalValueRE.test(exp);\n}\n/**\n * Strip quotes from a string\n */\nfunction stripQuotes(str) {\n    const a = str.charCodeAt(0);\n    const b = str.charCodeAt(str.length - 1);\n    return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;\n}\n/**\n * Determine the type of a character in a keypath.\n */\nfunction getPathCharType(ch) {\n    if (ch === undefined || ch === null) {\n        return \"o\" /* PathCharTypes.END_OF_FAIL */;\n    }\n    const code = ch.charCodeAt(0);\n    switch (code) {\n        case 0x5b: // [\n        case 0x5d: // ]\n        case 0x2e: // .\n        case 0x22: // \"\n        case 0x27: // '\n            return ch;\n        case 0x5f: // _\n        case 0x24: // $\n        case 0x2d: // -\n            return \"i\" /* PathCharTypes.IDENT */;\n        case 0x09: // Tab (HT)\n        case 0x0a: // Newline (LF)\n        case 0x0d: // Return (CR)\n        case 0xa0: // No-break space (NBSP)\n        case 0xfeff: // Byte Order Mark (BOM)\n        case 0x2028: // Line Separator (LS)\n        case 0x2029: // Paragraph Separator (PS)\n            return \"w\" /* PathCharTypes.WORKSPACE */;\n    }\n    return \"i\" /* PathCharTypes.IDENT */;\n}\n/**\n * Format a subPath, return its plain form if it is\n * a literal string or number. Otherwise prepend the\n * dynamic indicator (*).\n */\nfunction formatSubPath(path) {\n    const trimmed = path.trim();\n    // invalid leading 0\n    if (path.charAt(0) === '0' && isNaN(parseInt(path))) {\n        return false;\n    }\n    return isLiteral(trimmed)\n        ? stripQuotes(trimmed)\n        : \"*\" /* PathCharTypes.ASTARISK */ + trimmed;\n}\n/**\n * Parse a string path into an array of segments\n */\nfunction parse(path) {\n    const keys = [];\n    let index = -1;\n    let mode = 0 /* States.BEFORE_PATH */;\n    let subPathDepth = 0;\n    let c;\n    let key; // eslint-disable-line\n    let newChar;\n    let type;\n    let transition;\n    let action;\n    let typeMap;\n    const actions = [];\n    actions[0 /* Actions.APPEND */] = () => {\n        if (key === undefined) {\n            key = newChar;\n        }\n        else {\n            key += newChar;\n        }\n    };\n    actions[1 /* Actions.PUSH */] = () => {\n        if (key !== undefined) {\n            keys.push(key);\n            key = undefined;\n        }\n    };\n    actions[2 /* Actions.INC_SUB_PATH_DEPTH */] = () => {\n        actions[0 /* Actions.APPEND */]();\n        subPathDepth++;\n    };\n    actions[3 /* Actions.PUSH_SUB_PATH */] = () => {\n        if (subPathDepth > 0) {\n            subPathDepth--;\n            mode = 4 /* States.IN_SUB_PATH */;\n            actions[0 /* Actions.APPEND */]();\n        }\n        else {\n            subPathDepth = 0;\n            if (key === undefined) {\n                return false;\n            }\n            key = formatSubPath(key);\n            if (key === false) {\n                return false;\n            }\n            else {\n                actions[1 /* Actions.PUSH */]();\n            }\n        }\n    };\n    function maybeUnescapeQuote() {\n        const nextChar = path[index + 1];\n        if ((mode === 5 /* States.IN_SINGLE_QUOTE */ &&\n            nextChar === \"'\" /* PathCharTypes.SINGLE_QUOTE */) ||\n            (mode === 6 /* States.IN_DOUBLE_QUOTE */ &&\n                nextChar === \"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */)) {\n            index++;\n            newChar = '\\\\' + nextChar;\n            actions[0 /* Actions.APPEND */]();\n            return true;\n        }\n    }\n    while (mode !== null) {\n        index++;\n        c = path[index];\n        if (c === '\\\\' && maybeUnescapeQuote()) {\n            continue;\n        }\n        type = getPathCharType(c);\n        typeMap = pathStateMachine[mode];\n        transition = typeMap[type] || typeMap[\"l\" /* PathCharTypes.ELSE */] || 8 /* States.ERROR */;\n        // check parse error\n        if (transition === 8 /* States.ERROR */) {\n            return;\n        }\n        mode = transition[0];\n        if (transition[1] !== undefined) {\n            action = actions[transition[1]];\n            if (action) {\n                newChar = c;\n                if (action() === false) {\n                    return;\n                }\n            }\n        }\n        // check parse finish\n        if (mode === 7 /* States.AFTER_PATH */) {\n            return keys;\n        }\n    }\n}\n// path token cache\nconst cache = new Map();\n/**\n * key-value message resolver\n *\n * @remarks\n * Resolves messages with the key-value structure. Note that messages with a hierarchical structure such as objects cannot be resolved\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveWithKeyValue(obj, path) {\n    return isObject(obj) ? obj[path] : null;\n}\n/**\n * message resolver\n *\n * @remarks\n * Resolves messages. messages with a hierarchical structure such as objects can be resolved. This resolver is used in VueI18n as default.\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveValue$1(obj, path) {\n    // check object\n    if (!isObject(obj)) {\n        return null;\n    }\n    // parse path\n    let hit = cache.get(path);\n    if (!hit) {\n        hit = parse(path);\n        if (hit) {\n            cache.set(path, hit);\n        }\n    }\n    // check hit\n    if (!hit) {\n        return null;\n    }\n    // resolve path value\n    const len = hit.length;\n    let last = obj;\n    let i = 0;\n    while (i < len) {\n        const val = last[hit[i]];\n        if (val === undefined) {\n            return null;\n        }\n        if (isFunction(last)) {\n            return null;\n        }\n        last = val;\n        i++;\n    }\n    return last;\n}\n\nconst DEFAULT_MODIFIER = (str) => str;\nconst DEFAULT_MESSAGE = (ctx) => ''; // eslint-disable-line\nconst DEFAULT_MESSAGE_DATA_TYPE = 'text';\nconst DEFAULT_NORMALIZE = (values) => values.length === 0 ? '' : join(values);\nconst DEFAULT_INTERPOLATE = toDisplayString;\nfunction pluralDefault(choice, choicesLength) {\n    choice = Math.abs(choice);\n    if (choicesLength === 2) {\n        // prettier-ignore\n        return choice\n            ? choice > 1\n                ? 1\n                : 0\n            : 1;\n    }\n    return choice ? Math.min(choice, 2) : 0;\n}\nfunction getPluralIndex(options) {\n    // prettier-ignore\n    const index = isNumber(options.pluralIndex)\n        ? options.pluralIndex\n        : -1;\n    // prettier-ignore\n    return options.named && (isNumber(options.named.count) || isNumber(options.named.n))\n        ? isNumber(options.named.count)\n            ? options.named.count\n            : isNumber(options.named.n)\n                ? options.named.n\n                : index\n        : index;\n}\nfunction normalizeNamed(pluralIndex, props) {\n    if (!props.count) {\n        props.count = pluralIndex;\n    }\n    if (!props.n) {\n        props.n = pluralIndex;\n    }\n}\nfunction createMessageContext(options = {}) {\n    const locale = options.locale;\n    const pluralIndex = getPluralIndex(options);\n    const pluralRule = isObject(options.pluralRules) &&\n        isString(locale) &&\n        isFunction(options.pluralRules[locale])\n        ? options.pluralRules[locale]\n        : pluralDefault;\n    const orgPluralRule = isObject(options.pluralRules) &&\n        isString(locale) &&\n        isFunction(options.pluralRules[locale])\n        ? pluralDefault\n        : undefined;\n    const plural = (messages) => {\n        return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];\n    };\n    const _list = options.list || [];\n    const list = (index) => _list[index];\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const _named = options.named || create();\n    isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);\n    const named = (key) => _named[key];\n    function message(key) {\n        // prettier-ignore\n        const msg = isFunction(options.messages)\n            ? options.messages(key)\n            : isObject(options.messages)\n                ? options.messages[key]\n                : false;\n        return !msg\n            ? options.parent\n                ? options.parent.message(key) // resolve from parent messages\n                : DEFAULT_MESSAGE\n            : msg;\n    }\n    const _modifier = (name) => options.modifiers\n        ? options.modifiers[name]\n        : DEFAULT_MODIFIER;\n    const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize)\n        ? options.processor.normalize\n        : DEFAULT_NORMALIZE;\n    const interpolate = isPlainObject(options.processor) &&\n        isFunction(options.processor.interpolate)\n        ? options.processor.interpolate\n        : DEFAULT_INTERPOLATE;\n    const type = isPlainObject(options.processor) && isString(options.processor.type)\n        ? options.processor.type\n        : DEFAULT_MESSAGE_DATA_TYPE;\n    const linked = (key, ...args) => {\n        const [arg1, arg2] = args;\n        let type = 'text';\n        let modifier = '';\n        if (args.length === 1) {\n            if (isObject(arg1)) {\n                modifier = arg1.modifier || modifier;\n                type = arg1.type || type;\n            }\n            else if (isString(arg1)) {\n                modifier = arg1 || modifier;\n            }\n        }\n        else if (args.length === 2) {\n            if (isString(arg1)) {\n                modifier = arg1 || modifier;\n            }\n            if (isString(arg2)) {\n                type = arg2 || type;\n            }\n        }\n        const ret = message(key)(ctx);\n        const msg = \n        // The message in vnode resolved with linked are returned as an array by processor.nomalize\n        type === 'vnode' && isArray(ret) && modifier\n            ? ret[0]\n            : ret;\n        return modifier ? _modifier(modifier)(msg, type) : msg;\n    };\n    const ctx = {\n        [\"list\" /* HelperNameMap.LIST */]: list,\n        [\"named\" /* HelperNameMap.NAMED */]: named,\n        [\"plural\" /* HelperNameMap.PLURAL */]: plural,\n        [\"linked\" /* HelperNameMap.LINKED */]: linked,\n        [\"message\" /* HelperNameMap.MESSAGE */]: message,\n        [\"type\" /* HelperNameMap.TYPE */]: type,\n        [\"interpolate\" /* HelperNameMap.INTERPOLATE */]: interpolate,\n        [\"normalize\" /* HelperNameMap.NORMALIZE */]: normalize,\n        [\"values\" /* HelperNameMap.VALUES */]: assign(create(), _list, _named)\n    };\n    return ctx;\n}\n\nlet devtools = null;\nfunction setDevToolsHook(hook) {\n    devtools = hook;\n}\nfunction getDevToolsHook() {\n    return devtools;\n}\nfunction initI18nDevTools(i18n, version, meta) {\n    // TODO: queue if devtools is undefined\n    devtools &&\n        devtools.emit(\"i18n:init\" /* IntlifyDevToolsHooks.I18nInit */, {\n            timestamp: Date.now(),\n            i18n,\n            version,\n            meta\n        });\n}\nconst translateDevTools = /* #__PURE__*/ createDevToolsHook(\"function:translate\" /* IntlifyDevToolsHooks.FunctionTranslate */);\nfunction createDevToolsHook(hook) {\n    return (payloads) => devtools && devtools.emit(hook, payloads);\n}\n\nconst code$1 = CompileWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst CoreWarnCodes = {\n    NOT_FOUND_KEY: code$1, // 2\n    FALLBACK_TO_TRANSLATE: inc$1(), // 3\n    CANNOT_FORMAT_NUMBER: inc$1(), // 4\n    FALLBACK_TO_NUMBER_FORMAT: inc$1(), // 5\n    CANNOT_FORMAT_DATE: inc$1(), // 6\n    FALLBACK_TO_DATE_FORMAT: inc$1(), // 7\n    EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: inc$1(), // 8\n    __EXTEND_POINT__: inc$1() // 9\n};\n/** @internal */\nconst warnMessages = {\n    [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,\n    [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,\n    [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,\n    [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`\n};\nfunction getWarnMessage(code, ...args) {\n    return format$1(warnMessages[code], ...args);\n}\n\nconst code = CompileErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst CoreErrorCodes = {\n    INVALID_ARGUMENT: code, // 17\n    INVALID_DATE_ARGUMENT: inc(), // 18\n    INVALID_ISO_DATE_ARGUMENT: inc(), // 19\n    NOT_SUPPORT_NON_STRING_MESSAGE: inc(), // 20\n    NOT_SUPPORT_LOCALE_PROMISE_VALUE: inc(), // 21\n    NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: inc(), // 22\n    NOT_SUPPORT_LOCALE_TYPE: inc(), // 23\n    __EXTEND_POINT__: inc() // 24\n};\nfunction createCoreError(code) {\n    return createCompileError(code, null, (process.env.NODE_ENV !== 'production') ? { messages: errorMessages } : undefined);\n}\n/** @internal */\nconst errorMessages = {\n    [CoreErrorCodes.INVALID_ARGUMENT]: 'Invalid arguments',\n    [CoreErrorCodes.INVALID_DATE_ARGUMENT]: 'The date provided is an invalid Date object.' +\n        'Make sure your Date represents a valid date.',\n    [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: 'The argument provided is not a valid ISO date string',\n    [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: 'Not support non-string message',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: 'cannot support promise value',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: 'cannot support async function',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: 'cannot support locale type'\n};\n\n/** @internal */\nfunction getLocale(context, options) {\n    return options.locale != null\n        ? resolveLocale(options.locale)\n        : resolveLocale(context.locale);\n}\nlet _resolveLocale;\n/** @internal */\nfunction resolveLocale(locale) {\n    if (isString(locale)) {\n        return locale;\n    }\n    else {\n        if (isFunction(locale)) {\n            if (locale.resolvedOnce && _resolveLocale != null) {\n                return _resolveLocale;\n            }\n            else if (locale.constructor.name === 'Function') {\n                const resolve = locale();\n                if (isPromise(resolve)) {\n                    throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);\n                }\n                return (_resolveLocale = resolve);\n            }\n            else {\n                throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);\n            }\n        }\n        else {\n            throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);\n        }\n    }\n}\n/**\n * Fallback with simple implemenation\n *\n * @remarks\n * A fallback locale function implemented with a simple fallback algorithm.\n *\n * Basically, it returns the value as specified in the `fallbackLocale` props, and is processed with the fallback inside intlify.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithSimple(ctx, fallback, start // eslint-disable-line @typescript-eslint/no-unused-vars\n) {\n    // prettier-ignore\n    return [...new Set([\n            start,\n            ...(isArray(fallback)\n                ? fallback\n                : isObject(fallback)\n                    ? Object.keys(fallback)\n                    : isString(fallback)\n                        ? [fallback]\n                        : [start])\n        ])];\n}\n/**\n * Fallback with locale chain\n *\n * @remarks\n * A fallback locale function implemented with a fallback chain algorithm. It's used in VueI18n as default.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nSee [Fallbacking](../guide/essentials/fallback)\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithLocaleChain(ctx, fallback, start) {\n    const startLocale = isString(start) ? start : DEFAULT_LOCALE;\n    const context = ctx;\n    if (!context.__localeChainCache) {\n        context.__localeChainCache = new Map();\n    }\n    let chain = context.__localeChainCache.get(startLocale);\n    if (!chain) {\n        chain = [];\n        // first block defined by start\n        let block = [start];\n        // while any intervening block found\n        while (isArray(block)) {\n            block = appendBlockToChain(chain, block, fallback);\n        }\n        // prettier-ignore\n        // last block defined by default\n        const defaults = isArray(fallback) || !isPlainObject(fallback)\n            ? fallback\n            : fallback['default']\n                ? fallback['default']\n                : null;\n        // convert defaults to array\n        block = isString(defaults) ? [defaults] : defaults;\n        if (isArray(block)) {\n            appendBlockToChain(chain, block, false);\n        }\n        context.__localeChainCache.set(startLocale, chain);\n    }\n    return chain;\n}\nfunction appendBlockToChain(chain, block, blocks) {\n    let follow = true;\n    for (let i = 0; i < block.length && isBoolean(follow); i++) {\n        const locale = block[i];\n        if (isString(locale)) {\n            follow = appendLocaleToChain(chain, block[i], blocks);\n        }\n    }\n    return follow;\n}\nfunction appendLocaleToChain(chain, locale, blocks) {\n    let follow;\n    const tokens = locale.split('-');\n    do {\n        const target = tokens.join('-');\n        follow = appendItemToChain(chain, target, blocks);\n        tokens.splice(-1, 1);\n    } while (tokens.length && follow === true);\n    return follow;\n}\nfunction appendItemToChain(chain, target, blocks) {\n    let follow = false;\n    if (!chain.includes(target)) {\n        follow = true;\n        if (target) {\n            follow = target[target.length - 1] !== '!';\n            const locale = target.replace(/!/g, '');\n            chain.push(locale);\n            if ((isArray(blocks) || isPlainObject(blocks)) &&\n                blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any\n            ) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                follow = blocks[locale];\n            }\n        }\n    }\n    return follow;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Intlify core-base version\n * @internal\n */\nconst VERSION = '9.14.3';\nconst NOT_REOSLVED = -1;\nconst DEFAULT_LOCALE = 'en-US';\nconst MISSING_RESOLVE_VALUE = '';\nconst capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;\nfunction getDefaultLinkedModifiers() {\n    return {\n        upper: (val, type) => {\n            // prettier-ignore\n            return type === 'text' && isString(val)\n                ? val.toUpperCase()\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? val.children.toUpperCase()\n                    : val;\n        },\n        lower: (val, type) => {\n            // prettier-ignore\n            return type === 'text' && isString(val)\n                ? val.toLowerCase()\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? val.children.toLowerCase()\n                    : val;\n        },\n        capitalize: (val, type) => {\n            // prettier-ignore\n            return (type === 'text' && isString(val)\n                ? capitalize(val)\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? capitalize(val.children)\n                    : val);\n        }\n    };\n}\nlet _compiler;\nfunction registerMessageCompiler(compiler) {\n    _compiler = compiler;\n}\nlet _resolver;\n/**\n * Register the message resolver\n *\n * @param resolver - A {@link MessageResolver} function\n *\n * @VueI18nGeneral\n */\nfunction registerMessageResolver(resolver) {\n    _resolver = resolver;\n}\nlet _fallbacker;\n/**\n * Register the locale fallbacker\n *\n * @param fallbacker - A {@link LocaleFallbacker} function\n *\n * @VueI18nGeneral\n */\nfunction registerLocaleFallbacker(fallbacker) {\n    _fallbacker = fallbacker;\n}\n// Additional Meta for Intlify DevTools\nlet _additionalMeta =  null;\n/* #__NO_SIDE_EFFECTS__ */\nconst setAdditionalMeta = (meta) => {\n    _additionalMeta = meta;\n};\n/* #__NO_SIDE_EFFECTS__ */\nconst getAdditionalMeta = () => _additionalMeta;\nlet _fallbackContext = null;\nconst setFallbackContext = (context) => {\n    _fallbackContext = context;\n};\nconst getFallbackContext = () => _fallbackContext;\n// ID for CoreContext\nlet _cid = 0;\nfunction createCoreContext(options = {}) {\n    // setup options\n    const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;\n    const version = isString(options.version) ? options.version : VERSION;\n    const locale = isString(options.locale) || isFunction(options.locale)\n        ? options.locale\n        : DEFAULT_LOCALE;\n    const _locale = isFunction(locale) ? DEFAULT_LOCALE : locale;\n    const fallbackLocale = isArray(options.fallbackLocale) ||\n        isPlainObject(options.fallbackLocale) ||\n        isString(options.fallbackLocale) ||\n        options.fallbackLocale === false\n        ? options.fallbackLocale\n        : _locale;\n    const messages = isPlainObject(options.messages)\n        ? options.messages\n        : createResources(_locale);\n    const datetimeFormats = isPlainObject(options.datetimeFormats)\n            ? options.datetimeFormats\n            : createResources(_locale)\n        ;\n    const numberFormats = isPlainObject(options.numberFormats)\n            ? options.numberFormats\n            : createResources(_locale)\n        ;\n    const modifiers = assign(create(), options.modifiers, getDefaultLinkedModifiers());\n    const pluralRules = options.pluralRules || create();\n    const missing = isFunction(options.missing) ? options.missing : null;\n    const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n        ? options.missingWarn\n        : true;\n    const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n        ? options.fallbackWarn\n        : true;\n    const fallbackFormat = !!options.fallbackFormat;\n    const unresolving = !!options.unresolving;\n    const postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    const processor = isPlainObject(options.processor) ? options.processor : null;\n    const warnHtmlMessage = isBoolean(options.warnHtmlMessage)\n        ? options.warnHtmlMessage\n        : true;\n    const escapeParameter = !!options.escapeParameter;\n    const messageCompiler = isFunction(options.messageCompiler)\n        ? options.messageCompiler\n        : _compiler;\n    if ((process.env.NODE_ENV !== 'production') &&\n        !false &&\n        !false &&\n        isFunction(options.messageCompiler)) {\n        warnOnce(getWarnMessage(CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));\n    }\n    const messageResolver = isFunction(options.messageResolver)\n        ? options.messageResolver\n        : _resolver || resolveWithKeyValue;\n    const localeFallbacker = isFunction(options.localeFallbacker)\n        ? options.localeFallbacker\n        : _fallbacker || fallbackWithSimple;\n    const fallbackContext = isObject(options.fallbackContext)\n        ? options.fallbackContext\n        : undefined;\n    // setup internal options\n    const internalOptions = options;\n    const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters)\n            ? internalOptions.__datetimeFormatters\n            : new Map()\n        ;\n    const __numberFormatters = isObject(internalOptions.__numberFormatters)\n            ? internalOptions.__numberFormatters\n            : new Map()\n        ;\n    const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};\n    _cid++;\n    const context = {\n        version,\n        cid: _cid,\n        locale,\n        fallbackLocale,\n        messages,\n        modifiers,\n        pluralRules,\n        missing,\n        missingWarn,\n        fallbackWarn,\n        fallbackFormat,\n        unresolving,\n        postTranslation,\n        processor,\n        warnHtmlMessage,\n        escapeParameter,\n        messageCompiler,\n        messageResolver,\n        localeFallbacker,\n        fallbackContext,\n        onWarn,\n        __meta\n    };\n    {\n        context.datetimeFormats = datetimeFormats;\n        context.numberFormats = numberFormats;\n        context.__datetimeFormatters = __datetimeFormatters;\n        context.__numberFormatters = __numberFormatters;\n    }\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.__v_emitter =\n            internalOptions.__v_emitter != null\n                ? internalOptions.__v_emitter\n                : undefined;\n    }\n    // NOTE: experimental !!\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n        initI18nDevTools(context, version, __meta);\n    }\n    return context;\n}\nconst createResources = (locale) => ({ [locale]: create() });\n/** @internal */\nfunction isTranslateFallbackWarn(fallback, key) {\n    return fallback instanceof RegExp ? fallback.test(key) : fallback;\n}\n/** @internal */\nfunction isTranslateMissingWarn(missing, key) {\n    return missing instanceof RegExp ? missing.test(key) : missing;\n}\n/** @internal */\nfunction handleMissing(context, key, locale, missingWarn, type) {\n    const { missing, onWarn } = context;\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        const emitter = context.__v_emitter;\n        if (emitter) {\n            emitter.emit(\"missing\" /* VueDevToolsTimelineEvents.MISSING */, {\n                locale,\n                key,\n                type,\n                groupId: `${type}:${key}`\n            });\n        }\n    }\n    if (missing !== null) {\n        const ret = missing(context, locale, key, type);\n        return isString(ret) ? ret : key;\n    }\n    else {\n        if ((process.env.NODE_ENV !== 'production') && isTranslateMissingWarn(missingWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));\n        }\n        return key;\n    }\n}\n/** @internal */\nfunction updateFallbackLocale(ctx, locale, fallback) {\n    const context = ctx;\n    context.__localeChainCache = new Map();\n    ctx.localeFallbacker(ctx, fallback, locale);\n}\n/** @internal */\nfunction isAlmostSameLocale(locale, compareLocale) {\n    if (locale === compareLocale)\n        return false;\n    return locale.split('-')[0] === compareLocale.split('-')[0];\n}\n/** @internal */\nfunction isImplicitFallback(targetLocale, locales) {\n    const index = locales.indexOf(targetLocale);\n    if (index === -1) {\n        return false;\n    }\n    for (let i = index + 1; i < locales.length; i++) {\n        if (isAlmostSameLocale(targetLocale, locales[i])) {\n            return true;\n        }\n    }\n    return false;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nfunction format(ast) {\n    const msg = (ctx) => formatParts(ctx, ast);\n    return msg;\n}\nfunction formatParts(ctx, ast) {\n    const body = resolveBody(ast);\n    if (body == null) {\n        throw createUnhandleNodeError(0 /* NodeTypes.Resource */);\n    }\n    const type = resolveType(body);\n    if (type === 1 /* NodeTypes.Plural */) {\n        const plural = body;\n        const cases = resolveCases(plural);\n        return ctx.plural(cases.reduce((messages, c) => [\n            ...messages,\n            formatMessageParts(ctx, c)\n        ], []));\n    }\n    else {\n        return formatMessageParts(ctx, body);\n    }\n}\nconst PROPS_BODY = ['b', 'body'];\nfunction resolveBody(node) {\n    return resolveProps(node, PROPS_BODY);\n}\nconst PROPS_CASES = ['c', 'cases'];\nfunction resolveCases(node) {\n    return resolveProps(node, PROPS_CASES, []);\n}\nfunction formatMessageParts(ctx, node) {\n    const static_ = resolveStatic(node);\n    if (static_ != null) {\n        return ctx.type === 'text'\n            ? static_\n            : ctx.normalize([static_]);\n    }\n    else {\n        const messages = resolveItems(node).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);\n        return ctx.normalize(messages);\n    }\n}\nconst PROPS_STATIC = ['s', 'static'];\nfunction resolveStatic(node) {\n    return resolveProps(node, PROPS_STATIC);\n}\nconst PROPS_ITEMS = ['i', 'items'];\nfunction resolveItems(node) {\n    return resolveProps(node, PROPS_ITEMS, []);\n}\nfunction formatMessagePart(ctx, node) {\n    const type = resolveType(node);\n    switch (type) {\n        case 3 /* NodeTypes.Text */: {\n            return resolveValue(node, type);\n        }\n        case 9 /* NodeTypes.Literal */: {\n            return resolveValue(node, type);\n        }\n        case 4 /* NodeTypes.Named */: {\n            const named = node;\n            if (hasOwn(named, 'k') && named.k) {\n                return ctx.interpolate(ctx.named(named.k));\n            }\n            if (hasOwn(named, 'key') && named.key) {\n                return ctx.interpolate(ctx.named(named.key));\n            }\n            throw createUnhandleNodeError(type);\n        }\n        case 5 /* NodeTypes.List */: {\n            const list = node;\n            if (hasOwn(list, 'i') && isNumber(list.i)) {\n                return ctx.interpolate(ctx.list(list.i));\n            }\n            if (hasOwn(list, 'index') && isNumber(list.index)) {\n                return ctx.interpolate(ctx.list(list.index));\n            }\n            throw createUnhandleNodeError(type);\n        }\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            const modifier = resolveLinkedModifier(linked);\n            const key = resolveLinkedKey(linked);\n            return ctx.linked(formatMessagePart(ctx, key), modifier ? formatMessagePart(ctx, modifier) : undefined, ctx.type);\n        }\n        case 7 /* NodeTypes.LinkedKey */: {\n            return resolveValue(node, type);\n        }\n        case 8 /* NodeTypes.LinkedModifier */: {\n            return resolveValue(node, type);\n        }\n        default:\n            throw new Error(`unhandled node on format message part: ${type}`);\n    }\n}\nconst PROPS_TYPE = ['t', 'type'];\nfunction resolveType(node) {\n    return resolveProps(node, PROPS_TYPE);\n}\nconst PROPS_VALUE = ['v', 'value'];\nfunction resolveValue(node, type) {\n    const resolved = resolveProps(node, PROPS_VALUE);\n    if (resolved) {\n        return resolved;\n    }\n    else {\n        throw createUnhandleNodeError(type);\n    }\n}\nconst PROPS_MODIFIER = ['m', 'modifier'];\nfunction resolveLinkedModifier(node) {\n    return resolveProps(node, PROPS_MODIFIER);\n}\nconst PROPS_KEY = ['k', 'key'];\nfunction resolveLinkedKey(node) {\n    const resolved = resolveProps(node, PROPS_KEY);\n    if (resolved) {\n        return resolved;\n    }\n    else {\n        throw createUnhandleNodeError(6 /* NodeTypes.Linked */);\n    }\n}\nfunction resolveProps(node, props, defaultValue) {\n    for (let i = 0; i < props.length; i++) {\n        const prop = props[i];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (hasOwn(node, prop) && node[prop] != null) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return node[prop];\n        }\n    }\n    return defaultValue;\n}\nfunction createUnhandleNodeError(type) {\n    return new Error(`unhandled node type: ${type}`);\n}\n\nconst WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;\nfunction checkHtmlMessage(source, warnHtmlMessage) {\n    if (warnHtmlMessage && detectHtmlTag(source)) {\n        warn(format$1(WARN_MESSAGE, { source }));\n    }\n}\nconst defaultOnCacheKey = (message) => message;\nlet compileCache = create();\nfunction onCompileWarn(_warn) {\n    if (_warn.code === CompileWarnCodes.USE_MODULO_SYNTAX) {\n        warn(`The use of named interpolation with modulo syntax is deprecated. ` +\n            `It will be removed in v10.\\n` +\n            `reference: https://vue-i18n.intlify.dev/guide/essentials/syntax#rails-i18n-format \\n` +\n            `(message compiler warning message: ${_warn.message})`);\n    }\n}\nfunction clearCompileCache() {\n    compileCache = create();\n}\nfunction isMessageAST(val) {\n    return (isObject(val) &&\n        resolveType(val) === 0 &&\n        (hasOwn(val, 'b') || hasOwn(val, 'body')));\n}\nfunction baseCompile(message, options = {}) {\n    // error detecting on compile\n    let detectError = false;\n    const onError = options.onError || defaultOnError;\n    options.onError = (err) => {\n        detectError = true;\n        onError(err);\n    };\n    // compile with mesasge-compiler\n    return { ...baseCompile$1(message, options), detectError };\n}\n/* #__NO_SIDE_EFFECTS__ */\nconst compileToFunction = (message, context) => {\n    if (!isString(message)) {\n        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);\n    }\n    // set onWarn\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.onWarn = onCompileWarn;\n    }\n    {\n        // check HTML message\n        const warnHtmlMessage = isBoolean(context.warnHtmlMessage)\n            ? context.warnHtmlMessage\n            : true;\n        (process.env.NODE_ENV !== 'production') && checkHtmlMessage(message, warnHtmlMessage);\n        // check caches\n        const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n        const cacheKey = onCacheKey(message);\n        const cached = compileCache[cacheKey];\n        if (cached) {\n            return cached;\n        }\n        // compile\n        const { code, detectError } = baseCompile(message, context);\n        // evaluate function\n        const msg = new Function(`return ${code}`)();\n        // if occurred compile error, don't cache\n        return !detectError\n            ? (compileCache[cacheKey] = msg)\n            : msg;\n    }\n};\nfunction compile(message, context) {\n    // set onWarn\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.onWarn = onCompileWarn;\n    }\n    if (((__INTLIFY_JIT_COMPILATION__ && !__INTLIFY_DROP_MESSAGE_COMPILER__)) &&\n        isString(message)) {\n        // check HTML message\n        const warnHtmlMessage = isBoolean(context.warnHtmlMessage)\n            ? context.warnHtmlMessage\n            : true;\n        (process.env.NODE_ENV !== 'production') && checkHtmlMessage(message, warnHtmlMessage);\n        // check caches\n        const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n        const cacheKey = onCacheKey(message);\n        const cached = compileCache[cacheKey];\n        if (cached) {\n            return cached;\n        }\n        // compile with JIT mode\n        const { ast, detectError } = baseCompile(message, {\n            ...context,\n            location: (process.env.NODE_ENV !== 'production'),\n            jit: true\n        });\n        // compose message function from AST\n        const msg = format(ast);\n        // if occurred compile error, don't cache\n        return !detectError\n            ? (compileCache[cacheKey] = msg)\n            : msg;\n    }\n    else {\n        if ((process.env.NODE_ENV !== 'production') && !isMessageAST(message)) {\n            warn(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);\n            return (() => message);\n        }\n        // AST case (passed from bundler)\n        const cacheKey = message.cacheKey;\n        if (cacheKey) {\n            const cached = compileCache[cacheKey];\n            if (cached) {\n                return cached;\n            }\n            // compose message function from message (AST)\n            return (compileCache[cacheKey] =\n                format(message));\n        }\n        else {\n            return format(message);\n        }\n    }\n}\n\nconst NOOP_MESSAGE_FUNCTION = () => '';\nconst isMessageFunction = (val) => isFunction(val);\n// implementation of `translate` function\nfunction translate(context, ...args) {\n    const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;\n    const [key, options] = parseTranslateArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const escapeParameter = isBoolean(options.escapeParameter)\n        ? options.escapeParameter\n        : context.escapeParameter;\n    const resolvedMessage = !!options.resolvedMessage;\n    // prettier-ignore\n    const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) // default by function option\n        ? !isBoolean(options.default)\n            ? options.default\n            : (!messageCompiler ? () => key : key)\n        : fallbackFormat // default by `fallbackFormat` option\n            ? (!messageCompiler ? () => key : key)\n            : '';\n    const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== '';\n    const locale = getLocale(context, options);\n    // escape params\n    escapeParameter && escapeParams(options);\n    // resolve message format\n    // eslint-disable-next-line prefer-const\n    let [formatScope, targetLocale, message] = !resolvedMessage\n        ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn)\n        : [\n            key,\n            locale,\n            messages[locale] || create()\n        ];\n    // NOTE:\n    //  Fix to work around `ssrTransfrom` bug in Vite.\n    //  https://github.com/vitejs/vite/issues/4306\n    //  To get around this, use temporary variables.\n    //  https://github.com/nuxt/framework/issues/1461#issuecomment-954606243\n    let format = formatScope;\n    // if you use default message, set it as message format!\n    let cacheBaseKey = key;\n    if (!resolvedMessage &&\n        !(isString(format) ||\n            isMessageAST(format) ||\n            isMessageFunction(format))) {\n        if (enableDefaultMsg) {\n            format = defaultMsgOrKey;\n            cacheBaseKey = format;\n        }\n    }\n    // checking message format and target locale\n    if (!resolvedMessage &&\n        (!(isString(format) ||\n            isMessageAST(format) ||\n            isMessageFunction(format)) ||\n            !isString(targetLocale))) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    // TODO: refactor\n    if ((process.env.NODE_ENV !== 'production') && isString(format) && context.messageCompiler == null) {\n        warn(`The message format compilation is not supported in this build. ` +\n            `Because message compiler isn't included. ` +\n            `You need to pre-compilation all message format. ` +\n            `So translate function return '${key}'.`);\n        return key;\n    }\n    // setup compile error detecting\n    let occurred = false;\n    const onError = () => {\n        occurred = true;\n    };\n    // compile message format\n    const msg = !isMessageFunction(format)\n        ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError)\n        : format;\n    // if occurred compile error, return the message format\n    if (occurred) {\n        return format;\n    }\n    // evaluate message with context\n    const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);\n    const msgContext = createMessageContext(ctxOptions);\n    const messaged = evaluateMessage(context, msg, msgContext);\n    // if use post translation option, proceed it with handler\n    const ret = postTranslation\n        ? postTranslation(messaged, key)\n        : messaged;\n    // NOTE: experimental !!\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n        // prettier-ignore\n        const payloads = {\n            timestamp: Date.now(),\n            key: isString(key)\n                ? key\n                : isMessageFunction(format)\n                    ? format.key\n                    : '',\n            locale: targetLocale || (isMessageFunction(format)\n                ? format.locale\n                : ''),\n            format: isString(format)\n                ? format\n                : isMessageFunction(format)\n                    ? format.source\n                    : '',\n            message: ret\n        };\n        payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});\n        translateDevTools(payloads);\n    }\n    return ret;\n}\nfunction escapeParams(options) {\n    if (isArray(options.list)) {\n        options.list = options.list.map(item => isString(item) ? escapeHtml(item) : item);\n    }\n    else if (isObject(options.named)) {\n        Object.keys(options.named).forEach(key => {\n            if (isString(options.named[key])) {\n                options.named[key] = escapeHtml(options.named[key]);\n            }\n        });\n    }\n}\nfunction resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {\n    const { messages, onWarn, messageResolver: resolveValue, localeFallbacker } = context;\n    const locales = localeFallbacker(context, fallbackLocale, locale); // eslint-disable-line @typescript-eslint/no-explicit-any\n    let message = create();\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'translate';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            !isAlmostSameLocale(locale, targetLocale) &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        message =\n            messages[targetLocale] || create();\n        // for vue-devtools timeline event\n        let start = null;\n        let startTag;\n        let endTag;\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n            start = window.performance.now();\n            startTag = 'intlify-message-resolve-start';\n            endTag = 'intlify-message-resolve-end';\n            mark && mark(startTag);\n        }\n        if ((format = resolveValue(message, key)) === null) {\n            // if null, resolve with object key path\n            format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n            const end = window.performance.now();\n            const emitter = context.__v_emitter;\n            if (emitter && start && format) {\n                emitter.emit(\"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */, {\n                    type: \"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */,\n                    key,\n                    message: format,\n                    time: end - start,\n                    groupId: `${type}:${key}`\n                });\n            }\n            if (startTag && endTag && mark && measure) {\n                mark(endTag);\n                measure('intlify message resolve', startTag, endTag);\n            }\n        }\n        if (isString(format) || isMessageAST(format) || isMessageFunction(format)) {\n            break;\n        }\n        if (!isImplicitFallback(targetLocale, locales)) {\n            const missingRet = handleMissing(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n            key, targetLocale, missingWarn, type);\n            if (missingRet !== key) {\n                format = missingRet;\n            }\n        }\n        from = to;\n    }\n    return [format, targetLocale, message];\n}\nfunction compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError) {\n    const { messageCompiler, warnHtmlMessage } = context;\n    if (isMessageFunction(format)) {\n        const msg = format;\n        msg.locale = msg.locale || targetLocale;\n        msg.key = msg.key || key;\n        return msg;\n    }\n    if (messageCompiler == null) {\n        const msg = (() => format);\n        msg.locale = targetLocale;\n        msg.key = key;\n        return msg;\n    }\n    // for vue-devtools timeline event\n    let start = null;\n    let startTag;\n    let endTag;\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        start = window.performance.now();\n        startTag = 'intlify-message-compilation-start';\n        endTag = 'intlify-message-compilation-end';\n        mark && mark(startTag);\n    }\n    const msg = messageCompiler(format, getCompileContext(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, onError));\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        const end = window.performance.now();\n        const emitter = context.__v_emitter;\n        if (emitter && start) {\n            emitter.emit(\"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */, {\n                type: \"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */,\n                message: format,\n                time: end - start,\n                groupId: `${'translate'}:${key}`\n            });\n        }\n        if (startTag && endTag && mark && measure) {\n            mark(endTag);\n            measure('intlify message compilation', startTag, endTag);\n        }\n    }\n    msg.locale = targetLocale;\n    msg.key = key;\n    msg.source = format;\n    return msg;\n}\nfunction evaluateMessage(context, msg, msgCtx) {\n    // for vue-devtools timeline event\n    let start = null;\n    let startTag;\n    let endTag;\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        start = window.performance.now();\n        startTag = 'intlify-message-evaluation-start';\n        endTag = 'intlify-message-evaluation-end';\n        mark && mark(startTag);\n    }\n    const messaged = msg(msgCtx);\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        const end = window.performance.now();\n        const emitter = context.__v_emitter;\n        if (emitter && start) {\n            emitter.emit(\"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */, {\n                type: \"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */,\n                value: messaged,\n                time: end - start,\n                groupId: `${'translate'}:${msg.key}`\n            });\n        }\n        if (startTag && endTag && mark && measure) {\n            mark(endTag);\n            measure('intlify message evaluation', startTag, endTag);\n        }\n    }\n    return messaged;\n}\n/** @internal */\nfunction parseTranslateArgs(...args) {\n    const [arg1, arg2, arg3] = args;\n    const options = create();\n    if (!isString(arg1) &&\n        !isNumber(arg1) &&\n        !isMessageFunction(arg1) &&\n        !isMessageAST(arg1)) {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    // prettier-ignore\n    const key = isNumber(arg1)\n        ? String(arg1)\n        : isMessageFunction(arg1)\n            ? arg1\n            : arg1;\n    if (isNumber(arg2)) {\n        options.plural = arg2;\n    }\n    else if (isString(arg2)) {\n        options.default = arg2;\n    }\n    else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {\n        options.named = arg2;\n    }\n    else if (isArray(arg2)) {\n        options.list = arg2;\n    }\n    if (isNumber(arg3)) {\n        options.plural = arg3;\n    }\n    else if (isString(arg3)) {\n        options.default = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        assign(options, arg3);\n    }\n    return [key, options];\n}\nfunction getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {\n    return {\n        locale,\n        key,\n        warnHtmlMessage,\n        onError: (err) => {\n            onError && onError(err);\n            if ((process.env.NODE_ENV !== 'production')) {\n                const _source = getSourceForCodeFrame(source);\n                const message = `Message compilation error: ${err.message}`;\n                const codeFrame = err.location &&\n                    _source &&\n                    generateCodeFrame(_source, err.location.start.offset, err.location.end.offset);\n                const emitter = context.__v_emitter;\n                if (emitter && _source) {\n                    emitter.emit(\"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */, {\n                        message: _source,\n                        error: err.message,\n                        start: err.location && err.location.start.offset,\n                        end: err.location && err.location.end.offset,\n                        groupId: `${'translate'}:${key}`\n                    });\n                }\n                console.error(codeFrame ? `${message}\\n${codeFrame}` : message);\n            }\n            else {\n                throw err;\n            }\n        },\n        onCacheKey: (source) => generateFormatCacheKey(locale, key, source)\n    };\n}\nfunction getSourceForCodeFrame(source) {\n    if (isString(source)) {\n        return source;\n    }\n    else {\n        if (source.loc && source.loc.source) {\n            return source.loc.source;\n        }\n    }\n}\nfunction getMessageContextOptions(context, locale, message, options) {\n    const { modifiers, pluralRules, messageResolver: resolveValue, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;\n    const resolveMessage = (key) => {\n        let val = resolveValue(message, key);\n        // fallback to root context\n        if (val == null && fallbackContext) {\n            const [, , message] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);\n            val = resolveValue(message, key);\n        }\n        if (isString(val) || isMessageAST(val)) {\n            let occurred = false;\n            const onError = () => {\n                occurred = true;\n            };\n            const msg = compileMessageFormat(context, key, locale, val, key, onError);\n            return !occurred\n                ? msg\n                : NOOP_MESSAGE_FUNCTION;\n        }\n        else if (isMessageFunction(val)) {\n            return val;\n        }\n        else {\n            // TODO: should be implemented warning message\n            return NOOP_MESSAGE_FUNCTION;\n        }\n    };\n    const ctxOptions = {\n        locale,\n        modifiers,\n        pluralRules,\n        messages: resolveMessage\n    };\n    if (context.processor) {\n        ctxOptions.processor = context.processor;\n    }\n    if (options.list) {\n        ctxOptions.list = options.list;\n    }\n    if (options.named) {\n        ctxOptions.named = options.named;\n    }\n    if (isNumber(options.plural)) {\n        ctxOptions.pluralIndex = options.plural;\n    }\n    return ctxOptions;\n}\n\nconst intlDefined = typeof Intl !== 'undefined';\nconst Availabilities = {\n    dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\n    numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\n};\n\n// implementation of `datetime` function\nfunction datetime(context, ...args) {\n    const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\n    const { __datetimeFormatters } = context;\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.dateTimeFormat) {\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));\n        return MISSING_RESOLVE_VALUE;\n    }\n    const [key, value, options, overrides] = parseDateTimeArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const part = !!options.part;\n    const locale = getLocale(context, options);\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n    fallbackLocale, locale);\n    if (!isString(key) || key === '') {\n        return new Intl.DateTimeFormat(locale, overrides).format(value);\n    }\n    // resolve format\n    let datetimeFormat = {};\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'datetime format';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        datetimeFormat =\n            datetimeFormats[targetLocale] || {};\n        format = datetimeFormat[key];\n        if (isPlainObject(format))\n            break;\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n        from = to;\n    }\n    // checking format and target locale\n    if (!isPlainObject(format) || !isString(targetLocale)) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    let id = `${targetLocale}__${key}`;\n    if (!isEmptyObject(overrides)) {\n        id = `${id}__${JSON.stringify(overrides)}`;\n    }\n    let formatter = __datetimeFormatters.get(id);\n    if (!formatter) {\n        formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format, overrides));\n        __datetimeFormatters.set(id, formatter);\n    }\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst DATETIME_FORMAT_OPTIONS_KEYS = [\n    'localeMatcher',\n    'weekday',\n    'era',\n    'year',\n    'month',\n    'day',\n    'hour',\n    'minute',\n    'second',\n    'timeZoneName',\n    'formatMatcher',\n    'hour12',\n    'timeZone',\n    'dateStyle',\n    'timeStyle',\n    'calendar',\n    'dayPeriod',\n    'numberingSystem',\n    'hourCycle',\n    'fractionalSecondDigits'\n];\n/** @internal */\nfunction parseDateTimeArgs(...args) {\n    const [arg1, arg2, arg3, arg4] = args;\n    const options = create();\n    let overrides = create();\n    let value;\n    if (isString(arg1)) {\n        // Only allow ISO strings - other date formats are often supported,\n        // but may cause different results in different browsers.\n        const matches = arg1.match(/(\\d{4}-\\d{2}-\\d{2})(T|\\s)?(.*)/);\n        if (!matches) {\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n        }\n        // Some browsers can not parse the iso datetime separated by space,\n        // this is a compromise solution by replace the 'T'/' ' with 'T'\n        const dateTime = matches[3]\n            ? matches[3].trim().startsWith('T')\n                ? `${matches[1].trim()}${matches[3].trim()}`\n                : `${matches[1].trim()}T${matches[3].trim()}`\n            : matches[1].trim();\n        value = new Date(dateTime);\n        try {\n            // This will fail if the date is not valid\n            value.toISOString();\n        }\n        catch (e) {\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n        }\n    }\n    else if (isDate(arg1)) {\n        if (isNaN(arg1.getTime())) {\n            throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);\n        }\n        value = arg1;\n    }\n    else if (isNumber(arg1)) {\n        value = arg1;\n    }\n    else {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    if (isString(arg2)) {\n        options.key = arg2;\n    }\n    else if (isPlainObject(arg2)) {\n        Object.keys(arg2).forEach(key => {\n            if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {\n                overrides[key] = arg2[key];\n            }\n            else {\n                options[key] = arg2[key];\n            }\n        });\n    }\n    if (isString(arg3)) {\n        options.locale = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        overrides = arg3;\n    }\n    if (isPlainObject(arg4)) {\n        overrides = arg4;\n    }\n    return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearDateTimeFormat(ctx, locale, format) {\n    const context = ctx;\n    for (const key in format) {\n        const id = `${locale}__${key}`;\n        if (!context.__datetimeFormatters.has(id)) {\n            continue;\n        }\n        context.__datetimeFormatters.delete(id);\n    }\n}\n\n// implementation of `number` function\nfunction number(context, ...args) {\n    const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\n    const { __numberFormatters } = context;\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.numberFormat) {\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));\n        return MISSING_RESOLVE_VALUE;\n    }\n    const [key, value, options, overrides] = parseNumberArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const part = !!options.part;\n    const locale = getLocale(context, options);\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n    fallbackLocale, locale);\n    if (!isString(key) || key === '') {\n        return new Intl.NumberFormat(locale, overrides).format(value);\n    }\n    // resolve format\n    let numberFormat = {};\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'number format';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        numberFormat =\n            numberFormats[targetLocale] || {};\n        format = numberFormat[key];\n        if (isPlainObject(format))\n            break;\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n        from = to;\n    }\n    // checking format and target locale\n    if (!isPlainObject(format) || !isString(targetLocale)) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    let id = `${targetLocale}__${key}`;\n    if (!isEmptyObject(overrides)) {\n        id = `${id}__${JSON.stringify(overrides)}`;\n    }\n    let formatter = __numberFormatters.get(id);\n    if (!formatter) {\n        formatter = new Intl.NumberFormat(targetLocale, assign({}, format, overrides));\n        __numberFormatters.set(id, formatter);\n    }\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst NUMBER_FORMAT_OPTIONS_KEYS = [\n    'localeMatcher',\n    'style',\n    'currency',\n    'currencyDisplay',\n    'currencySign',\n    'useGrouping',\n    'minimumIntegerDigits',\n    'minimumFractionDigits',\n    'maximumFractionDigits',\n    'minimumSignificantDigits',\n    'maximumSignificantDigits',\n    'compactDisplay',\n    'notation',\n    'signDisplay',\n    'unit',\n    'unitDisplay',\n    'roundingMode',\n    'roundingPriority',\n    'roundingIncrement',\n    'trailingZeroDisplay'\n];\n/** @internal */\nfunction parseNumberArgs(...args) {\n    const [arg1, arg2, arg3, arg4] = args;\n    const options = create();\n    let overrides = create();\n    if (!isNumber(arg1)) {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    const value = arg1;\n    if (isString(arg2)) {\n        options.key = arg2;\n    }\n    else if (isPlainObject(arg2)) {\n        Object.keys(arg2).forEach(key => {\n            if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {\n                overrides[key] = arg2[key];\n            }\n            else {\n                options[key] = arg2[key];\n            }\n        });\n    }\n    if (isString(arg3)) {\n        options.locale = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        overrides = arg3;\n    }\n    if (isPlainObject(arg4)) {\n        overrides = arg4;\n    }\n    return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearNumberFormat(ctx, locale, format) {\n    const context = ctx;\n    for (const key in format) {\n        const id = `${locale}__${key}`;\n        if (!context.__numberFormatters.has(id)) {\n            continue;\n        }\n        context.__numberFormatters.delete(id);\n    }\n}\n\n{\n    initFeatureFlags();\n}\n\nexport { CoreErrorCodes, CoreWarnCodes, DATETIME_FORMAT_OPTIONS_KEYS, DEFAULT_LOCALE, DEFAULT_MESSAGE_DATA_TYPE, MISSING_RESOLVE_VALUE, NOT_REOSLVED, NUMBER_FORMAT_OPTIONS_KEYS, VERSION, clearCompileCache, clearDateTimeFormat, clearNumberFormat, compile, compileToFunction, createCoreContext, createCoreError, createMessageContext, datetime, fallbackWithLocaleChain, fallbackWithSimple, getAdditionalMeta, getDevToolsHook, getFallbackContext, getLocale, getWarnMessage, handleMissing, initI18nDevTools, isAlmostSameLocale, isImplicitFallback, isMessageAST, isMessageFunction, isTranslateFallbackWarn, isTranslateMissingWarn, number, parse, parseDateTimeArgs, parseNumberArgs, parseTranslateArgs, registerLocaleFallbacker, registerMessageCompiler, registerMessageResolver, resolveLocale, resolveValue$1 as resolveValue, resolveWithKeyValue, setAdditionalMeta, setDevToolsHook, setFallbackContext, translate, translateDevTools, updateFallbackLocale };\n", "/*!\n  * vue-i18n v9.14.3\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, incrementer, format, makeSymbol, isPlainObject, isArray, create, deepCopy, isString, hasOwn, isObject, warn, warnOnce, isBoolean, isRegExp, isFunction, inBrowser, assign, isNumber, createEmitter, isEmptyObject } from '@intlify/shared';\nimport { CoreWarnCodes, CoreErrorCodes, createCompileError, DEFAULT_LOCALE, updateFallbackLocale, setFallbackContext, createCoreContext, clearDateTimeFormat, clearNumberFormat, setAdditionalMeta, getFallbackContext, NOT_REOSLVED, isTranslateFallbackWarn, isTranslateMissingWarn, parseTranslateArgs, translate, MISSING_RESOLVE_VALUE, parseDateTimeArgs, datetime, parseNumberArgs, number, isMessageAST, isMessageFunction, fallbackWith<PERSON><PERSON><PERSON><PERSON>hain, NUMBER_FORMAT_OPTIONS_KEYS, DATETIME_FORMAT_OPTIONS_KEYS, registerMessageCompiler, compile, compileToFunction, registerMessageResolver, resolveValue, registerLocaleFallbacker, setDevToolsHook } from '@intlify/core-base';\nimport { createVNode, Text, computed, watch, getCurrentInstance, ref, shallowRef, Fragment, defineComponent, h, effectScope, inject, onMounted, onUnmounted, onBeforeMount, isRef } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * Vue I18n Version\n *\n * @remarks\n * Semver format. Same format as the package.json `version` field.\n *\n * @VueI18nGeneral\n */\nconst VERSION = '9.14.3';\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n    if (typeof __VUE_I18N_FULL_INSTALL__ !== 'boolean') {\n        getGlobalThis().__VUE_I18N_FULL_INSTALL__ = true;\n    }\n    if (typeof __VUE_I18N_LEGACY_API__ !== 'boolean') {\n        getGlobalThis().__VUE_I18N_LEGACY_API__ = true;\n    }\n    if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n    }\n    if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n    }\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n    }\n}\n\nconst code$1 = CoreWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst I18nWarnCodes = {\n    FALLBACK_TO_ROOT: code$1, // 9\n    NOT_SUPPORTED_PRESERVE: inc$1(), // 10\n    NOT_SUPPORTED_FORMATTER: inc$1(), // 11\n    NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$1(), // 12\n    NOT_SUPPORTED_GET_CHOICE_INDEX: inc$1(), // 13\n    COMPONENT_NAME_LEGACY_COMPATIBLE: inc$1(), // 14\n    NOT_FOUND_PARENT_SCOPE: inc$1(), // 15\n    IGNORE_OBJ_FLATTEN: inc$1(), // 16\n    NOTICE_DROP_ALLOW_COMPOSITION: inc$1(), // 17\n    NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG: inc$1() // 18\n};\nconst warnMessages = {\n    [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,\n    [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,\n    [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`,\n    [I18nWarnCodes.IGNORE_OBJ_FLATTEN]: `Ignore object flatten: '{key}' key has an string value`,\n    [I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION]: `'allowComposition' option will be dropped in the next major version. For more information, please see 👉 https://tinyurl.com/2p97mcze`,\n    [I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG]: `'translateExistCompatible' option will be dropped in the next major version.`\n};\nfunction getWarnMessage(code, ...args) {\n    return format(warnMessages[code], ...args);\n}\n\nconst code = CoreErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst I18nErrorCodes = {\n    // composer module errors\n    UNEXPECTED_RETURN_TYPE: code, // 24\n    // legacy module errors\n    INVALID_ARGUMENT: inc(), // 25\n    // i18n module errors\n    MUST_BE_CALL_SETUP_TOP: inc(), // 26\n    NOT_INSTALLED: inc(), // 27\n    NOT_AVAILABLE_IN_LEGACY_MODE: inc(), // 28\n    // directive module errors\n    REQUIRED_VALUE: inc(), // 29\n    INVALID_VALUE: inc(), // 30\n    // vue-devtools errors\n    CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc(), // 31\n    NOT_INSTALLED_WITH_PROVIDE: inc(), // 32\n    // unexpected error\n    UNEXPECTED_ERROR: inc(), // 33\n    // not compatible legacy vue-i18n constructor\n    NOT_COMPATIBLE_LEGACY_VUE_I18N: inc(), // 34\n    // bridge support vue 2.x only\n    BRIDGE_SUPPORT_VUE_2_ONLY: inc(), // 35\n    // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``\n    MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc(), // 36\n    // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly\n    NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc(), // 37\n    // for enhancement\n    __EXTEND_POINT__: inc() // 38\n};\nfunction createI18nError(code, ...args) {\n    return createCompileError(code, null, (process.env.NODE_ENV !== 'production') ? { messages: errorMessages, args } : undefined);\n}\nconst errorMessages = {\n    [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: 'Unexpected return type in composer',\n    [I18nErrorCodes.INVALID_ARGUMENT]: 'Invalid argument',\n    [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: 'Must be called at the top of a `setup` function',\n    [I18nErrorCodes.NOT_INSTALLED]: 'Need to install with `app.use` function',\n    [I18nErrorCodes.UNEXPECTED_ERROR]: 'Unexpected error',\n    [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: 'Not available in legacy mode',\n    [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,\n    [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,\n    [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,\n    [I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE]: 'Need to install with `provide` function',\n    [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: 'Not compatible legacy VueI18n.',\n    [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: 'vue-i18n-bridge support Vue 2.x only',\n    [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: 'Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode',\n    [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: 'Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly'\n};\n\nconst TranslateVNodeSymbol = \n/* #__PURE__*/ makeSymbol('__translateVNode');\nconst DatetimePartsSymbol = /* #__PURE__*/ makeSymbol('__datetimeParts');\nconst NumberPartsSymbol = /* #__PURE__*/ makeSymbol('__numberParts');\nconst EnableEmitter = /* #__PURE__*/ makeSymbol('__enableEmitter');\nconst DisableEmitter = /* #__PURE__*/ makeSymbol('__disableEmitter');\nconst SetPluralRulesSymbol = makeSymbol('__setPluralRules');\nmakeSymbol('__intlifyMeta');\nconst InejctWithOptionSymbol = \n/* #__PURE__*/ makeSymbol('__injectWithOption');\nconst DisposeSymbol = /* #__PURE__*/ makeSymbol('__dispose');\nconst __VUE_I18N_BRIDGE__ =  '__VUE_I18N_BRIDGE__';\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Transform flat json in obj to normal json in obj\n */\nfunction handleFlatJson(obj) {\n    // check obj\n    if (!isObject(obj)) {\n        return obj;\n    }\n    for (const key in obj) {\n        // check key\n        if (!hasOwn(obj, key)) {\n            continue;\n        }\n        // handle for normal json\n        if (!key.includes('.')) {\n            // recursive process value if value is also a object\n            if (isObject(obj[key])) {\n                handleFlatJson(obj[key]);\n            }\n        }\n        // handle for flat json, transform to normal json\n        else {\n            // go to the last object\n            const subKeys = key.split('.');\n            const lastIndex = subKeys.length - 1;\n            let currentObj = obj;\n            let hasStringValue = false;\n            for (let i = 0; i < lastIndex; i++) {\n                if (subKeys[i] === '__proto__') {\n                    throw new Error(`unsafe key: ${subKeys[i]}`);\n                }\n                if (!(subKeys[i] in currentObj)) {\n                    currentObj[subKeys[i]] = create();\n                }\n                if (!isObject(currentObj[subKeys[i]])) {\n                    (process.env.NODE_ENV !== 'production') &&\n                        warn(getWarnMessage(I18nWarnCodes.IGNORE_OBJ_FLATTEN, {\n                            key: subKeys[i]\n                        }));\n                    hasStringValue = true;\n                    break;\n                }\n                currentObj = currentObj[subKeys[i]];\n            }\n            // update last object value, delete old property\n            if (!hasStringValue) {\n                currentObj[subKeys[lastIndex]] = obj[key];\n                delete obj[key];\n            }\n            // recursive process value if value is also a object\n            if (isObject(currentObj[subKeys[lastIndex]])) {\n                handleFlatJson(currentObj[subKeys[lastIndex]]);\n            }\n        }\n    }\n    return obj;\n}\nfunction getLocaleMessages(locale, options) {\n    const { messages, __i18n, messageResolver, flatJson } = options;\n    // prettier-ignore\n    const ret = (isPlainObject(messages)\n        ? messages\n        : isArray(__i18n)\n            ? create()\n            : { [locale]: create() });\n    // merge locale messages of i18n custom block\n    if (isArray(__i18n)) {\n        __i18n.forEach(custom => {\n            if ('locale' in custom && 'resource' in custom) {\n                const { locale, resource } = custom;\n                if (locale) {\n                    ret[locale] = ret[locale] || create();\n                    deepCopy(resource, ret[locale]);\n                }\n                else {\n                    deepCopy(resource, ret);\n                }\n            }\n            else {\n                isString(custom) && deepCopy(JSON.parse(custom), ret);\n            }\n        });\n    }\n    // handle messages for flat json\n    if (messageResolver == null && flatJson) {\n        for (const key in ret) {\n            if (hasOwn(ret, key)) {\n                handleFlatJson(ret[key]);\n            }\n        }\n    }\n    return ret;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getComponentOptions(instance) {\n    return instance.type ;\n}\nfunction adjustI18nResources(gl, options, componentOptions // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    let messages = isObject(options.messages)\n        ? options.messages\n        : create();\n    if ('__i18nGlobal' in componentOptions) {\n        messages = getLocaleMessages(gl.locale.value, {\n            messages,\n            __i18n: componentOptions.__i18nGlobal\n        });\n    }\n    // merge locale messages\n    const locales = Object.keys(messages);\n    if (locales.length) {\n        locales.forEach(locale => {\n            gl.mergeLocaleMessage(locale, messages[locale]);\n        });\n    }\n    {\n        // merge datetime formats\n        if (isObject(options.datetimeFormats)) {\n            const locales = Object.keys(options.datetimeFormats);\n            if (locales.length) {\n                locales.forEach(locale => {\n                    gl.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);\n                });\n            }\n        }\n        // merge number formats\n        if (isObject(options.numberFormats)) {\n            const locales = Object.keys(options.numberFormats);\n            if (locales.length) {\n                locales.forEach(locale => {\n                    gl.mergeNumberFormat(locale, options.numberFormats[locale]);\n                });\n            }\n        }\n    }\n}\nfunction createTextNode(key) {\n    return createVNode(Text, null, key, 0)\n        ;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// extend VNode interface\nconst DEVTOOLS_META = '__INTLIFY_META__';\nconst NOOP_RETURN_ARRAY = () => [];\nconst NOOP_RETURN_FALSE = () => false;\nlet composerID = 0;\nfunction defineCoreMissingHandler(missing) {\n    return ((ctx, locale, key, type) => {\n        return missing(locale, key, getCurrentInstance() || undefined, type);\n    });\n}\n// for Intlify DevTools\n/* #__NO_SIDE_EFFECTS__ */\nconst getMetaInfo = () => {\n    const instance = getCurrentInstance();\n    let meta = null; // eslint-disable-line @typescript-eslint/no-explicit-any\n    return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META])\n        ? { [DEVTOOLS_META]: meta } // eslint-disable-line @typescript-eslint/no-explicit-any\n        : null;\n};\n/**\n * Create composer interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createComposer(options = {}, VueI18nLegacy) {\n    const { __root, __injectWithOption } = options;\n    const _isGlobal = __root === undefined;\n    const flatJson = options.flatJson;\n    const _ref = inBrowser ? ref : shallowRef;\n    const translateExistCompatible = !!options.translateExistCompatible;\n    if ((process.env.NODE_ENV !== 'production')) {\n        if (translateExistCompatible && !false) {\n            warnOnce(getWarnMessage(I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG));\n        }\n    }\n    let _inheritLocale = isBoolean(options.inheritLocale)\n        ? options.inheritLocale\n        : true;\n    const _locale = _ref(\n    // prettier-ignore\n    __root && _inheritLocale\n        ? __root.locale.value\n        : isString(options.locale)\n            ? options.locale\n            : DEFAULT_LOCALE);\n    const _fallbackLocale = _ref(\n    // prettier-ignore\n    __root && _inheritLocale\n        ? __root.fallbackLocale.value\n        : isString(options.fallbackLocale) ||\n            isArray(options.fallbackLocale) ||\n            isPlainObject(options.fallbackLocale) ||\n            options.fallbackLocale === false\n            ? options.fallbackLocale\n            : _locale.value);\n    const _messages = _ref(getLocaleMessages(_locale.value, options));\n    // prettier-ignore\n    const _datetimeFormats = _ref(isPlainObject(options.datetimeFormats)\n            ? options.datetimeFormats\n            : { [_locale.value]: {} })\n        ;\n    // prettier-ignore\n    const _numberFormats = _ref(isPlainObject(options.numberFormats)\n            ? options.numberFormats\n            : { [_locale.value]: {} })\n        ;\n    // warning suppress options\n    // prettier-ignore\n    let _missingWarn = __root\n        ? __root.missingWarn\n        : isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n            ? options.missingWarn\n            : true;\n    // prettier-ignore\n    let _fallbackWarn = __root\n        ? __root.fallbackWarn\n        : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n            ? options.fallbackWarn\n            : true;\n    // prettier-ignore\n    let _fallbackRoot = __root\n        ? __root.fallbackRoot\n        : isBoolean(options.fallbackRoot)\n            ? options.fallbackRoot\n            : true;\n    // configure fall back to root\n    let _fallbackFormat = !!options.fallbackFormat;\n    // runtime missing\n    let _missing = isFunction(options.missing) ? options.missing : null;\n    let _runtimeMissing = isFunction(options.missing)\n        ? defineCoreMissingHandler(options.missing)\n        : null;\n    // postTranslation handler\n    let _postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    // prettier-ignore\n    let _warnHtmlMessage = __root\n        ? __root.warnHtmlMessage\n        : isBoolean(options.warnHtmlMessage)\n            ? options.warnHtmlMessage\n            : true;\n    let _escapeParameter = !!options.escapeParameter;\n    // custom linked modifiers\n    // prettier-ignore\n    const _modifiers = __root\n        ? __root.modifiers\n        : isPlainObject(options.modifiers)\n            ? options.modifiers\n            : {};\n    // pluralRules\n    let _pluralRules = options.pluralRules || (__root && __root.pluralRules);\n    // runtime context\n    // eslint-disable-next-line prefer-const\n    let _context;\n    const getCoreContext = () => {\n        _isGlobal && setFallbackContext(null);\n        const ctxOptions = {\n            version: VERSION,\n            locale: _locale.value,\n            fallbackLocale: _fallbackLocale.value,\n            messages: _messages.value,\n            modifiers: _modifiers,\n            pluralRules: _pluralRules,\n            missing: _runtimeMissing === null ? undefined : _runtimeMissing,\n            missingWarn: _missingWarn,\n            fallbackWarn: _fallbackWarn,\n            fallbackFormat: _fallbackFormat,\n            unresolving: true,\n            postTranslation: _postTranslation === null ? undefined : _postTranslation,\n            warnHtmlMessage: _warnHtmlMessage,\n            escapeParameter: _escapeParameter,\n            messageResolver: options.messageResolver,\n            messageCompiler: options.messageCompiler,\n            __meta: { framework: 'vue' }\n        };\n        {\n            ctxOptions.datetimeFormats = _datetimeFormats.value;\n            ctxOptions.numberFormats = _numberFormats.value;\n            ctxOptions.__datetimeFormatters = isPlainObject(_context)\n                ? _context.__datetimeFormatters\n                : undefined;\n            ctxOptions.__numberFormatters = isPlainObject(_context)\n                ? _context.__numberFormatters\n                : undefined;\n        }\n        if ((process.env.NODE_ENV !== 'production')) {\n            ctxOptions.__v_emitter = isPlainObject(_context)\n                ? _context.__v_emitter\n                : undefined;\n        }\n        const ctx = createCoreContext(ctxOptions);\n        _isGlobal && setFallbackContext(ctx);\n        return ctx;\n    };\n    _context = getCoreContext();\n    updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n    // track reactivity\n    function trackReactivityValues() {\n        return [\n                _locale.value,\n                _fallbackLocale.value,\n                _messages.value,\n                _datetimeFormats.value,\n                _numberFormats.value\n            ]\n            ;\n    }\n    // locale\n    const locale = computed({\n        get: () => _locale.value,\n        set: val => {\n            _locale.value = val;\n            _context.locale = _locale.value;\n        }\n    });\n    // fallbackLocale\n    const fallbackLocale = computed({\n        get: () => _fallbackLocale.value,\n        set: val => {\n            _fallbackLocale.value = val;\n            _context.fallbackLocale = _fallbackLocale.value;\n            updateFallbackLocale(_context, _locale.value, val);\n        }\n    });\n    // messages\n    const messages = computed(() => _messages.value);\n    // datetimeFormats\n    const datetimeFormats = /* #__PURE__*/ computed(() => _datetimeFormats.value);\n    // numberFormats\n    const numberFormats = /* #__PURE__*/ computed(() => _numberFormats.value);\n    // getPostTranslationHandler\n    function getPostTranslationHandler() {\n        return isFunction(_postTranslation) ? _postTranslation : null;\n    }\n    // setPostTranslationHandler\n    function setPostTranslationHandler(handler) {\n        _postTranslation = handler;\n        _context.postTranslation = handler;\n    }\n    // getMissingHandler\n    function getMissingHandler() {\n        return _missing;\n    }\n    // setMissingHandler\n    function setMissingHandler(handler) {\n        if (handler !== null) {\n            _runtimeMissing = defineCoreMissingHandler(handler);\n        }\n        _missing = handler;\n        _context.missing = _runtimeMissing;\n    }\n    function isResolvedTranslateMessage(type, arg // eslint-disable-line @typescript-eslint/no-explicit-any\n    ) {\n        return type !== 'translate' || !arg.resolvedMessage;\n    }\n    const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {\n        trackReactivityValues(); // track reactive dependency\n        // NOTE: experimental !!\n        let ret;\n        try {\n            if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n                setAdditionalMeta(getMetaInfo());\n            }\n            if (!_isGlobal) {\n                _context.fallbackContext = __root\n                    ? getFallbackContext()\n                    : undefined;\n            }\n            ret = fn(_context);\n        }\n        finally {\n            if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n                setAdditionalMeta(null);\n            }\n            if (!_isGlobal) {\n                _context.fallbackContext = undefined;\n            }\n        }\n        if ((warnType !== 'translate exists' && // for not `te` (e.g `t`)\n            isNumber(ret) &&\n            ret === NOT_REOSLVED) ||\n            (warnType === 'translate exists' && !ret) // for `te`\n        ) {\n            const [key, arg2] = argumentParser();\n            if ((process.env.NODE_ENV !== 'production') &&\n                __root &&\n                isString(key) &&\n                isResolvedTranslateMessage(warnType, arg2)) {\n                if (_fallbackRoot &&\n                    (isTranslateFallbackWarn(_fallbackWarn, key) ||\n                        isTranslateMissingWarn(_missingWarn, key))) {\n                    warn(getWarnMessage(I18nWarnCodes.FALLBACK_TO_ROOT, {\n                        key,\n                        type: warnType\n                    }));\n                }\n                // for vue-devtools timeline event\n                if ((process.env.NODE_ENV !== 'production')) {\n                    const { __v_emitter: emitter } = _context;\n                    if (emitter && _fallbackRoot) {\n                        emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                            type: warnType,\n                            key,\n                            to: 'global',\n                            groupId: `${warnType}:${key}`\n                        });\n                    }\n                }\n            }\n            return __root && _fallbackRoot\n                ? fallbackSuccess(__root)\n                : fallbackFail(key);\n        }\n        else if (successCondition(ret)) {\n            return ret;\n        }\n        else {\n            /* istanbul ignore next */\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);\n        }\n    };\n    // t\n    function t(...args) {\n        return wrapWithDeps(context => Reflect.apply(translate, null, [context, ...args]), () => parseTranslateArgs(...args), 'translate', root => Reflect.apply(root.t, root, [...args]), key => key, val => isString(val));\n    }\n    // rt\n    function rt(...args) {\n        const [arg1, arg2, arg3] = args;\n        if (arg3 && !isObject(arg3)) {\n            throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n        }\n        return t(...[arg1, arg2, assign({ resolvedMessage: true }, arg3 || {})]);\n    }\n    // d\n    function d(...args) {\n        return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format', root => Reflect.apply(root.d, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n    }\n    // n\n    function n(...args) {\n        return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format', root => Reflect.apply(root.n, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n    }\n    // for custom processor\n    function normalize(values) {\n        return values.map(val => isString(val) || isNumber(val) || isBoolean(val)\n            ? createTextNode(String(val))\n            : val);\n    }\n    const interpolate = (val) => val;\n    const processor = {\n        normalize,\n        interpolate,\n        type: 'vnode'\n    };\n    // translateVNode, using for `i18n-t` component\n    function translateVNode(...args) {\n        return wrapWithDeps(context => {\n            let ret;\n            const _context = context;\n            try {\n                _context.processor = processor;\n                ret = Reflect.apply(translate, null, [_context, ...args]);\n            }\n            finally {\n                _context.processor = null;\n            }\n            return ret;\n        }, () => parseTranslateArgs(...args), 'translate', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[TranslateVNodeSymbol](...args), key => [createTextNode(key)], val => isArray(val));\n    }\n    // numberParts, using for `i18n-n` component\n    function numberParts(...args) {\n        return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[NumberPartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n    }\n    // datetimeParts, using for `i18n-d` component\n    function datetimeParts(...args) {\n        return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[DatetimePartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n    }\n    function setPluralRules(rules) {\n        _pluralRules = rules;\n        _context.pluralRules = _pluralRules;\n    }\n    // te\n    function te(key, locale) {\n        return wrapWithDeps(() => {\n            if (!key) {\n                return false;\n            }\n            const targetLocale = isString(locale) ? locale : _locale.value;\n            const message = getLocaleMessage(targetLocale);\n            const resolved = _context.messageResolver(message, key);\n            return !translateExistCompatible\n                ? isMessageAST(resolved) ||\n                    isMessageFunction(resolved) ||\n                    isString(resolved)\n                : resolved != null;\n        }, () => [key], 'translate exists', root => {\n            return Reflect.apply(root.te, root, [key, locale]);\n        }, NOOP_RETURN_FALSE, val => isBoolean(val));\n    }\n    function resolveMessages(key) {\n        let messages = null;\n        const locales = fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);\n        for (let i = 0; i < locales.length; i++) {\n            const targetLocaleMessages = _messages.value[locales[i]] || {};\n            const messageValue = _context.messageResolver(targetLocaleMessages, key);\n            if (messageValue != null) {\n                messages = messageValue;\n                break;\n            }\n        }\n        return messages;\n    }\n    // tm\n    function tm(key) {\n        const messages = resolveMessages(key);\n        // prettier-ignore\n        return messages != null\n            ? messages\n            : __root\n                ? __root.tm(key) || {}\n                : {};\n    }\n    // getLocaleMessage\n    function getLocaleMessage(locale) {\n        return (_messages.value[locale] || {});\n    }\n    // setLocaleMessage\n    function setLocaleMessage(locale, message) {\n        if (flatJson) {\n            const _message = { [locale]: message };\n            for (const key in _message) {\n                if (hasOwn(_message, key)) {\n                    handleFlatJson(_message[key]);\n                }\n            }\n            message = _message[locale];\n        }\n        _messages.value[locale] = message;\n        _context.messages = _messages.value;\n    }\n    // mergeLocaleMessage\n    function mergeLocaleMessage(locale, message) {\n        _messages.value[locale] = _messages.value[locale] || {};\n        const _message = { [locale]: message };\n        if (flatJson) {\n            for (const key in _message) {\n                if (hasOwn(_message, key)) {\n                    handleFlatJson(_message[key]);\n                }\n            }\n        }\n        message = _message[locale];\n        deepCopy(message, _messages.value[locale]);\n        _context.messages = _messages.value;\n    }\n    // getDateTimeFormat\n    function getDateTimeFormat(locale) {\n        return _datetimeFormats.value[locale] || {};\n    }\n    // setDateTimeFormat\n    function setDateTimeFormat(locale, format) {\n        _datetimeFormats.value[locale] = format;\n        _context.datetimeFormats = _datetimeFormats.value;\n        clearDateTimeFormat(_context, locale, format);\n    }\n    // mergeDateTimeFormat\n    function mergeDateTimeFormat(locale, format) {\n        _datetimeFormats.value[locale] = assign(_datetimeFormats.value[locale] || {}, format);\n        _context.datetimeFormats = _datetimeFormats.value;\n        clearDateTimeFormat(_context, locale, format);\n    }\n    // getNumberFormat\n    function getNumberFormat(locale) {\n        return _numberFormats.value[locale] || {};\n    }\n    // setNumberFormat\n    function setNumberFormat(locale, format) {\n        _numberFormats.value[locale] = format;\n        _context.numberFormats = _numberFormats.value;\n        clearNumberFormat(_context, locale, format);\n    }\n    // mergeNumberFormat\n    function mergeNumberFormat(locale, format) {\n        _numberFormats.value[locale] = assign(_numberFormats.value[locale] || {}, format);\n        _context.numberFormats = _numberFormats.value;\n        clearNumberFormat(_context, locale, format);\n    }\n    // for debug\n    composerID++;\n    // watch root locale & fallbackLocale\n    if (__root && inBrowser) {\n        watch(__root.locale, (val) => {\n            if (_inheritLocale) {\n                _locale.value = val;\n                _context.locale = val;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        });\n        watch(__root.fallbackLocale, (val) => {\n            if (_inheritLocale) {\n                _fallbackLocale.value = val;\n                _context.fallbackLocale = val;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        });\n    }\n    // define basic composition API!\n    const composer = {\n        id: composerID,\n        locale,\n        fallbackLocale,\n        get inheritLocale() {\n            return _inheritLocale;\n        },\n        set inheritLocale(val) {\n            _inheritLocale = val;\n            if (val && __root) {\n                _locale.value = __root.locale.value;\n                _fallbackLocale.value = __root.fallbackLocale.value;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        },\n        get availableLocales() {\n            return Object.keys(_messages.value).sort();\n        },\n        messages,\n        get modifiers() {\n            return _modifiers;\n        },\n        get pluralRules() {\n            return _pluralRules || {};\n        },\n        get isGlobal() {\n            return _isGlobal;\n        },\n        get missingWarn() {\n            return _missingWarn;\n        },\n        set missingWarn(val) {\n            _missingWarn = val;\n            _context.missingWarn = _missingWarn;\n        },\n        get fallbackWarn() {\n            return _fallbackWarn;\n        },\n        set fallbackWarn(val) {\n            _fallbackWarn = val;\n            _context.fallbackWarn = _fallbackWarn;\n        },\n        get fallbackRoot() {\n            return _fallbackRoot;\n        },\n        set fallbackRoot(val) {\n            _fallbackRoot = val;\n        },\n        get fallbackFormat() {\n            return _fallbackFormat;\n        },\n        set fallbackFormat(val) {\n            _fallbackFormat = val;\n            _context.fallbackFormat = _fallbackFormat;\n        },\n        get warnHtmlMessage() {\n            return _warnHtmlMessage;\n        },\n        set warnHtmlMessage(val) {\n            _warnHtmlMessage = val;\n            _context.warnHtmlMessage = val;\n        },\n        get escapeParameter() {\n            return _escapeParameter;\n        },\n        set escapeParameter(val) {\n            _escapeParameter = val;\n            _context.escapeParameter = val;\n        },\n        t,\n        getLocaleMessage,\n        setLocaleMessage,\n        mergeLocaleMessage,\n        getPostTranslationHandler,\n        setPostTranslationHandler,\n        getMissingHandler,\n        setMissingHandler,\n        [SetPluralRulesSymbol]: setPluralRules\n    };\n    {\n        composer.datetimeFormats = datetimeFormats;\n        composer.numberFormats = numberFormats;\n        composer.rt = rt;\n        composer.te = te;\n        composer.tm = tm;\n        composer.d = d;\n        composer.n = n;\n        composer.getDateTimeFormat = getDateTimeFormat;\n        composer.setDateTimeFormat = setDateTimeFormat;\n        composer.mergeDateTimeFormat = mergeDateTimeFormat;\n        composer.getNumberFormat = getNumberFormat;\n        composer.setNumberFormat = setNumberFormat;\n        composer.mergeNumberFormat = mergeNumberFormat;\n        composer[InejctWithOptionSymbol] = __injectWithOption;\n        composer[TranslateVNodeSymbol] = translateVNode;\n        composer[DatetimePartsSymbol] = datetimeParts;\n        composer[NumberPartsSymbol] = numberParts;\n    }\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        composer[EnableEmitter] = (emitter) => {\n            _context.__v_emitter = emitter;\n        };\n        composer[DisableEmitter] = () => {\n            _context.__v_emitter = undefined;\n        };\n    }\n    return composer;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Convert to I18n Composer Options from VueI18n Options\n *\n * @internal\n */\nfunction convertComposerOptions(options) {\n    const locale = isString(options.locale) ? options.locale : DEFAULT_LOCALE;\n    const fallbackLocale = isString(options.fallbackLocale) ||\n        isArray(options.fallbackLocale) ||\n        isPlainObject(options.fallbackLocale) ||\n        options.fallbackLocale === false\n        ? options.fallbackLocale\n        : locale;\n    const missing = isFunction(options.missing) ? options.missing : undefined;\n    const missingWarn = isBoolean(options.silentTranslationWarn) ||\n        isRegExp(options.silentTranslationWarn)\n        ? !options.silentTranslationWarn\n        : true;\n    const fallbackWarn = isBoolean(options.silentFallbackWarn) ||\n        isRegExp(options.silentFallbackWarn)\n        ? !options.silentFallbackWarn\n        : true;\n    const fallbackRoot = isBoolean(options.fallbackRoot)\n        ? options.fallbackRoot\n        : true;\n    const fallbackFormat = !!options.formatFallbackMessages;\n    const modifiers = isPlainObject(options.modifiers) ? options.modifiers : {};\n    const pluralizationRules = options.pluralizationRules;\n    const postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : undefined;\n    const warnHtmlMessage = isString(options.warnHtmlInMessage)\n        ? options.warnHtmlInMessage !== 'off'\n        : true;\n    const escapeParameter = !!options.escapeParameterHtml;\n    const inheritLocale = isBoolean(options.sync) ? options.sync : true;\n    if ((process.env.NODE_ENV !== 'production') && options.formatter) {\n        warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n    }\n    if ((process.env.NODE_ENV !== 'production') && options.preserveDirectiveContent) {\n        warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n    }\n    let messages = options.messages;\n    if (isPlainObject(options.sharedMessages)) {\n        const sharedMessages = options.sharedMessages;\n        const locales = Object.keys(sharedMessages);\n        messages = locales.reduce((messages, locale) => {\n            const message = messages[locale] || (messages[locale] = {});\n            assign(message, sharedMessages[locale]);\n            return messages;\n        }, (messages || {}));\n    }\n    const { __i18n, __root, __injectWithOption } = options;\n    const datetimeFormats = options.datetimeFormats;\n    const numberFormats = options.numberFormats;\n    const flatJson = options.flatJson;\n    const translateExistCompatible = options\n        .translateExistCompatible;\n    return {\n        locale,\n        fallbackLocale,\n        messages,\n        flatJson,\n        datetimeFormats,\n        numberFormats,\n        missing,\n        missingWarn,\n        fallbackWarn,\n        fallbackRoot,\n        fallbackFormat,\n        modifiers,\n        pluralRules: pluralizationRules,\n        postTranslation,\n        warnHtmlMessage,\n        escapeParameter,\n        messageResolver: options.messageResolver,\n        inheritLocale,\n        translateExistCompatible,\n        __i18n,\n        __root,\n        __injectWithOption\n    };\n}\n/**\n * create VueI18n interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createVueI18n(options = {}, VueI18nLegacy) {\n    {\n        const composer = createComposer(convertComposerOptions(options));\n        const { __extender } = options;\n        // defines VueI18n\n        const vueI18n = {\n            // id\n            id: composer.id,\n            // locale\n            get locale() {\n                return composer.locale.value;\n            },\n            set locale(val) {\n                composer.locale.value = val;\n            },\n            // fallbackLocale\n            get fallbackLocale() {\n                return composer.fallbackLocale.value;\n            },\n            set fallbackLocale(val) {\n                composer.fallbackLocale.value = val;\n            },\n            // messages\n            get messages() {\n                return composer.messages.value;\n            },\n            // datetimeFormats\n            get datetimeFormats() {\n                return composer.datetimeFormats.value;\n            },\n            // numberFormats\n            get numberFormats() {\n                return composer.numberFormats.value;\n            },\n            // availableLocales\n            get availableLocales() {\n                return composer.availableLocales;\n            },\n            // formatter\n            get formatter() {\n                (process.env.NODE_ENV !== 'production') && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n                // dummy\n                return {\n                    interpolate() {\n                        return [];\n                    }\n                };\n            },\n            set formatter(val) {\n                (process.env.NODE_ENV !== 'production') && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n            },\n            // missing\n            get missing() {\n                return composer.getMissingHandler();\n            },\n            set missing(handler) {\n                composer.setMissingHandler(handler);\n            },\n            // silentTranslationWarn\n            get silentTranslationWarn() {\n                return isBoolean(composer.missingWarn)\n                    ? !composer.missingWarn\n                    : composer.missingWarn;\n            },\n            set silentTranslationWarn(val) {\n                composer.missingWarn = isBoolean(val) ? !val : val;\n            },\n            // silentFallbackWarn\n            get silentFallbackWarn() {\n                return isBoolean(composer.fallbackWarn)\n                    ? !composer.fallbackWarn\n                    : composer.fallbackWarn;\n            },\n            set silentFallbackWarn(val) {\n                composer.fallbackWarn = isBoolean(val) ? !val : val;\n            },\n            // modifiers\n            get modifiers() {\n                return composer.modifiers;\n            },\n            // formatFallbackMessages\n            get formatFallbackMessages() {\n                return composer.fallbackFormat;\n            },\n            set formatFallbackMessages(val) {\n                composer.fallbackFormat = val;\n            },\n            // postTranslation\n            get postTranslation() {\n                return composer.getPostTranslationHandler();\n            },\n            set postTranslation(handler) {\n                composer.setPostTranslationHandler(handler);\n            },\n            // sync\n            get sync() {\n                return composer.inheritLocale;\n            },\n            set sync(val) {\n                composer.inheritLocale = val;\n            },\n            // warnInHtmlMessage\n            get warnHtmlInMessage() {\n                return composer.warnHtmlMessage ? 'warn' : 'off';\n            },\n            set warnHtmlInMessage(val) {\n                composer.warnHtmlMessage = val !== 'off';\n            },\n            // escapeParameterHtml\n            get escapeParameterHtml() {\n                return composer.escapeParameter;\n            },\n            set escapeParameterHtml(val) {\n                composer.escapeParameter = val;\n            },\n            // preserveDirectiveContent\n            get preserveDirectiveContent() {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n                return true;\n            },\n            set preserveDirectiveContent(val) {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n            },\n            // pluralizationRules\n            get pluralizationRules() {\n                return composer.pluralRules || {};\n            },\n            // for internal\n            __composer: composer,\n            // t\n            t(...args) {\n                const [arg1, arg2, arg3] = args;\n                const options = {};\n                let list = null;\n                let named = null;\n                if (!isString(arg1)) {\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n                }\n                const key = arg1;\n                if (isString(arg2)) {\n                    options.locale = arg2;\n                }\n                else if (isArray(arg2)) {\n                    list = arg2;\n                }\n                else if (isPlainObject(arg2)) {\n                    named = arg2;\n                }\n                if (isArray(arg3)) {\n                    list = arg3;\n                }\n                else if (isPlainObject(arg3)) {\n                    named = arg3;\n                }\n                // return composer.t(key, (list || named || {}) as any, options)\n                return Reflect.apply(composer.t, composer, [\n                    key,\n                    (list || named || {}),\n                    options\n                ]);\n            },\n            rt(...args) {\n                return Reflect.apply(composer.rt, composer, [...args]);\n            },\n            // tc\n            tc(...args) {\n                const [arg1, arg2, arg3] = args;\n                const options = { plural: 1 };\n                let list = null;\n                let named = null;\n                if (!isString(arg1)) {\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n                }\n                const key = arg1;\n                if (isString(arg2)) {\n                    options.locale = arg2;\n                }\n                else if (isNumber(arg2)) {\n                    options.plural = arg2;\n                }\n                else if (isArray(arg2)) {\n                    list = arg2;\n                }\n                else if (isPlainObject(arg2)) {\n                    named = arg2;\n                }\n                if (isString(arg3)) {\n                    options.locale = arg3;\n                }\n                else if (isArray(arg3)) {\n                    list = arg3;\n                }\n                else if (isPlainObject(arg3)) {\n                    named = arg3;\n                }\n                // return composer.t(key, (list || named || {}) as any, options)\n                return Reflect.apply(composer.t, composer, [\n                    key,\n                    (list || named || {}),\n                    options\n                ]);\n            },\n            // te\n            te(key, locale) {\n                return composer.te(key, locale);\n            },\n            // tm\n            tm(key) {\n                return composer.tm(key);\n            },\n            // getLocaleMessage\n            getLocaleMessage(locale) {\n                return composer.getLocaleMessage(locale);\n            },\n            // setLocaleMessage\n            setLocaleMessage(locale, message) {\n                composer.setLocaleMessage(locale, message);\n            },\n            // mergeLocaleMessage\n            mergeLocaleMessage(locale, message) {\n                composer.mergeLocaleMessage(locale, message);\n            },\n            // d\n            d(...args) {\n                return Reflect.apply(composer.d, composer, [...args]);\n            },\n            // getDateTimeFormat\n            getDateTimeFormat(locale) {\n                return composer.getDateTimeFormat(locale);\n            },\n            // setDateTimeFormat\n            setDateTimeFormat(locale, format) {\n                composer.setDateTimeFormat(locale, format);\n            },\n            // mergeDateTimeFormat\n            mergeDateTimeFormat(locale, format) {\n                composer.mergeDateTimeFormat(locale, format);\n            },\n            // n\n            n(...args) {\n                return Reflect.apply(composer.n, composer, [...args]);\n            },\n            // getNumberFormat\n            getNumberFormat(locale) {\n                return composer.getNumberFormat(locale);\n            },\n            // setNumberFormat\n            setNumberFormat(locale, format) {\n                composer.setNumberFormat(locale, format);\n            },\n            // mergeNumberFormat\n            mergeNumberFormat(locale, format) {\n                composer.mergeNumberFormat(locale, format);\n            },\n            // getChoiceIndex\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            getChoiceIndex(choice, choicesLength) {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));\n                return -1;\n            }\n        };\n        vueI18n.__extender = __extender;\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production')) {\n            vueI18n.__enableEmitter = (emitter) => {\n                const __composer = composer;\n                __composer[EnableEmitter] && __composer[EnableEmitter](emitter);\n            };\n            vueI18n.__disableEmitter = () => {\n                const __composer = composer;\n                __composer[DisableEmitter] && __composer[DisableEmitter]();\n            };\n        }\n        return vueI18n;\n    }\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nconst baseFormatProps = {\n    tag: {\n        type: [String, Object]\n    },\n    locale: {\n        type: String\n    },\n    scope: {\n        type: String,\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n        validator: (val /* ComponentI18nScope */) => val === 'parent' || val === 'global',\n        default: 'parent' /* ComponentI18nScope */\n    },\n    i18n: {\n        type: Object\n    }\n};\n\nfunction getInterpolateArg(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n{ slots }, // SetupContext,\nkeys) {\n    if (keys.length === 1 && keys[0] === 'default') {\n        // default slot with list\n        const ret = slots.default ? slots.default() : [];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return ret.reduce((slot, current) => {\n            return [\n                ...slot,\n                // prettier-ignore\n                ...(current.type === Fragment ? current.children : [current]\n                    )\n            ];\n        }, []);\n    }\n    else {\n        // named slots\n        return keys.reduce((arg, key) => {\n            const slot = slots[key];\n            if (slot) {\n                arg[key] = slot();\n            }\n            return arg;\n        }, create());\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getFragmentableTag(tag) {\n    return Fragment ;\n}\n\nconst TranslationImpl = /*#__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-t',\n    props: assign({\n        keypath: {\n            type: String,\n            required: true\n        },\n        plural: {\n            type: [Number, String],\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            validator: (val) => isNumber(val) || !isNaN(val)\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const { slots, attrs } = context;\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return () => {\n            const keys = Object.keys(slots).filter(key => key !== '_');\n            const options = create();\n            if (props.locale) {\n                options.locale = props.locale;\n            }\n            if (props.plural !== undefined) {\n                options.plural = isString(props.plural) ? +props.plural : props.plural;\n            }\n            const arg = getInterpolateArg(context, keys);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const children = i18n[TranslateVNodeSymbol](props.keypath, arg, options);\n            const assignedAttrs = assign(create(), attrs);\n            const tag = isString(props.tag) || isObject(props.tag)\n                ? props.tag\n                : getFragmentableTag();\n            return h(tag, assignedAttrs, children);\n        };\n    }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Translation Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [TranslationProps](component#translationprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Component Interpolation](../guide/advanced/component)\n *\n * @example\n * ```html\n * <div id=\"app\">\n *   <!-- ... -->\n *   <i18n keypath=\"term\" tag=\"label\" for=\"tos\">\n *     <a :href=\"url\" target=\"_blank\">{{ $t('tos') }}</a>\n *   </i18n>\n *   <!-- ... -->\n * </div>\n * ```\n * ```js\n * import { createApp } from 'vue'\n * import { createI18n } from 'vue-i18n'\n *\n * const messages = {\n *   en: {\n *     tos: 'Term of Service',\n *     term: 'I accept xxx {0}.'\n *   },\n *   ja: {\n *     tos: '利用規約',\n *     term: '私は xxx の{0}に同意します。'\n *   }\n * }\n *\n * const i18n = createI18n({\n *   locale: 'en',\n *   messages\n * })\n *\n * const app = createApp({\n *   data: {\n *     url: '/term'\n *   }\n * }).use(i18n).mount('#app')\n * ```\n *\n * @VueI18nComponent\n */\nconst Translation = TranslationImpl;\nconst I18nT = Translation;\n\nfunction isVNode(target) {\n    return isArray(target) && !isString(target[0]);\n}\nfunction renderFormatter(props, context, slotKeys, partFormatter) {\n    const { slots, attrs } = context;\n    return () => {\n        const options = { part: true };\n        let overrides = create();\n        if (props.locale) {\n            options.locale = props.locale;\n        }\n        if (isString(props.format)) {\n            options.key = props.format;\n        }\n        else if (isObject(props.format)) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            if (isString(props.format.key)) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                options.key = props.format.key;\n            }\n            // Filter out number format options only\n            overrides = Object.keys(props.format).reduce((options, prop) => {\n                return slotKeys.includes(prop)\n                    ? assign(create(), options, { [prop]: props.format[prop] }) // eslint-disable-line @typescript-eslint/no-explicit-any\n                    : options;\n            }, create());\n        }\n        const parts = partFormatter(...[props.value, options, overrides]);\n        let children = [options.key];\n        if (isArray(parts)) {\n            children = parts.map((part, index) => {\n                const slot = slots[part.type];\n                const node = slot\n                    ? slot({ [part.type]: part.value, index, parts })\n                    : [part.value];\n                if (isVNode(node)) {\n                    node[0].key = `${part.type}-${index}`;\n                }\n                return node;\n            });\n        }\n        else if (isString(parts)) {\n            children = [parts];\n        }\n        const assignedAttrs = assign(create(), attrs);\n        const tag = isString(props.tag) || isObject(props.tag)\n            ? props.tag\n            : getFragmentableTag();\n        return h(tag, assignedAttrs, children);\n    };\n}\n\nconst NumberFormatImpl = /*#__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-n',\n    props: assign({\n        value: {\n            type: Number,\n            required: true\n        },\n        format: {\n            type: [String, Object]\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return renderFormatter(props, context, NUMBER_FORMAT_OPTIONS_KEYS, (...args) => \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        i18n[NumberPartsSymbol](...args));\n    }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Number Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/number#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.NumberFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-numberformat)\n *\n * @VueI18nComponent\n */\nconst NumberFormat = NumberFormatImpl;\nconst I18nN = NumberFormat;\n\nconst DatetimeFormatImpl = /* #__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-d',\n    props: assign({\n        value: {\n            type: [Number, Date],\n            required: true\n        },\n        format: {\n            type: [String, Object]\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return renderFormatter(props, context, DATETIME_FORMAT_OPTIONS_KEYS, (...args) => \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        i18n[DatetimePartsSymbol](...args));\n    }\n});\n/**\n * Datetime Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/datetime#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.DateTimeFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-datetimeformat)\n *\n * @VueI18nComponent\n */\nconst DatetimeFormat = DatetimeFormatImpl;\nconst I18nD = DatetimeFormat;\n\nfunction getComposer$2(i18n, instance) {\n    const i18nInternal = i18n;\n    if (i18n.mode === 'composition') {\n        return (i18nInternal.__getInstance(instance) || i18n.global);\n    }\n    else {\n        const vueI18n = i18nInternal.__getInstance(instance);\n        return vueI18n != null\n            ? vueI18n.__composer\n            : i18n.global.__composer;\n    }\n}\nfunction vTDirective(i18n) {\n    const _process = (binding) => {\n        const { instance, modifiers, value } = binding;\n        /* istanbul ignore if */\n        if (!instance || !instance.$) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        const composer = getComposer$2(i18n, instance.$);\n        if ((process.env.NODE_ENV !== 'production') && modifiers.preserve) {\n            warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));\n        }\n        const parsedValue = parseValue(value);\n        return [\n            Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]),\n            composer\n        ];\n    };\n    const register = (el, binding) => {\n        const [textContent, composer] = _process(binding);\n        if (inBrowser && i18n.global === composer) {\n            // global scope only\n            el.__i18nWatcher = watch(composer.locale, () => {\n                binding.instance && binding.instance.$forceUpdate();\n            });\n        }\n        el.__composer = composer;\n        el.textContent = textContent;\n    };\n    const unregister = (el) => {\n        if (inBrowser && el.__i18nWatcher) {\n            el.__i18nWatcher();\n            el.__i18nWatcher = undefined;\n            delete el.__i18nWatcher;\n        }\n        if (el.__composer) {\n            el.__composer = undefined;\n            delete el.__composer;\n        }\n    };\n    const update = (el, { value }) => {\n        if (el.__composer) {\n            const composer = el.__composer;\n            const parsedValue = parseValue(value);\n            el.textContent = Reflect.apply(composer.t, composer, [\n                ...makeParams(parsedValue)\n            ]);\n        }\n    };\n    const getSSRProps = (binding) => {\n        const [textContent] = _process(binding);\n        return { textContent };\n    };\n    return {\n        created: register,\n        unmounted: unregister,\n        beforeUpdate: update,\n        getSSRProps\n    };\n}\nfunction parseValue(value) {\n    if (isString(value)) {\n        return { path: value };\n    }\n    else if (isPlainObject(value)) {\n        if (!('path' in value)) {\n            throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, 'path');\n        }\n        return value;\n    }\n    else {\n        throw createI18nError(I18nErrorCodes.INVALID_VALUE);\n    }\n}\nfunction makeParams(value) {\n    const { path, locale, args, choice, plural } = value;\n    const options = {};\n    const named = args || {};\n    if (isString(locale)) {\n        options.locale = locale;\n    }\n    if (isNumber(choice)) {\n        options.plural = choice;\n    }\n    if (isNumber(plural)) {\n        options.plural = plural;\n    }\n    return [path, named, options];\n}\n\nfunction apply(app, i18n, ...options) {\n    const pluginOptions = isPlainObject(options[0])\n        ? options[0]\n        : {};\n    const useI18nComponentName = !!pluginOptions.useI18nComponentName;\n    const globalInstall = isBoolean(pluginOptions.globalInstall)\n        ? pluginOptions.globalInstall\n        : true;\n    if ((process.env.NODE_ENV !== 'production') && globalInstall && useI18nComponentName) {\n        warn(getWarnMessage(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {\n            name: Translation.name\n        }));\n    }\n    if (globalInstall) {\n        [!useI18nComponentName ? Translation.name : 'i18n', 'I18nT'].forEach(name => app.component(name, Translation));\n        [NumberFormat.name, 'I18nN'].forEach(name => app.component(name, NumberFormat));\n        [DatetimeFormat.name, 'I18nD'].forEach(name => app.component(name, DatetimeFormat));\n    }\n    // install directive\n    {\n        app.directive('t', vTDirective(i18n));\n    }\n}\n\nconst VueDevToolsLabels = {\n    [\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */]: 'Vue I18n devtools',\n    [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'I18n Resources',\n    [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 'Vue I18n'\n};\nconst VueDevToolsPlaceholders = {\n    [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'Search for scopes ...'\n};\nconst VueDevToolsTimelineColors = {\n    [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 0xffcd19\n};\n\nconst VUE_I18N_COMPONENT_TYPES = 'vue-i18n: composer properties';\nlet devtoolsApi;\nasync function enableDevTools(app, i18n) {\n    return new Promise((resolve, reject) => {\n        try {\n            setupDevtoolsPlugin({\n                id: \"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */,\n                label: VueDevToolsLabels[\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */],\n                packageName: 'vue-i18n',\n                homepage: 'https://vue-i18n.intlify.dev',\n                logo: 'https://vue-i18n.intlify.dev/vue-i18n-devtools-logo.png',\n                componentStateTypes: [VUE_I18N_COMPONENT_TYPES],\n                app: app // eslint-disable-line @typescript-eslint/no-explicit-any\n            }, api => {\n                devtoolsApi = api;\n                api.on.visitComponentTree(({ componentInstance, treeNode }) => {\n                    updateComponentTreeTags(componentInstance, treeNode, i18n);\n                });\n                api.on.inspectComponent(({ componentInstance, instanceData }) => {\n                    if (componentInstance.vnode.el &&\n                        componentInstance.vnode.el.__VUE_I18N__ &&\n                        instanceData) {\n                        if (i18n.mode === 'legacy') {\n                            // ignore global scope on legacy mode\n                            if (componentInstance.vnode.el.__VUE_I18N__ !==\n                                i18n.global.__composer) {\n                                inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n                            }\n                        }\n                        else {\n                            inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n                        }\n                    }\n                });\n                api.addInspector({\n                    id: \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */,\n                    label: VueDevToolsLabels[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */],\n                    icon: 'language',\n                    treeFilterPlaceholder: VueDevToolsPlaceholders[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]\n                });\n                api.on.getInspectorTree(payload => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        registerScope(payload, i18n);\n                    }\n                });\n                const roots = new Map();\n                api.on.getInspectorState(async (payload) => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        api.unhighlightElement();\n                        inspectScope(payload, i18n);\n                        if (payload.nodeId === 'global') {\n                            if (!roots.has(payload.app)) {\n                                const [root] = await api.getComponentInstances(payload.app);\n                                roots.set(payload.app, root);\n                            }\n                            api.highlightElement(roots.get(payload.app));\n                        }\n                        else {\n                            const instance = getComponentInstance(payload.nodeId, i18n);\n                            instance && api.highlightElement(instance);\n                        }\n                    }\n                });\n                api.on.editInspectorState(payload => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        editScope(payload, i18n);\n                    }\n                });\n                api.addTimelineLayer({\n                    id: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n                    label: VueDevToolsLabels[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */],\n                    color: VueDevToolsTimelineColors[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]\n                });\n                resolve(true);\n            });\n        }\n        catch (e) {\n            console.error(e);\n            reject(false);\n        }\n    });\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getI18nScopeLable(instance) {\n    return (instance.type.name ||\n        instance.type.displayName ||\n        instance.type.__file ||\n        'Anonymous');\n}\nfunction updateComponentTreeTags(instance, // eslint-disable-line @typescript-eslint/no-explicit-any\ntreeNode, i18n) {\n    // prettier-ignore\n    const global = i18n.mode === 'composition'\n        ? i18n.global\n        : i18n.global.__composer;\n    if (instance && instance.vnode.el && instance.vnode.el.__VUE_I18N__) {\n        // add custom tags local scope only\n        if (instance.vnode.el.__VUE_I18N__ !== global) {\n            const tag = {\n                label: `i18n (${getI18nScopeLable(instance)} Scope)`,\n                textColor: 0x000000,\n                backgroundColor: 0xffcd19\n            };\n            treeNode.tags.push(tag);\n        }\n    }\n}\nfunction inspectComposer(instanceData, composer) {\n    const type = VUE_I18N_COMPONENT_TYPES;\n    instanceData.state.push({\n        type,\n        key: 'locale',\n        editable: true,\n        value: composer.locale.value\n    });\n    instanceData.state.push({\n        type,\n        key: 'availableLocales',\n        editable: false,\n        value: composer.availableLocales\n    });\n    instanceData.state.push({\n        type,\n        key: 'fallbackLocale',\n        editable: true,\n        value: composer.fallbackLocale.value\n    });\n    instanceData.state.push({\n        type,\n        key: 'inheritLocale',\n        editable: true,\n        value: composer.inheritLocale\n    });\n    instanceData.state.push({\n        type,\n        key: 'messages',\n        editable: false,\n        value: getLocaleMessageValue(composer.messages.value)\n    });\n    {\n        instanceData.state.push({\n            type,\n            key: 'datetimeFormats',\n            editable: false,\n            value: composer.datetimeFormats.value\n        });\n        instanceData.state.push({\n            type,\n            key: 'numberFormats',\n            editable: false,\n            value: composer.numberFormats.value\n        });\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getLocaleMessageValue(messages) {\n    const value = {};\n    Object.keys(messages).forEach((key) => {\n        const v = messages[key];\n        if (isFunction(v) && 'source' in v) {\n            value[key] = getMessageFunctionDetails(v);\n        }\n        else if (isMessageAST(v) && v.loc && v.loc.source) {\n            value[key] = v.loc.source;\n        }\n        else if (isObject(v)) {\n            value[key] = getLocaleMessageValue(v);\n        }\n        else {\n            value[key] = v;\n        }\n    });\n    return value;\n}\nconst ESC = {\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '&': '&amp;'\n};\nfunction escape(s) {\n    return s.replace(/[<>\"&]/g, escapeChar);\n}\nfunction escapeChar(a) {\n    return ESC[a] || a;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getMessageFunctionDetails(func) {\n    const argString = func.source ? `(\"${escape(func.source)}\")` : `(?)`;\n    return {\n        _custom: {\n            type: 'function',\n            display: `<span>ƒ</span> ${argString}`\n        }\n    };\n}\nfunction registerScope(payload, i18n) {\n    payload.rootNodes.push({\n        id: 'global',\n        label: 'Global Scope'\n    });\n    // prettier-ignore\n    const global = i18n.mode === 'composition'\n        ? i18n.global\n        : i18n.global.__composer;\n    for (const [keyInstance, instance] of i18n.__instances) {\n        // prettier-ignore\n        const composer = i18n.mode === 'composition'\n            ? instance\n            : instance.__composer;\n        if (global === composer) {\n            continue;\n        }\n        payload.rootNodes.push({\n            id: composer.id.toString(),\n            label: `${getI18nScopeLable(keyInstance)} Scope`\n        });\n    }\n}\nfunction getComponentInstance(nodeId, i18n) {\n    let instance = null;\n    if (nodeId !== 'global') {\n        for (const [component, composer] of i18n.__instances.entries()) {\n            if (composer.id.toString() === nodeId) {\n                instance = component;\n                break;\n            }\n        }\n    }\n    return instance;\n}\nfunction getComposer$1(nodeId, i18n) {\n    if (nodeId === 'global') {\n        return i18n.mode === 'composition'\n            ? i18n.global\n            : i18n.global.__composer;\n    }\n    else {\n        const instance = Array.from(i18n.__instances.values()).find(item => item.id.toString() === nodeId);\n        if (instance) {\n            return i18n.mode === 'composition'\n                ? instance\n                : instance.__composer;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction inspectScope(payload, i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n    const composer = getComposer$1(payload.nodeId, i18n);\n    if (composer) {\n        // TODO:\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        payload.state = makeScopeInspectState(composer);\n    }\n    return null;\n}\nfunction makeScopeInspectState(composer) {\n    const state = {};\n    const localeType = 'Locale related info';\n    const localeStates = [\n        {\n            type: localeType,\n            key: 'locale',\n            editable: true,\n            value: composer.locale.value\n        },\n        {\n            type: localeType,\n            key: 'fallbackLocale',\n            editable: true,\n            value: composer.fallbackLocale.value\n        },\n        {\n            type: localeType,\n            key: 'availableLocales',\n            editable: false,\n            value: composer.availableLocales\n        },\n        {\n            type: localeType,\n            key: 'inheritLocale',\n            editable: true,\n            value: composer.inheritLocale\n        }\n    ];\n    state[localeType] = localeStates;\n    const localeMessagesType = 'Locale messages info';\n    const localeMessagesStates = [\n        {\n            type: localeMessagesType,\n            key: 'messages',\n            editable: false,\n            value: getLocaleMessageValue(composer.messages.value)\n        }\n    ];\n    state[localeMessagesType] = localeMessagesStates;\n    {\n        const datetimeFormatsType = 'Datetime formats info';\n        const datetimeFormatsStates = [\n            {\n                type: datetimeFormatsType,\n                key: 'datetimeFormats',\n                editable: false,\n                value: composer.datetimeFormats.value\n            }\n        ];\n        state[datetimeFormatsType] = datetimeFormatsStates;\n        const numberFormatsType = 'Datetime formats info';\n        const numberFormatsStates = [\n            {\n                type: numberFormatsType,\n                key: 'numberFormats',\n                editable: false,\n                value: composer.numberFormats.value\n            }\n        ];\n        state[numberFormatsType] = numberFormatsStates;\n    }\n    return state;\n}\nfunction addTimelineEvent(event, payload) {\n    if (devtoolsApi) {\n        let groupId;\n        if (payload && 'groupId' in payload) {\n            groupId = payload.groupId;\n            delete payload.groupId;\n        }\n        devtoolsApi.addTimelineEvent({\n            layerId: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n            event: {\n                title: event,\n                groupId,\n                time: Date.now(),\n                meta: {},\n                data: payload || {},\n                logType: event === \"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */\n                    ? 'error'\n                    : event === \"fallback\" /* VueDevToolsTimelineEvents.FALBACK */ ||\n                        event === \"missing\" /* VueDevToolsTimelineEvents.MISSING */\n                        ? 'warning'\n                        : 'default'\n            }\n        });\n    }\n}\nfunction editScope(payload, i18n) {\n    const composer = getComposer$1(payload.nodeId, i18n);\n    if (composer) {\n        const [field] = payload.path;\n        if (field === 'locale' && isString(payload.state.value)) {\n            composer.locale.value = payload.state.value;\n        }\n        else if (field === 'fallbackLocale' &&\n            (isString(payload.state.value) ||\n                isArray(payload.state.value) ||\n                isObject(payload.state.value))) {\n            composer.fallbackLocale.value = payload.state.value;\n        }\n        else if (field === 'inheritLocale' && isBoolean(payload.state.value)) {\n            composer.inheritLocale = payload.state.value;\n        }\n    }\n}\n\n/**\n * Supports compatibility for legacy vue-i18n APIs\n * This mixin is used when we use vue-i18n@v9.x or later\n */\nfunction defineMixin(vuei18n, composer, i18n) {\n    return {\n        beforeCreate() {\n            const instance = getCurrentInstance();\n            /* istanbul ignore if */\n            if (!instance) {\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n            }\n            const options = this.$options;\n            if (options.i18n) {\n                const optionsI18n = options.i18n;\n                if (options.__i18n) {\n                    optionsI18n.__i18n = options.__i18n;\n                }\n                optionsI18n.__root = composer;\n                if (this === this.$root) {\n                    // merge option and gttach global\n                    this.$i18n = mergeToGlobal(vuei18n, optionsI18n);\n                }\n                else {\n                    optionsI18n.__injectWithOption = true;\n                    optionsI18n.__extender = i18n.__vueI18nExtend;\n                    // atttach local VueI18n instance\n                    this.$i18n = createVueI18n(optionsI18n);\n                    // extend VueI18n instance\n                    const _vueI18n = this.$i18n;\n                    if (_vueI18n.__extender) {\n                        _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n                    }\n                }\n            }\n            else if (options.__i18n) {\n                if (this === this.$root) {\n                    // merge option and gttach global\n                    this.$i18n = mergeToGlobal(vuei18n, options);\n                }\n                else {\n                    // atttach local VueI18n instance\n                    this.$i18n = createVueI18n({\n                        __i18n: options.__i18n,\n                        __injectWithOption: true,\n                        __extender: i18n.__vueI18nExtend,\n                        __root: composer\n                    });\n                    // extend VueI18n instance\n                    const _vueI18n = this.$i18n;\n                    if (_vueI18n.__extender) {\n                        _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n                    }\n                }\n            }\n            else {\n                // attach global VueI18n instance\n                this.$i18n = vuei18n;\n            }\n            if (options.__i18nGlobal) {\n                adjustI18nResources(composer, options, options);\n            }\n            // defines vue-i18n legacy APIs\n            this.$t = (...args) => this.$i18n.t(...args);\n            this.$rt = (...args) => this.$i18n.rt(...args);\n            this.$tc = (...args) => this.$i18n.tc(...args);\n            this.$te = (key, locale) => this.$i18n.te(key, locale);\n            this.$d = (...args) => this.$i18n.d(...args);\n            this.$n = (...args) => this.$i18n.n(...args);\n            this.$tm = (key) => this.$i18n.tm(key);\n            i18n.__setInstance(instance, this.$i18n);\n        },\n        mounted() {\n            /* istanbul ignore if */\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                this.$el &&\n                this.$i18n) {\n                const _vueI18n = this.$i18n;\n                this.$el.__VUE_I18N__ = _vueI18n.__composer;\n                const emitter = (this.__v_emitter =\n                    createEmitter());\n                _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n                emitter.on('*', addTimelineEvent);\n            }\n        },\n        unmounted() {\n            const instance = getCurrentInstance();\n            /* istanbul ignore if */\n            if (!instance) {\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n            }\n            const _vueI18n = this.$i18n;\n            /* istanbul ignore if */\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                this.$el &&\n                this.$el.__VUE_I18N__) {\n                if (this.__v_emitter) {\n                    this.__v_emitter.off('*', addTimelineEvent);\n                    delete this.__v_emitter;\n                }\n                if (this.$i18n) {\n                    _vueI18n.__disableEmitter && _vueI18n.__disableEmitter();\n                    delete this.$el.__VUE_I18N__;\n                }\n            }\n            delete this.$t;\n            delete this.$rt;\n            delete this.$tc;\n            delete this.$te;\n            delete this.$d;\n            delete this.$n;\n            delete this.$tm;\n            if (_vueI18n.__disposer) {\n                _vueI18n.__disposer();\n                delete _vueI18n.__disposer;\n                delete _vueI18n.__extender;\n            }\n            i18n.__deleteInstance(instance);\n            delete this.$i18n;\n        }\n    };\n}\nfunction mergeToGlobal(g, options) {\n    g.locale = options.locale || g.locale;\n    g.fallbackLocale = options.fallbackLocale || g.fallbackLocale;\n    g.missing = options.missing || g.missing;\n    g.silentTranslationWarn =\n        options.silentTranslationWarn || g.silentFallbackWarn;\n    g.silentFallbackWarn = options.silentFallbackWarn || g.silentFallbackWarn;\n    g.formatFallbackMessages =\n        options.formatFallbackMessages || g.formatFallbackMessages;\n    g.postTranslation = options.postTranslation || g.postTranslation;\n    g.warnHtmlInMessage = options.warnHtmlInMessage || g.warnHtmlInMessage;\n    g.escapeParameterHtml = options.escapeParameterHtml || g.escapeParameterHtml;\n    g.sync = options.sync || g.sync;\n    g.__composer[SetPluralRulesSymbol](options.pluralizationRules || g.pluralizationRules);\n    const messages = getLocaleMessages(g.locale, {\n        messages: options.messages,\n        __i18n: options.__i18n\n    });\n    Object.keys(messages).forEach(locale => g.mergeLocaleMessage(locale, messages[locale]));\n    if (options.datetimeFormats) {\n        Object.keys(options.datetimeFormats).forEach(locale => g.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));\n    }\n    if (options.numberFormats) {\n        Object.keys(options.numberFormats).forEach(locale => g.mergeNumberFormat(locale, options.numberFormats[locale]));\n    }\n    return g;\n}\n\n/**\n * Injection key for {@link useI18n}\n *\n * @remarks\n * The global injection key for I18n instances with `useI18n`. this injection key is used in Web Components.\n * Specify the i18n instance created by {@link createI18n} together with `provide` function.\n *\n * @VueI18nGeneral\n */\nconst I18nInjectionKey = \n/* #__PURE__*/ makeSymbol('global-vue-i18n');\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction createI18n(options = {}, VueI18nLegacy) {\n    // prettier-ignore\n    const __legacyMode = __VUE_I18N_LEGACY_API__ && isBoolean(options.legacy)\n            ? options.legacy\n            : __VUE_I18N_LEGACY_API__;\n    // prettier-ignore\n    const __globalInjection = isBoolean(options.globalInjection)\n        ? options.globalInjection\n        : true;\n    // prettier-ignore\n    const __allowComposition = __VUE_I18N_LEGACY_API__ && __legacyMode\n            ? !!options.allowComposition\n            : true;\n    const __instances = new Map();\n    const [globalScope, __global] = createGlobal(options, __legacyMode);\n    const symbol = /* #__PURE__*/ makeSymbol((process.env.NODE_ENV !== 'production') ? 'vue-i18n' : '');\n    if ((process.env.NODE_ENV !== 'production')) {\n        if (__legacyMode && __allowComposition && !false) {\n            warn(getWarnMessage(I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION));\n        }\n    }\n    function __getInstance(component) {\n        return __instances.get(component) || null;\n    }\n    function __setInstance(component, instance) {\n        __instances.set(component, instance);\n    }\n    function __deleteInstance(component) {\n        __instances.delete(component);\n    }\n    {\n        const i18n = {\n            // mode\n            get mode() {\n                return __VUE_I18N_LEGACY_API__ && __legacyMode\n                    ? 'legacy'\n                    : 'composition';\n            },\n            // allowComposition\n            get allowComposition() {\n                return __allowComposition;\n            },\n            // install plugin\n            async install(app, ...options) {\n                if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                    !false) {\n                    app.__VUE_I18N__ = i18n;\n                }\n                // setup global provider\n                app.__VUE_I18N_SYMBOL__ = symbol;\n                app.provide(app.__VUE_I18N_SYMBOL__, i18n);\n                // set composer & vuei18n extend hook options from plugin options\n                if (isPlainObject(options[0])) {\n                    const opts = options[0];\n                    i18n.__composerExtend =\n                        opts.__composerExtend;\n                    i18n.__vueI18nExtend =\n                        opts.__vueI18nExtend;\n                }\n                // global method and properties injection for Composition API\n                let globalReleaseHandler = null;\n                if (!__legacyMode && __globalInjection) {\n                    globalReleaseHandler = injectGlobalFields(app, i18n.global);\n                }\n                // install built-in components and directive\n                if (__VUE_I18N_FULL_INSTALL__) {\n                    apply(app, i18n, ...options);\n                }\n                // setup mixin for Legacy API\n                if (__VUE_I18N_LEGACY_API__ && __legacyMode) {\n                    app.mixin(defineMixin(__global, __global.__composer, i18n));\n                }\n                // release global scope\n                const unmountApp = app.unmount;\n                app.unmount = () => {\n                    globalReleaseHandler && globalReleaseHandler();\n                    i18n.dispose();\n                    unmountApp();\n                };\n                // setup vue-devtools plugin\n                if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && !false) {\n                    const ret = await enableDevTools(app, i18n);\n                    if (!ret) {\n                        throw createI18nError(I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN);\n                    }\n                    const emitter = createEmitter();\n                    if (__legacyMode) {\n                        const _vueI18n = __global;\n                        _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n                    }\n                    else {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        const _composer = __global;\n                        _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n                    }\n                    emitter.on('*', addTimelineEvent);\n                }\n            },\n            // global accessor\n            get global() {\n                return __global;\n            },\n            dispose() {\n                globalScope.stop();\n            },\n            // @internal\n            __instances,\n            // @internal\n            __getInstance,\n            // @internal\n            __setInstance,\n            // @internal\n            __deleteInstance\n        };\n        return i18n;\n    }\n}\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction useI18n(options = {}) {\n    const instance = getCurrentInstance();\n    if (instance == null) {\n        throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);\n    }\n    if (!instance.isCE &&\n        instance.appContext.app != null &&\n        !instance.appContext.app.__VUE_I18N_SYMBOL__) {\n        throw createI18nError(I18nErrorCodes.NOT_INSTALLED);\n    }\n    const i18n = getI18nInstance(instance);\n    const gl = getGlobalComposer(i18n);\n    const componentOptions = getComponentOptions(instance);\n    const scope = getScope(options, componentOptions);\n    if (__VUE_I18N_LEGACY_API__) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (i18n.mode === 'legacy' && !options.__useComponent) {\n            if (!i18n.allowComposition) {\n                throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);\n            }\n            return useI18nForLegacy(instance, scope, gl, options);\n        }\n    }\n    if (scope === 'global') {\n        adjustI18nResources(gl, options, componentOptions);\n        return gl;\n    }\n    if (scope === 'parent') {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        let composer = getComposer(i18n, instance, options.__useComponent);\n        if (composer == null) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn(getWarnMessage(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));\n            }\n            composer = gl;\n        }\n        return composer;\n    }\n    const i18nInternal = i18n;\n    let composer = i18nInternal.__getInstance(instance);\n    if (composer == null) {\n        const composerOptions = assign({}, options);\n        if ('__i18n' in componentOptions) {\n            composerOptions.__i18n = componentOptions.__i18n;\n        }\n        if (gl) {\n            composerOptions.__root = gl;\n        }\n        composer = createComposer(composerOptions);\n        if (i18nInternal.__composerExtend) {\n            composer[DisposeSymbol] =\n                i18nInternal.__composerExtend(composer);\n        }\n        setupLifeCycle(i18nInternal, instance, composer);\n        i18nInternal.__setInstance(instance, composer);\n    }\n    return composer;\n}\n/**\n * Cast to VueI18n legacy compatible type\n *\n * @remarks\n * This API is provided only with [vue-i18n-bridge](https://vue-i18n.intlify.dev/guide/migration/ways.html#what-is-vue-i18n-bridge).\n *\n * The purpose of this function is to convert an {@link I18n} instance created with {@link createI18n | createI18n(legacy: true)} into a `vue-i18n@v8.x` compatible instance of `new VueI18n` in a TypeScript environment.\n *\n * @param i18n - An instance of {@link I18n}\n * @returns A i18n instance which is casted to {@link VueI18n} type\n *\n * @VueI18nTip\n * :new: provided by **vue-i18n-bridge only**\n *\n * @VueI18nGeneral\n */\n/* #__NO_SIDE_EFFECTS__ */\nconst castToVueI18n = (i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n    if (!(__VUE_I18N_BRIDGE__ in i18n)) {\n        throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);\n    }\n    return i18n;\n};\nfunction createGlobal(options, legacyMode, VueI18nLegacy // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    const scope = effectScope();\n    {\n        const obj = __VUE_I18N_LEGACY_API__ && legacyMode\n            ? scope.run(() => createVueI18n(options))\n            : scope.run(() => createComposer(options));\n        if (obj == null) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        return [scope, obj];\n    }\n}\nfunction getI18nInstance(instance) {\n    {\n        const i18n = inject(!instance.isCE\n            ? instance.appContext.app.__VUE_I18N_SYMBOL__\n            : I18nInjectionKey);\n        /* istanbul ignore if */\n        if (!i18n) {\n            throw createI18nError(!instance.isCE\n                ? I18nErrorCodes.UNEXPECTED_ERROR\n                : I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE);\n        }\n        return i18n;\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getScope(options, componentOptions) {\n    // prettier-ignore\n    return isEmptyObject(options)\n        ? ('__i18n' in componentOptions)\n            ? 'local'\n            : 'global'\n        : !options.useScope\n            ? 'local'\n            : options.useScope;\n}\nfunction getGlobalComposer(i18n) {\n    // prettier-ignore\n    return i18n.mode === 'composition'\n            ? i18n.global\n            : i18n.global.__composer\n        ;\n}\nfunction getComposer(i18n, target, useComponent = false) {\n    let composer = null;\n    const root = target.root;\n    let current = getParentComponentInstance(target, useComponent);\n    while (current != null) {\n        const i18nInternal = i18n;\n        if (i18n.mode === 'composition') {\n            composer = i18nInternal.__getInstance(current);\n        }\n        else {\n            if (__VUE_I18N_LEGACY_API__) {\n                const vueI18n = i18nInternal.__getInstance(current);\n                if (vueI18n != null) {\n                    composer = vueI18n\n                        .__composer;\n                    if (useComponent &&\n                        composer &&\n                        !composer[InejctWithOptionSymbol] // eslint-disable-line @typescript-eslint/no-explicit-any\n                    ) {\n                        composer = null;\n                    }\n                }\n            }\n        }\n        if (composer != null) {\n            break;\n        }\n        if (root === current) {\n            break;\n        }\n        current = current.parent;\n    }\n    return composer;\n}\nfunction getParentComponentInstance(target, useComponent = false) {\n    if (target == null) {\n        return null;\n    }\n    {\n        // if `useComponent: true` will be specified, we get lexical scope owner instance for use-case slots\n        return !useComponent\n            ? target.parent\n            : target.vnode.ctx || target.parent; // eslint-disable-line @typescript-eslint/no-explicit-any\n    }\n}\nfunction setupLifeCycle(i18n, target, composer) {\n    let emitter = null;\n    {\n        onMounted(() => {\n            // inject composer instance to DOM for intlify-devtools\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                target.vnode.el) {\n                target.vnode.el.__VUE_I18N__ = composer;\n                emitter = createEmitter();\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const _composer = composer;\n                _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n                emitter.on('*', addTimelineEvent);\n            }\n        }, target);\n        onUnmounted(() => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const _composer = composer;\n            // remove composer instance from DOM for intlify-devtools\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                target.vnode.el &&\n                target.vnode.el.__VUE_I18N__) {\n                emitter && emitter.off('*', addTimelineEvent);\n                _composer[DisableEmitter] && _composer[DisableEmitter]();\n                delete target.vnode.el.__VUE_I18N__;\n            }\n            i18n.__deleteInstance(target);\n            // dispose extended resources\n            const dispose = _composer[DisposeSymbol];\n            if (dispose) {\n                dispose();\n                delete _composer[DisposeSymbol];\n            }\n        }, target);\n    }\n}\nfunction useI18nForLegacy(instance, scope, root, options = {} // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    const isLocalScope = scope === 'local';\n    const _composer = shallowRef(null);\n    if (isLocalScope &&\n        instance.proxy &&\n        !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {\n        throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);\n    }\n    const _inheritLocale = isBoolean(options.inheritLocale)\n        ? options.inheritLocale\n        : !isString(options.locale);\n    const _locale = ref(\n    // prettier-ignore\n    !isLocalScope || _inheritLocale\n        ? root.locale.value\n        : isString(options.locale)\n            ? options.locale\n            : DEFAULT_LOCALE);\n    const _fallbackLocale = ref(\n    // prettier-ignore\n    !isLocalScope || _inheritLocale\n        ? root.fallbackLocale.value\n        : isString(options.fallbackLocale) ||\n            isArray(options.fallbackLocale) ||\n            isPlainObject(options.fallbackLocale) ||\n            options.fallbackLocale === false\n            ? options.fallbackLocale\n            : _locale.value);\n    const _messages = ref(getLocaleMessages(_locale.value, options));\n    // prettier-ignore\n    const _datetimeFormats = ref(isPlainObject(options.datetimeFormats)\n        ? options.datetimeFormats\n        : { [_locale.value]: {} });\n    // prettier-ignore\n    const _numberFormats = ref(isPlainObject(options.numberFormats)\n        ? options.numberFormats\n        : { [_locale.value]: {} });\n    // prettier-ignore\n    const _missingWarn = isLocalScope\n        ? root.missingWarn\n        : isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n            ? options.missingWarn\n            : true;\n    // prettier-ignore\n    const _fallbackWarn = isLocalScope\n        ? root.fallbackWarn\n        : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n            ? options.fallbackWarn\n            : true;\n    // prettier-ignore\n    const _fallbackRoot = isLocalScope\n        ? root.fallbackRoot\n        : isBoolean(options.fallbackRoot)\n            ? options.fallbackRoot\n            : true;\n    // configure fall back to root\n    const _fallbackFormat = !!options.fallbackFormat;\n    // runtime missing\n    const _missing = isFunction(options.missing) ? options.missing : null;\n    // postTranslation handler\n    const _postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    // prettier-ignore\n    const _warnHtmlMessage = isLocalScope\n        ? root.warnHtmlMessage\n        : isBoolean(options.warnHtmlMessage)\n            ? options.warnHtmlMessage\n            : true;\n    const _escapeParameter = !!options.escapeParameter;\n    // prettier-ignore\n    const _modifiers = isLocalScope\n        ? root.modifiers\n        : isPlainObject(options.modifiers)\n            ? options.modifiers\n            : {};\n    // pluralRules\n    const _pluralRules = options.pluralRules || (isLocalScope && root.pluralRules);\n    // track reactivity\n    function trackReactivityValues() {\n        return [\n            _locale.value,\n            _fallbackLocale.value,\n            _messages.value,\n            _datetimeFormats.value,\n            _numberFormats.value\n        ];\n    }\n    // locale\n    const locale = computed({\n        get: () => {\n            return _composer.value ? _composer.value.locale.value : _locale.value;\n        },\n        set: val => {\n            if (_composer.value) {\n                _composer.value.locale.value = val;\n            }\n            _locale.value = val;\n        }\n    });\n    // fallbackLocale\n    const fallbackLocale = computed({\n        get: () => {\n            return _composer.value\n                ? _composer.value.fallbackLocale.value\n                : _fallbackLocale.value;\n        },\n        set: val => {\n            if (_composer.value) {\n                _composer.value.fallbackLocale.value = val;\n            }\n            _fallbackLocale.value = val;\n        }\n    });\n    // messages\n    const messages = computed(() => {\n        if (_composer.value) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return _composer.value.messages.value;\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return _messages.value;\n        }\n    });\n    const datetimeFormats = computed(() => _datetimeFormats.value);\n    const numberFormats = computed(() => _numberFormats.value);\n    function getPostTranslationHandler() {\n        return _composer.value\n            ? _composer.value.getPostTranslationHandler()\n            : _postTranslation;\n    }\n    function setPostTranslationHandler(handler) {\n        if (_composer.value) {\n            _composer.value.setPostTranslationHandler(handler);\n        }\n    }\n    function getMissingHandler() {\n        return _composer.value ? _composer.value.getMissingHandler() : _missing;\n    }\n    function setMissingHandler(handler) {\n        if (_composer.value) {\n            _composer.value.setMissingHandler(handler);\n        }\n    }\n    function warpWithDeps(fn) {\n        trackReactivityValues();\n        return fn();\n    }\n    function t(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function rt(...args) {\n        return _composer.value\n            ? Reflect.apply(_composer.value.rt, null, [...args])\n            : '';\n    }\n    function d(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function n(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function tm(key) {\n        return _composer.value ? _composer.value.tm(key) : {};\n    }\n    function te(key, locale) {\n        return _composer.value ? _composer.value.te(key, locale) : false;\n    }\n    function getLocaleMessage(locale) {\n        return _composer.value ? _composer.value.getLocaleMessage(locale) : {};\n    }\n    function setLocaleMessage(locale, message) {\n        if (_composer.value) {\n            _composer.value.setLocaleMessage(locale, message);\n            _messages.value[locale] = message;\n        }\n    }\n    function mergeLocaleMessage(locale, message) {\n        if (_composer.value) {\n            _composer.value.mergeLocaleMessage(locale, message);\n        }\n    }\n    function getDateTimeFormat(locale) {\n        return _composer.value ? _composer.value.getDateTimeFormat(locale) : {};\n    }\n    function setDateTimeFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.setDateTimeFormat(locale, format);\n            _datetimeFormats.value[locale] = format;\n        }\n    }\n    function mergeDateTimeFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.mergeDateTimeFormat(locale, format);\n        }\n    }\n    function getNumberFormat(locale) {\n        return _composer.value ? _composer.value.getNumberFormat(locale) : {};\n    }\n    function setNumberFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.setNumberFormat(locale, format);\n            _numberFormats.value[locale] = format;\n        }\n    }\n    function mergeNumberFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.mergeNumberFormat(locale, format);\n        }\n    }\n    const wrapper = {\n        get id() {\n            return _composer.value ? _composer.value.id : -1;\n        },\n        locale,\n        fallbackLocale,\n        messages,\n        datetimeFormats,\n        numberFormats,\n        get inheritLocale() {\n            return _composer.value ? _composer.value.inheritLocale : _inheritLocale;\n        },\n        set inheritLocale(val) {\n            if (_composer.value) {\n                _composer.value.inheritLocale = val;\n            }\n        },\n        get availableLocales() {\n            return _composer.value\n                ? _composer.value.availableLocales\n                : Object.keys(_messages.value);\n        },\n        get modifiers() {\n            return (_composer.value ? _composer.value.modifiers : _modifiers);\n        },\n        get pluralRules() {\n            return (_composer.value ? _composer.value.pluralRules : _pluralRules);\n        },\n        get isGlobal() {\n            return _composer.value ? _composer.value.isGlobal : false;\n        },\n        get missingWarn() {\n            return _composer.value ? _composer.value.missingWarn : _missingWarn;\n        },\n        set missingWarn(val) {\n            if (_composer.value) {\n                _composer.value.missingWarn = val;\n            }\n        },\n        get fallbackWarn() {\n            return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;\n        },\n        set fallbackWarn(val) {\n            if (_composer.value) {\n                _composer.value.missingWarn = val;\n            }\n        },\n        get fallbackRoot() {\n            return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;\n        },\n        set fallbackRoot(val) {\n            if (_composer.value) {\n                _composer.value.fallbackRoot = val;\n            }\n        },\n        get fallbackFormat() {\n            return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;\n        },\n        set fallbackFormat(val) {\n            if (_composer.value) {\n                _composer.value.fallbackFormat = val;\n            }\n        },\n        get warnHtmlMessage() {\n            return _composer.value\n                ? _composer.value.warnHtmlMessage\n                : _warnHtmlMessage;\n        },\n        set warnHtmlMessage(val) {\n            if (_composer.value) {\n                _composer.value.warnHtmlMessage = val;\n            }\n        },\n        get escapeParameter() {\n            return _composer.value\n                ? _composer.value.escapeParameter\n                : _escapeParameter;\n        },\n        set escapeParameter(val) {\n            if (_composer.value) {\n                _composer.value.escapeParameter = val;\n            }\n        },\n        t,\n        getPostTranslationHandler,\n        setPostTranslationHandler,\n        getMissingHandler,\n        setMissingHandler,\n        rt,\n        d,\n        n,\n        tm,\n        te,\n        getLocaleMessage,\n        setLocaleMessage,\n        mergeLocaleMessage,\n        getDateTimeFormat,\n        setDateTimeFormat,\n        mergeDateTimeFormat,\n        getNumberFormat,\n        setNumberFormat,\n        mergeNumberFormat\n    };\n    function sync(composer) {\n        composer.locale.value = _locale.value;\n        composer.fallbackLocale.value = _fallbackLocale.value;\n        Object.keys(_messages.value).forEach(locale => {\n            composer.mergeLocaleMessage(locale, _messages.value[locale]);\n        });\n        Object.keys(_datetimeFormats.value).forEach(locale => {\n            composer.mergeDateTimeFormat(locale, _datetimeFormats.value[locale]);\n        });\n        Object.keys(_numberFormats.value).forEach(locale => {\n            composer.mergeNumberFormat(locale, _numberFormats.value[locale]);\n        });\n        composer.escapeParameter = _escapeParameter;\n        composer.fallbackFormat = _fallbackFormat;\n        composer.fallbackRoot = _fallbackRoot;\n        composer.fallbackWarn = _fallbackWarn;\n        composer.missingWarn = _missingWarn;\n        composer.warnHtmlMessage = _warnHtmlMessage;\n    }\n    onBeforeMount(() => {\n        if (instance.proxy == null || instance.proxy.$i18n == null) {\n            throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const composer = (_composer.value = instance.proxy.$i18n\n            .__composer);\n        if (scope === 'global') {\n            _locale.value = composer.locale.value;\n            _fallbackLocale.value = composer.fallbackLocale.value;\n            _messages.value = composer.messages.value;\n            _datetimeFormats.value = composer.datetimeFormats.value;\n            _numberFormats.value = composer.numberFormats.value;\n        }\n        else if (isLocalScope) {\n            sync(composer);\n        }\n    });\n    return wrapper;\n}\nconst globalExportProps = [\n    'locale',\n    'fallbackLocale',\n    'availableLocales'\n];\nconst globalExportMethods = ['t', 'rt', 'd', 'n', 'tm', 'te']\n    ;\nfunction injectGlobalFields(app, composer) {\n    const i18n = Object.create(null);\n    globalExportProps.forEach(prop => {\n        const desc = Object.getOwnPropertyDescriptor(composer, prop);\n        if (!desc) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        const wrap = isRef(desc.value) // check computed props\n            ? {\n                get() {\n                    return desc.value.value;\n                },\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                set(val) {\n                    desc.value.value = val;\n                }\n            }\n            : {\n                get() {\n                    return desc.get && desc.get();\n                }\n            };\n        Object.defineProperty(i18n, prop, wrap);\n    });\n    app.config.globalProperties.$i18n = i18n;\n    globalExportMethods.forEach(method => {\n        const desc = Object.getOwnPropertyDescriptor(composer, method);\n        if (!desc || !desc.value) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);\n    });\n    const dispose = () => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        delete app.config.globalProperties.$i18n;\n        globalExportMethods.forEach(method => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            delete app.config.globalProperties[`$${method}`];\n        });\n    };\n    return dispose;\n}\n\n{\n    initFeatureFlags();\n}\n// register message compiler at vue-i18n\nif (__INTLIFY_JIT_COMPILATION__) {\n    registerMessageCompiler(compile);\n}\nelse {\n    registerMessageCompiler(compileToFunction);\n}\n// register message resolver at vue-i18n\nregisterMessageResolver(resolveValue);\n// register fallback locale at vue-i18n\nregisterLocaleFallbacker(fallbackWithLocaleChain);\n// NOTE: experimental !!\nif ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n    const target = getGlobalThis();\n    target.__INTLIFY__ = true;\n    setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);\n}\nif ((process.env.NODE_ENV !== 'production')) ;\n\nexport { DatetimeFormat, I18nD, I18nInjectionKey, I18nN, I18nT, NumberFormat, Translation, VERSION, castToVueI18n, createI18n, useI18n, vTDirective };\n"], "names": ["perf", "RE_ARGS", "format", "isObject", "assign", "isString", "join", "code", "src", "des", "warnMessages", "errorMessages", "index", "context", "isLiteral", "parse", "baseCompile", "initFeatureFlags", "type", "code$1", "inc$1", "getWarnMessage", "format$1", "inc", "VERSION", "resolveValue", "msg", "source", "message", "locale", "locales", "_context", "messages", "options", "global", "composer"], "mappings": ";;;AAAO,SAAS,wBAAwB;AACpC,SAAO,UAAW,EAAC;AACvB;AAFgB;AAGT,SAAS,YAAY;AAExB,SAAQ,OAAO,cAAc,eAAe,OAAO,WAAW,cACxD,SACA,OAAO,eAAe,cAClB,aACA;AACd;AAPgB;AAQT,MAAM,mBAAmB,OAAO,UAAU;ACX1C,MAAM,aAAa;AACnB,MAAM,2BAA2B;ACDxC,IAAI;AACJ,IAAI;AACG,SAAS,yBAAyB;AACrC,MAAI;AACJ,MAAI,cAAc,QAAW;AACzB,WAAO;AAAA,EACV;AACD,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AACrD,gBAAY;AACZ,WAAO,OAAO;AAAA,EACjB,WACQ,OAAO,eAAe,iBAAiB,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAC9H,gBAAY;AACZ,WAAO,WAAW,WAAW;AAAA,EAChC,OACI;AACD,gBAAY;AAAA,EACf;AACD,SAAO;AACX;AAjBgB;AAkBT,SAAS,MAAM;AAClB,SAAO,uBAAwB,IAAG,KAAK,IAAG,IAAK,KAAK;AACxD;AAFgB;AClBT,MAAM,SAAS;AAAA,SAAA;AAAA;AAAA;AAAA,EAClB,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,UAAM,kBAAkB,CAAA;AACxB,QAAI,OAAO,UAAU;AACjB,iBAAW,MAAM,OAAO,UAAU;AAC9B,cAAM,OAAO,OAAO,SAAS,EAAE;AAC/B,wBAAgB,EAAE,IAAI,KAAK;AAAA,MAC9B;AAAA,IACJ;AACD,UAAM,sBAAsB,mCAAmC,OAAO,EAAE;AACxE,QAAI,kBAAkB,OAAO,OAAO,CAAE,GAAE,eAAe;AACvD,QAAI;AACA,YAAM,MAAM,aAAa,QAAQ,mBAAmB;AACpD,YAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,aAAO,OAAO,iBAAiB,IAAI;AAAA,IACtC,SACM,GAAG;AAAA,IAET;AACD,SAAK,YAAY;AAAA,MACb,cAAc;AACV,eAAO;AAAA,MACV;AAAA,MACD,YAAY,OAAO;AACf,YAAI;AACA,uBAAa,QAAQ,qBAAqB,KAAK,UAAU,KAAK,CAAC;AAAA,QAClE,SACM,GAAG;AAAA,QAET;AACD,0BAAkB;AAAA,MACrB;AAAA,MACD,MAAM;AACF,eAAO,IAAG;AAAA,MACb;AAAA,IACb;AACQ,QAAI,MAAM;AACN,WAAK,GAAG,0BAA0B,CAAC,UAAU,UAAU;AACnD,YAAI,aAAa,KAAK,OAAO,IAAI;AAC7B,eAAK,UAAU,YAAY,KAAK;AAAA,QACnC;AAAA,MACjB,CAAa;AAAA,IACJ;AACD,SAAK,YAAY,IAAI,MAAM,IAAI;AAAA,MAC3B,KAAK,wBAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,GAAG,IAAI;AAAA,QAC7B,OACI;AACD,iBAAO,IAAI,SAAS;AAChB,iBAAK,QAAQ,KAAK;AAAA,cACd,QAAQ;AAAA,cACR;AAAA,YAC5B,CAAyB;AAAA,UACzB;AAAA,QACiB;AAAA,MACJ,GAZI;AAAA,IAajB,CAAS;AACD,SAAK,gBAAgB,IAAI,MAAM,IAAI;AAAA,MAC/B,KAAK,wBAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,IAAI;AAAA,QAC1B,WACQ,SAAS,MAAM;AACpB,iBAAO,KAAK;AAAA,QACf,WACQ,OAAO,KAAK,KAAK,SAAS,EAAE,SAAS,IAAI,GAAG;AACjD,iBAAO,IAAI,SAAS;AAChB,iBAAK,YAAY,KAAK;AAAA,cAClB,QAAQ;AAAA,cACR;AAAA,cACA,SAAS,6BAAM;AAAA,cAAG,GAAT;AAAA,YACrC,CAAyB;AACD,mBAAO,KAAK,UAAU,IAAI,EAAE,GAAG,IAAI;AAAA,UAC3D;AAAA,QACiB,OACI;AACD,iBAAO,IAAI,SAAS;AAChB,mBAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,mBAAK,YAAY,KAAK;AAAA,gBAClB,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cAChC,CAA6B;AAAA,YAC7B,CAAyB;AAAA,UACzB;AAAA,QACiB;AAAA,MACJ,GA5BI;AAAA,IA6BjB,CAAS;AAAA,EACJ;AAAA,EACD,MAAM,cAAc,QAAQ;AACxB,SAAK,SAAS;AACd,eAAW,QAAQ,KAAK,SAAS;AAC7B,WAAK,OAAO,GAAG,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI;AAAA,IAC3C;AACD,eAAW,QAAQ,KAAK,aAAa;AACjC,WAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,IAC5D;AAAA,EACJ;AACL;ACpGO,SAAS,oBAAoB,kBAAkB,SAAS;AAC3D,QAAM,aAAa;AACnB,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,cAAc,oBAAoB,WAAW;AACnD,MAAI,SAAS,OAAO,yCAAyC,CAAC,cAAc;AACxE,SAAK,KAAK,YAAY,kBAAkB,OAAO;AAAA,EAClD,OACI;AACD,UAAM,QAAQ,cAAc,IAAI,SAAS,YAAY,IAAI,IAAI;AAC7D,UAAM,OAAO,OAAO,2BAA2B,OAAO,4BAA4B,CAAA;AAClF,SAAK,KAAK;AAAA,MACN,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,IACZ,CAAS;AACD,QAAI,OAAO;AACP,cAAQ,MAAM,aAAa;AAAA,IAC9B;AAAA,EACJ;AACL;AApBgB;ACNhB;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,YAAY,OAAO,WAAW;AACpC,IAAI;AACJ,IAAI;AACJ,IAAK,OAAwC;AACnC,QAAAA,QAAO,aAAa,OAAO;AACjC,MAAIA,SACAA,MAAK,QACLA,MAAK,WACLA,MAAK;AAAA,EAELA,MAAK,eAAe;AACpB,WAAO,wBAAC,QAAQ;AACZ,MAAAA,MAAK,KAAK,GAAG;AAAA,IAAA,GADV;AAGG,cAAA,wBAAC,MAAM,UAAU,WAAW;AAC7B,MAAAA,MAAA,QAAQ,MAAM,UAAU,MAAM;AACnC,MAAAA,MAAK,WAAW,QAAQ;AACxB,MAAAA,MAAK,WAAW,MAAM;AAAA,IAAA,GAHhB;AAAA,EAKd;AACJ;AACA,MAAMC,YAAU;AAEhB,SAASC,SAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAKC,WAAS,KAAK,CAAC,CAAC,GAAG;AACxC,WAAO,KAAK,CAAC;AAAA,EACjB;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAA;AAAA,EACX;AACA,SAAO,QAAQ,QAAQF,WAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EAAA,CAC/D;AACL;AAVSC;AAWT,MAAM,aAAa,wBAAC,MAAM,YAAY,UAAU,CAAC,YAAY,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI,GAAxE;AACnB,MAAM,yBAAyB,wBAAC,QAAQ,KAAK,WAAW,sBAAsB,EAAE,GAAG,QAAQ,GAAG,KAAK,GAAG,OAAQ,CAAA,GAA/E;AAC/B,MAAM,wBAAwB,wBAAC,SAAS,KAAK,UAAU,IAAI,EACtD,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,GAHH;AAI9B,MAAM,WAAW,wBAAC,QAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG,GAAhD;AACjB,MAAM,SAAS,wBAAC,QAAQ,aAAa,GAAG,MAAM,iBAA/B;AACf,MAAM,WAAW,wBAAC,QAAQ,aAAa,GAAG,MAAM,mBAA/B;AACjB,MAAM,gBAAgB,wBAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW,GAA3D;AACtB,MAAME,WAAS,OAAO;AACtB,MAAM,UAAU,OAAO;AACvB,MAAM,SAAS,wBAAC,MAAM,SAAS,QAAQ,GAAG,GAA3B;AACf,IAAI;AACJ,MAAM,gBAAgB,6BAAM;AAExB,SAAQ,gBACH,cACG,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,OAAO;AACrC,GAbsB;AActB,SAAS,WAAW,SAAS;AACzB,SAAO,QACF,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAC/B;AANS;AAOT,MAAM,iBAAiB,OAAO,UAAU;AACxC,SAAS,OAAO,KAAK,KAAK;AACf,SAAA,eAAe,KAAK,KAAK,GAAG;AACvC;AAFS;AAWT,MAAM,UAAU,MAAM;AACtB,MAAM,aAAa,wBAAC,QAAQ,OAAO,QAAQ,YAAxB;AACnB,MAAMC,aAAW,wBAAC,QAAQ,OAAO,QAAQ,UAAxB;AACjB,MAAM,YAAY,wBAAC,QAAQ,OAAO,QAAQ,WAAxB;AAClB,MAAM,WAAW,wBAAC,QAAQ,OAAO,QAAQ,UAAxB;AAEjB,MAAMF,aAAW,wBAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,UAAxC;AAEjB,MAAM,YAAY,wBAAC,QAAQ;AAChB,SAAAA,WAAS,GAAG,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AACxE,GAFkB;AAGlB,MAAM,iBAAiB,OAAO,UAAU;AACxC,MAAM,eAAe,wBAAC,UAAU,eAAe,KAAK,KAAK,GAApC;AACrB,MAAM,gBAAgB,wBAAC,QAAQ;AACvB,MAAA,CAACA,WAAS,GAAG;AACN,WAAA;AACL,QAAA,QAAQ,OAAO,eAAe,GAAG;AAChC,SAAA,UAAU,QAAQ,MAAM,gBAAgB;AACnD,GALsB;AAOtB,MAAM,kBAAkB,wBAAC,QAAQ;AAC7B,SAAO,OAAO,OACR,KACA,QAAQ,GAAG,KAAM,cAAc,GAAG,KAAK,IAAI,aAAa,iBACpD,KAAK,UAAU,KAAK,MAAM,CAAC,IAC3B,OAAO,GAAG;AACxB,GANwB;AAOxB,SAASG,OAAK,OAAO,YAAY,IAAI;AACjC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAW,UAAU,IAAI,MAAM,OAAO,MAAM,YAAY,MAAO,EAAE;AACrG;AAFSA;AAGT,MAAM,QAAQ;AACd,SAAS,kBAAkB,QAAQ,QAAQ,GAAG,MAAM,OAAO,QAAQ;AACzD,QAAA,QAAQ,OAAO,MAAM,OAAO;AAClC,MAAI,QAAQ;AACZ,QAAM,MAAM,CAAA;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC1B,aAAA,MAAM,CAAC,EAAE,SAAS;AAC3B,QAAI,SAAS,OAAO;AACP,eAAA,IAAI,IAAI,OAAO,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK;AACpD,YAAA,IAAI,KAAK,KAAK,MAAM;AACpB;AACJ,cAAM,OAAO,IAAI;AACjB,YAAI,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE;AAChE,cAAA,aAAa,MAAM,CAAC,EAAE;AAC5B,YAAI,MAAM,GAAG;AAEH,gBAAA,MAAM,SAAS,QAAQ,cAAc;AACrC,gBAAA,SAAS,KAAK,IAAI,GAAG,MAAM,QAAQ,aAAa,MAAM,MAAM,KAAK;AACnE,cAAA,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,QAAA,WAEnD,IAAI,GAAG;AACZ,cAAI,MAAM,OAAO;AACP,kBAAA,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;AAC5D,gBAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC;AAAA,UAC1C;AACA,mBAAS,aAAa;AAAA,QAC1B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AACO,SAAA,IAAI,KAAK,IAAI;AACxB;AA/BS;AAgCT,SAAS,YAAYC,OAAM;AACvB,MAAI,UAAUA;AACd,SAAO,MAAM,EAAE;AACnB;AAHS;AAKT,SAAS,KAAK,KAAK,KAAK;AAChB,MAAA,OAAO,YAAY,aAAa;AACxB,YAAA,KAAK,eAAe,GAAG;AAE/B,QAAI,KAAK;AACG,cAAA,KAAK,IAAI,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;AARS;AAST,MAAM,YAAY,CAAA;AAClB,SAAS,SAAS,KAAK;AACf,MAAA,CAAC,UAAU,GAAG,GAAG;AACjB,cAAU,GAAG,IAAI;AACjB,SAAK,GAAG;AAAA,EACZ;AACJ;AALS;AAmBT,SAAS,gBAAgB;AACf,QAAA,6BAAa;AACnB,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,GAAG,OAAO,SAAS;AACT,YAAA,WAAW,OAAO,IAAI,KAAK;AACjC,YAAM,QAAQ,YAAY,SAAS,KAAK,OAAO;AAC/C,UAAI,CAAC,OAAO;AACR,eAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,IAAI,OAAO,SAAS;AACV,YAAA,WAAW,OAAO,IAAI,KAAK;AACjC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,KAAK,OAAO,SAAS;AACjB,OAAC,OAAO,IAAI,KAAK,KAAK,CAAA,GACjB,QACA,IAAI,CAAA,YAAW,QAAQ,OAAO,CAAC;AACpC,OAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAChB,MAAM,EACN,IAAI,CAAA,YAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC/C;AAAA,EAAA;AAEG,SAAA;AACX;AA3BS;AA6BT,MAAM,uBAAuB,wBAAC,QAAQ,CAACJ,WAAS,GAAG,KAAK,QAAQ,GAAG,GAAtC;AAE7B,SAAS,SAAS,KAAK,KAAK;AAExB,MAAI,qBAAqB,GAAG,KAAK,qBAAqB,GAAG,GAAG;AAClD,UAAA,IAAI,MAAM,eAAe;AAAA,EACnC;AACA,QAAM,QAAQ,CAAC,EAAE,KAAK,IAAK,CAAA;AAC3B,SAAO,MAAM,QAAQ;AACjB,UAAM,EAAE,KAAAK,MAAK,KAAAC,SAAQ,MAAM;AAE3B,WAAO,KAAKD,IAAG,EAAE,QAAQ,CAAO,QAAA;AAC5B,UAAI,QAAQ,aAAa;AACrB;AAAA,MACJ;AAGI,UAAAL,WAASK,KAAI,GAAG,CAAC,KAAK,CAACL,WAASM,KAAI,GAAG,CAAC,GAAG;AAC3CA,aAAI,GAAG,IAAI,MAAM,QAAQD,KAAI,GAAG,CAAC,IAAI,KAAK;MAC9C;AACI,UAAA,qBAAqBC,KAAI,GAAG,CAAC,KAAK,qBAAqBD,KAAI,GAAG,CAAC,GAAG;AAIlEC,aAAI,GAAG,IAAID,KAAI,GAAG;AAAA,MAAA,OAEjB;AAEK,cAAA,KAAK,EAAE,KAAKA,KAAI,GAAG,GAAG,KAAKC,KAAI,GAAG,EAAA,CAAG;AAAA,MAC/C;AAAA,IAAA,CACH;AAAA,EACL;AACJ;AA9BS;AC1NT;AAAA;AAAA;AAAA;AAAA;AAKA,MAAM,gBAAgB;AAAA,EAClB,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAG;AAAA,EACxC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAG;AAC1C;AACA,SAAS,eAAe,MAAM,QAAQ,QAAQ;AAC1C,SAAO,EAAE,MAAM,QAAQ;AAC3B;AAFS;AAGT,SAAS,eAAe,OAAO,KAAK,QAAQ;AACxC,QAAM,MAAM,EAAE,OAAO;AACrB,MAAI,UAAU,MAAM;AAChB,QAAI,SAAS;AAAA,EAChB;AACD,SAAO;AACX;AANS;AAYT,MAAM,UAAU;AAEhB,SAASP,SAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG;AACxC,WAAO,KAAK,CAAC;AAAA,EAChB;AACD,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAA;AAAA,EACV;AACD,SAAO,QAAQ,QAAQ,SAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EACpE,CAAK;AACL;AAVSA;AAWT,MAAM,SAAS,OAAO;AACtB,MAAM,WAAW,wBAAC,QAAQ,OAAO,QAAQ,UAAxB;AAEjB,MAAM,WAAW,wBAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,UAAxC;AACjB,SAAS,KAAK,OAAO,YAAY,IAAI;AACjC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAW,UAAU,IAAI,MAAM,OAAO,MAAM,YAAY,MAAO,EAAE;AACrG;AAFS;AAIT,MAAM,mBAAmB;AAAA,EACrB,mBAAmB;AAAA,EACnB,kBAAkB;AACtB;AAEA,MAAMQ,iBAAe;AAAA,EACjB,CAAC,iBAAiB,iBAAiB,GAAG;AAC1C;AACA,SAAS,kBAAkBH,OAAM,QAAQ,MAAM;AAC3C,QAAM,MAAML,SAAOQ,eAAaH,KAAI,KAAK,IAAI,GAAI,QAAQ,CAAE,CAAC;AAC5D,QAAM,UAAU,EAAE,SAAS,OAAO,GAAG,GAAG,MAAAA;AACxC,MAAI,KAAK;AACL,YAAQ,WAAW;AAAA,EACtB;AACD,SAAO;AACX;AAPS;AAST,MAAM,oBAAoB;AAAA;AAAA,EAEtB,gBAAgB;AAAA,EAChB,8BAA8B;AAAA,EAC9B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,iCAAiC;AAAA,EACjC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA;AAAA,EAEvB,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA;AAAA,EAE7B,6BAA6B;AAAA;AAAA,EAE7B,8BAA8B;AAAA;AAAA;AAAA;AAAA,EAI9B,kBAAkB;AACtB;AAEA,MAAMI,kBAAgB;AAAA;AAAA,EAElB,CAAC,kBAAkB,cAAc,GAAG;AAAA,EACpC,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,wCAAwC,GAAG;AAAA,EAC9D,CAAC,kBAAkB,uBAAuB,GAAG;AAAA,EAC7C,CAAC,kBAAkB,+BAA+B,GAAG;AAAA,EACrD,CAAC,kBAAkB,wBAAwB,GAAG;AAAA,EAC9C,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,iBAAiB,GAAG;AAAA,EACvC,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,qBAAqB,GAAG;AAAA;AAAA,EAE3C,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,gCAAgC,GAAG;AAAA,EACtD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA,EACjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,4BAA4B,GAAG;AACtD;AACA,SAAS,mBAAmBJ,OAAM,KAAK,UAAU,CAAA,GAAI;AACjD,QAAM,EAAE,QAAQ,UAAU,KAAI,IAAK;AACnC,QAAM,MAAML,UAAQ,YAAYS,iBAAeJ,KAAI,KAAK,IAAI,GAAI,QAAQ,CAAA,CAAG;AAE3E,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAOA;AACb,MAAI,KAAK;AACL,UAAM,WAAW;AAAA,EACpB;AACD,QAAM,SAAS;AACf,SAAO;AACX;AAXS;AAaT,SAAS,eAAe,OAAO;AAC3B,QAAM;AACV;AAFS;AAKT,MAAM,cAAc;AACpB,MAAM,gBAAgB,wBAAC,WAAW,YAAY,KAAK,MAAM,GAAnC;AAEtB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,MAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,SAAS,cAAc,KAAK;AACxB,QAAM,OAAO;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,QAAM,SAAS,wBAACK,WAAU,KAAKA,MAAK,MAAM,WAAW,KAAKA,SAAQ,CAAC,MAAM,SAA1D;AACf,QAAM,OAAO,wBAACA,WAAU,KAAKA,MAAK,MAAM,SAA3B;AACb,QAAM,OAAO,wBAACA,WAAU,KAAKA,MAAK,MAAM,SAA3B;AACb,QAAM,OAAO,wBAACA,WAAU,KAAKA,MAAK,MAAM,SAA3B;AACb,QAAM,YAAY,wBAACA,WAAU,OAAOA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,GAApE;AAClB,QAAM,QAAQ,6BAAM,QAAN;AACd,QAAM,OAAO,6BAAM,OAAN;AACb,QAAM,SAAS,6BAAM,SAAN;AACf,QAAM,aAAa,6BAAM,aAAN;AACnB,QAAM,SAAS,wBAAC,WAAW,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,GAAlF;AACf,QAAM,cAAc,6BAAM,OAAO,MAAM,GAAnB;AACpB,QAAM,cAAc,6BAAM,OAAO,SAAS,WAAW,GAAjC;AACpB,WAAS,OAAO;AACZ,kBAAc;AACd,QAAI,UAAU,MAAM,GAAG;AACnB;AACA,gBAAU;AAAA,IACb;AACD,QAAI,OAAO,MAAM,GAAG;AAChB;AAAA,IACH;AACD;AACA;AACA,WAAO,KAAK,MAAM;AAAA,EACrB;AAZQ;AAaT,WAAS,OAAO;AACZ,QAAI,OAAO,SAAS,WAAW,GAAG;AAC9B;AAAA,IACH;AACD;AACA,WAAO,KAAK,SAAS,WAAW;AAAA,EACnC;AANQ;AAOT,WAAS,QAAQ;AACb,aAAS;AACT,YAAQ;AACR,cAAU;AACV,kBAAc;AAAA,EACjB;AALQ;AAMT,WAAS,UAAU,SAAS,GAAG;AAC3B,kBAAc;AAAA,EACjB;AAFQ;AAGT,WAAS,aAAa;AAClB,UAAM,SAAS,SAAS;AAExB,WAAO,WAAW,QAAQ;AACtB;IACH;AACD,kBAAc;AAAA,EACjB;AAPQ;AAQT,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR;AACA;AArES;AAuET,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AACvB,SAAS,gBAAgB,QAAQ,UAAU,IAAI;AAC3C,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,QAAQ,cAAc,MAAM;AAClC,QAAM,gBAAgB,6BAAM,MAAM,SAAZ;AACtB,QAAM,kBAAkB,6BAAM,eAAe,MAAM,KAAI,GAAI,MAAM,OAAQ,GAAE,MAAM,MAAO,CAAA,GAAhE;AACxB,QAAM,WAAW;AACjB,QAAM,cAAc;AACpB,QAAM,WAAW;AAAA,IACb,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,EACd;AACI,QAAM,UAAU,6BAAM,UAAN;AAChB,QAAM,EAAE,QAAS,IAAG;AACpB,WAAS,UAAUL,OAAM,KAAK,WAAW,MAAM;AAC3C,UAAM,MAAM;AACZ,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,IAAI,UAAU,GAAG,IAAI;AAC3D,YAAM,MAAM,mBAAmBA,OAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MAChB,CAAa;AACD,cAAQ,GAAG;AAAA,IACd;AAAA,EACJ;AAZQ;AAaT,WAAS,SAASM,UAAS,MAAM,OAAO;AACpC,IAAAA,SAAQ,SAAS;AACjB,IAAAA,SAAQ,cAAc;AACtB,UAAM,QAAQ,EAAE;AAChB,QAAI,UAAU;AACV,YAAM,MAAM,eAAeA,SAAQ,UAAUA,SAAQ,MAAM;AAAA,IAC9D;AACD,QAAI,SAAS,MAAM;AACf,YAAM,QAAQ;AAAA,IACjB;AACD,WAAO;AAAA,EACV;AAXQ;AAYT,QAAM,cAAc,wBAACA,aAAY;AAAA,IAASA;AAAA,IAAS;AAAA;AAAA,EAAE,GAAjC;AACpB,WAAS,IAAI,MAAM,IAAI;AACnB,QAAI,KAAK,YAAa,MAAK,IAAI;AAC3B,WAAK,KAAI;AACT,aAAO;AAAA,IACV,OACI;AACD,gBAAU,kBAAkB,gBAAgB,gBAAe,GAAI,GAAG,EAAE;AACpE,aAAO;AAAA,IACV;AAAA,EACJ;AATQ;AAUT,WAAS,WAAW,MAAM;AACtB,QAAI,MAAM;AACV,WAAO,KAAK,kBAAkB,WAAW,KAAK,YAAa,MAAK,SAAS;AACrE,aAAO,KAAK;AACZ,WAAK,KAAI;AAAA,IACZ;AACD,WAAO;AAAA,EACV;AAPQ;AAQT,WAAS,WAAW,MAAM;AACtB,UAAM,MAAM,WAAW,IAAI;AAC3B,SAAK,WAAU;AACf,WAAO;AAAA,EACV;AAJQ;AAKT,WAAS,kBAAkB,IAAI;AAC3B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACV;AACD,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,EAEd;AATQ;AAUT,WAAS,cAAc,IAAI;AACvB,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACV;AACD,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC5B;AANQ;AAOT,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAa,CAAA;AAChD,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AATQ;AAUT,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,KAAK,KAAK,kBAAkB,MAAM,KAAK,KAAM,IAAG,KAAK;AAC3D,UAAM,MAAM,cAAc,EAAE;AAC5B,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AAVQ;AAWT,WAAS,eAAe,MAAMA,UAAS;AACnC,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAW,MAAO;AACnC,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AATQ;AAUT,WAAS,iBAAiB,MAAMA,UAAS;AACrC,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,GAAgC;AAChD,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAW,MAAO;AACnC,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AATQ;AAUT,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAa,CAAA;AAChD,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AATQ;AAUT,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,EAAE,gBAAgB,KAClB,gBAAgB,KAAqC;AACrD,aAAO;AAAA,IACV;AACD,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAW,MAAO;AACnC,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AAVQ;AAWT,WAAS,mBAAmB,MAAMA,UAAS;AACvC,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,gBAAgB,IAAqC;AACrD,aAAO;AAAA,IACV;AACD,UAAM,KAAK,6BAAM;AACb,YAAM,KAAK,KAAK;AAChB,UAAI,OAAO,KAAgC;AACvC,eAAO,kBAAkB,KAAK,KAAI,CAAE;AAAA,MACvC,WACQ,OAAO,OACZ,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,WACP,CAAC,IAAI;AACL,eAAO;AAAA,MACV,WACQ,OAAO,SAAS;AACrB,aAAK,KAAI;AACT,eAAO,GAAE;AAAA,MACZ,OACI;AAED,eAAO,YAAY,MAAM,KAAK;AAAA,MACjC;AAAA,IACb,GAtBmB;AAuBX,UAAM,MAAM;AACZ,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AA/BQ;AAgCT,WAAS,cAAc,MAAM;AACzB,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAW,MAAO;AACnC,SAAK,UAAS;AACd,WAAO;AAAA,EACV;AALQ;AAMT,WAAS,kBAAkB,MAAM;AAC7B,UAAM,SAAS,WAAW,IAAI;AAC9B,UAAM,MAAM,KAAK,YAAW,MAAO,OAC/B,KAAK,KAAM,MAAK;AACpB,SAAK,UAAS;AACd,WAAO;AAAA,MACH,UAAU;AAAA,MACV,UAAU,OAAO,SAAS;AAAA,IACtC;AAAA,EACK;AATQ;AAUT,WAAS,YAAY,MAAM,QAAQ,MAAM;AACrC,UAAM,KAAK,wBAAC,WAAW,OAAO,OAAO,IAAI,eAAe,UAAU;AAC9D,YAAM,KAAK,KAAK;AAChB,UAAI,OAAO,KAAgC;AACvC,eAAO,SAAS,MAA8B,QAAQ;AAAA,MACzD,WACQ,OAAO,OAAoC,CAAC,IAAI;AACrD,eAAO,SAAS,MAA8B,OAAO;AAAA,MACxD,WACQ,OAAO,KAA6B;AACzC,aAAK,KAAI;AACT,eAAO,GAAG,UAAU,KAA6B,IAAI;AAAA,MACxD,WACQ,OAAO,KAA2B;AACvC,eAAO,SAAS,OAA+B,eACzC,OACA,EAAE,SAAS,WAAW,SAAS;AAAA,MACxC,WACQ,OAAO,SAAS;AACrB,aAAK,KAAI;AACT,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACxC,WACQ,OAAO,SAAS;AACrB,aAAK,KAAI;AACT,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACxC,OACI;AACD,eAAO;AAAA,MACV;AAAA,IACb,GA5BmB;AA6BX,UAAM,MAAM;AACZ,aAAS,KAAK;AACd,WAAO;AAAA,EACV;AAjCQ;AAkCT,WAAS,SAAS,MAAM,IAAI;AACxB,UAAM,KAAK,KAAK;AAChB,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACV;AACD,QAAI,GAAG,EAAE,GAAG;AACR,WAAK,KAAI;AACT,aAAO;AAAA,IACV;AACD,WAAO;AAAA,EACV;AAVQ;AAWT,WAAS,aAAa,IAAI;AACtB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,EAEd;AARQ;AAST,WAAS,mBAAmB,MAAM;AAC9B,WAAO,SAAS,MAAM,YAAY;AAAA,EACrC;AAFQ;AAGT,WAAS,kBAAkB,IAAI;AAC3B,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EAEd;AATQ;AAUT,WAAS,wBAAwB,MAAM;AACnC,WAAO,SAAS,MAAM,iBAAiB;AAAA,EAC1C;AAFQ;AAGT,WAAS,QAAQ,IAAI;AACjB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC5B;AAHQ;AAIT,WAAS,UAAU,MAAM;AACrB,WAAO,SAAS,MAAM,OAAO;AAAA,EAChC;AAFQ;AAGT,WAAS,WAAW,IAAI;AACpB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,EAC1B;AALQ;AAMT,WAAS,aAAa,MAAM;AACxB,WAAO,SAAS,MAAM,UAAU;AAAA,EACnC;AAFQ;AAGT,WAAS,UAAU,MAAM;AACrB,QAAI,KAAK;AACT,QAAI,MAAM;AACV,WAAQ,KAAK,UAAU,IAAI,GAAI;AAC3B,aAAO;AAAA,IACV;AACD,WAAO;AAAA,EACV;AAPQ;AAQT,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,KAAK,KAAK;AAChB,QAAI,OAAO,KAA6B;AACpC,gBAAU,kBAAkB,gBAAgB,gBAAe,GAAI,GAAG,EAAE;AAAA,IACvE;AACD,SAAK,KAAI;AACT,WAAO;AAAA,EACV;AARQ;AAST,WAAS,SAAS,MAAM;AACpB,QAAI,MAAM;AAEV,WAAO,MAAM;AACT,YAAM,KAAK,KAAK;AAChB,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL;AAAA,MACH,WACQ,OAAO,KAA6B;AACzC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAI;AAAA,QACZ,OACI;AACD;AAAA,QACH;AAAA,MACJ,WACQ,OAAO,WAAW,OAAO,SAAS;AACvC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAI;AAAA,QACZ,WACQ,cAAc,IAAI,GAAG;AAC1B;AAAA,QACH,OACI;AACD,iBAAO;AACP,eAAK,KAAI;AAAA,QACZ;AAAA,MACJ,OACI;AACD,eAAO;AACP,aAAK,KAAI;AAAA,MACZ;AAAA,IACJ;AACD,WAAO;AAAA,EACV;AAxCQ;AAyCT,WAAS,oBAAoB,MAAM;AAC/B,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,wBAAwB,IAAI,GAAI;AACzC,cAAQ;AAAA,IACX;AACD,QAAI,KAAK,YAAa,MAAK,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAAA,IAC/E;AACD,WAAO;AAAA,EACV;AAXQ;AAYT,WAAS,mBAAmB,MAAM;AAC9B,eAAW,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI,KAAK,YAAa,MAAK,KAAK;AAC5B,WAAK,KAAI;AACT,eAAS,IAAI,UAAU,IAAI,CAAC;AAAA,IAC/B,OACI;AACD,eAAS,UAAU,IAAI;AAAA,IAC1B;AACD,QAAI,KAAK,YAAa,MAAK,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAAA,IAC/E;AACD,WAAO;AAAA,EACV;AAdQ;AAeT,WAASC,WAAU,IAAI;AACnB,WAAO,OAAO,qBAAqB,OAAO;AAAA,EAC7C;AAFQ,SAAAA,YAAA;AAGT,WAAS,YAAY,MAAM;AACvB,eAAW,IAAI;AAEf,QAAI,MAAM,GAAI;AACd,QAAI,KAAK;AACT,QAAI,UAAU;AACd,WAAQ,KAAK,SAAS,MAAMA,UAAS,GAAI;AACrC,UAAI,OAAO,MAAM;AACb,mBAAW,mBAAmB,IAAI;AAAA,MACrC,OACI;AACD,mBAAW;AAAA,MACd;AAAA,IACJ;AACD,UAAM,UAAU,KAAK;AACrB,QAAI,YAAY,WAAW,YAAY,KAAK;AACxC,gBAAU,kBAAkB,0CAA0C,gBAAiB,GAAE,CAAC;AAE1F,UAAI,YAAY,SAAS;AACrB,aAAK,KAAI;AAET,YAAI,MAAM,GAAI;AAAA,MACjB;AACD,aAAO;AAAA,IACV;AAED,QAAI,MAAM,GAAI;AACd,WAAO;AAAA,EACV;AA5BQ;AA6BT,WAAS,mBAAmB,MAAM;AAC9B,UAAM,KAAK,KAAK;AAChB,YAAQ,IAAE;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AACD,aAAK,KAAI;AACT,eAAO,KAAK,EAAE;AAAA,MAClB,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD;AACI,kBAAU,kBAAkB,yBAAyB,gBAAe,GAAI,GAAG,EAAE;AAC7E,eAAO;AAAA,IACd;AAAA,EACJ;AAfQ;AAgBT,WAAS,0BAA0B,MAAM,SAAS,QAAQ;AACtD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAM,KAAK,aAAa,IAAI;AAC5B,UAAI,CAAC,IAAI;AACL,kBAAU,kBAAkB,iCAAiC,gBAAiB,GAAE,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK,YAAa,CAAA,EAAE;AACjI;AAAA,MACH;AACD,kBAAY;AAAA,IACf;AACD,WAAO,KAAK,OAAO,GAAG,QAAQ;AAAA,EACjC;AAZQ;AAaT,WAAS,oBAAoB,IAAI;AAC7B,WAAQ,OAAO,OACX,OAAO,OACP,OAAO,WACP,OAAO;AAAA,EACd;AALQ;AAMT,WAAS,sBAAsB,MAAM;AACjC,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,cAAc;AAClB,WAAQ,KAAK,SAAS,MAAM,mBAAmB,GAAI;AAC/C,qBAAe;AAAA,IAClB;AACD,WAAO;AAAA,EACV;AARQ;AAST,WAAS,mBAAmB,MAAM;AAC9B,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,cAAQ;AAAA,IACX;AACD,WAAO;AAAA,EACV;AAPQ;AAQT,WAAS,gBAAgB,MAAM;AAC3B,UAAM,KAAK,wBAAC,QAAQ;AAChB,YAAM,KAAK,KAAK;AAChB,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL,eAAO;AAAA,MACV,WACQ,OAAO,SAAS;AACrB,eAAO;AAAA,MACV,WACQ,OAAO,WAAW,OAAO,KAAK;AACnC,eAAO;AACP,aAAK,KAAI;AACT,eAAO,GAAG,GAAG;AAAA,MAChB,OACI;AACD,eAAO;AACP,aAAK,KAAI;AACT,eAAO,GAAG,GAAG;AAAA,MAChB;AAAA,IACb,GAxBmB;AAyBX,WAAO,GAAG,EAAE;AAAA,EACf;AA3BQ;AA4BT,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,SAAS;AAAA,MAAI;AAAA,MAAM;AAAA;AAAA,IAAG;AAC5B,eAAW,IAAI;AACf,WAAO;AAAA,EACV;AALQ;AAOT,WAAS,uBAAuB,MAAMD,UAAS;AAC3C,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK;AAChB,YAAQ,IAAE;AAAA,MACN,KAAK;AACD,YAAIA,SAAQ,aAAa,GAAG;AACxB,oBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAAA,QAC/E;AACD,aAAK,KAAI;AACT,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAAG;AAC3D,mBAAW,IAAI;AACf,QAAAA,SAAQ;AACR,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,KACpBA,SAAQ,gBAAgB,GAA8B;AACtD,oBAAU,kBAAkB,mBAAmB,gBAAiB,GAAE,CAAC;AAAA,QACtE;AACD,aAAK,KAAI;AACT,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAAG;AAC5D,QAAAA,SAAQ;AACR,QAAAA,SAAQ,YAAY,KAAK,WAAW,IAAI;AACxC,YAAIA,SAAQ,YAAYA,SAAQ,cAAc,GAAG;AAC7C,UAAAA,SAAQ,WAAW;AAAA,QACtB;AACD,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,GAAG;AACvB,oBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAAA,QAC/E;AACD,gBAAQ,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAC/D,QAAAA,SAAQ,YAAY;AACpB,eAAO;AAAA,MACX,SAAS;AACL,YAAI,uBAAuB;AAC3B,YAAI,sBAAsB;AAC1B,YAAI,eAAe;AACnB,YAAI,cAAc,IAAI,GAAG;AACrB,cAAIA,SAAQ,YAAY,GAAG;AACvB,sBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAAA,UAC/E;AACD,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACV;AACD,YAAIA,SAAQ,YAAY,MACnBA,SAAQ,gBAAgB,KACrBA,SAAQ,gBAAgB,KACxBA,SAAQ,gBAAgB,IAA6B;AACzD,oBAAU,kBAAkB,4BAA4B,gBAAiB,GAAE,CAAC;AAC5E,UAAAA,SAAQ,YAAY;AACpB,iBAAO,UAAU,MAAMA,QAAO;AAAA,QACjC;AACD,YAAK,uBAAuB,uBAAuB,MAAMA,QAAO,GAAI;AAChE,kBAAQ,SAASA,UAAS,GAA0B,oBAAoB,IAAI,CAAC;AAC7E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACV;AACD,YAAK,sBAAsB,sBAAsB,MAAMA,QAAO,GAAI;AAC9D,kBAAQ,SAASA,UAAS,GAAyB,mBAAmB,IAAI,CAAC;AAC3E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACV;AACD,YAAK,eAAe,eAAe,MAAMA,QAAO,GAAI;AAChD,kBAAQ,SAASA,UAAS,GAA4B,YAAY,IAAI,CAAC;AACvE,qBAAW,IAAI;AACf,iBAAO;AAAA,QACV;AACD,YAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,cAAc;AAEhE,kBAAQ,SAASA,UAAS,IAAkC,sBAAsB,IAAI,CAAC;AACvF,oBAAU,kBAAkB,8BAA8B,gBAAiB,GAAE,GAAG,MAAM,KAAK;AAC3F,qBAAW,IAAI;AACf,iBAAO;AAAA,QACV;AACD;AAAA,MACH;AAAA,IACJ;AACD,WAAO;AAAA,EACV;AAjFQ;AAmFT,WAAS,kBAAkB,MAAMA,UAAS;AACtC,UAAM,EAAE,YAAa,IAAGA;AACxB,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK;AAChB,SAAK,gBAAgB,KACjB,gBAAgB,KAChB,gBAAgB,MAChB,gBAAgB,QACf,OAAO,WAAW,OAAO,UAAU;AACpC,gBAAU,kBAAkB,uBAAuB,gBAAiB,GAAE,CAAC;AAAA,IAC1E;AACD,YAAQ,IAAE;AAAA,MACN,KAAK;AACD,aAAK,KAAI;AACT,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAAgC;AAAA;AAAA,QAAG;AAC7D,QAAAA,SAAQ,WAAW;AACnB,eAAO;AAAA,MACX,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAI;AACT,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAAG;AAAA,MAC9D,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAI;AACT,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAAqC;AAAA;AAAA,QAAG;AAAA,MACrE;AACI,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACV;AACD,YAAI,iBAAiB,MAAMA,QAAO,KAC9B,uBAAuB,MAAMA,QAAO,GAAG;AACvC,qBAAW,IAAI;AACf,iBAAO,kBAAkB,MAAMA,QAAO;AAAA,QACzC;AACD,YAAI,sBAAsB,MAAMA,QAAO,GAAG;AACtC,qBAAW,IAAI;AACf,iBAAO,SAASA,UAAS,IAAoC,mBAAmB,IAAI,CAAC;AAAA,QACxF;AACD,YAAI,mBAAmB,MAAMA,QAAO,GAAG;AACnC,qBAAW,IAAI;AACf,cAAI,OAAO,KAAgC;AAEvC,mBAAO,uBAAuB,MAAMA,QAAO,KAAK;AAAA,UACnD,OACI;AACD,mBAAO,SAASA,UAAS,IAA+B,gBAAgB,IAAI,CAAC;AAAA,UAChF;AAAA,QACJ;AACD,YAAI,gBAAgB,GAAgC;AAChD,oBAAU,kBAAkB,uBAAuB,gBAAiB,GAAE,CAAC;AAAA,QAC1E;AACD,QAAAA,SAAQ,YAAY;AACpB,QAAAA,SAAQ,WAAW;AACnB,eAAO,UAAU,MAAMA,QAAO;AAAA,IACrC;AAAA,EACJ;AA3DQ;AA6DT,WAAS,UAAU,MAAMA,UAAS;AAC9B,QAAI,QAAQ;AAAA,MAAE,MAAM;AAAA;AAAA;AACpB,QAAIA,SAAQ,YAAY,GAAG;AACvB,aAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IACtE;AACD,QAAIA,SAAQ,UAAU;AAClB,aAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IACjE;AACD,UAAM,KAAK,KAAK;AAChB,YAAQ,IAAE;AAAA,MACN,KAAK;AACD,eAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MACvE,KAAK;AACD,kBAAU,kBAAkB,0BAA0B,gBAAiB,GAAE,CAAC;AAC1E,aAAK,KAAI;AACT,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAAG;AAAA,MAC/D,KAAK;AACD,eAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MAClE,SAAS;AACL,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACV;AACD,cAAM,EAAE,UAAU,SAAU,IAAG,kBAAkB,IAAI;AACrD,YAAI,UAAU;AACV,iBAAO,WACD,SAASA,UAAS,GAAyB,SAAS,IAAI,CAAC,IACzD,SAASA,UAAS,GAA2B,WAAW,IAAI,CAAC;AAAA,QACtE;AACD,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO,SAASA,UAAS,GAAyB,SAAS,IAAI,CAAC;AAAA,QACnE;AACD;AAAA,MACH;AAAA,IACJ;AACD,WAAO;AAAA,EACV;AAvCQ;AAwCT,WAAS,YAAY;AACjB,UAAM,EAAE,aAAa,QAAQ,UAAU,OAAM,IAAK;AAClD,aAAS,WAAW;AACpB,aAAS,aAAa;AACtB,aAAS,eAAe;AACxB,aAAS,aAAa;AACtB,aAAS,SAAS;AAClB,aAAS,WAAW;AACpB,QAAI,MAAM,YAAa,MAAK,KAAK;AAC7B,aAAO;AAAA,QAAS;AAAA,QAAU;AAAA;AAAA;IAC7B;AACD,WAAO,UAAU,OAAO,QAAQ;AAAA,EACnC;AAZQ;AAaT,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR;AACA;AAnrBS;AAqrBT,MAAM,iBAAiB;AAEvB,MAAM,gBAAgB;AACtB,SAAS,mBAAmB,OAAO,YAAY,YAAY;AACvD,UAAQ,OAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IAEX,KAAK;AAED,aAAO;AAAA,IACX,SAAS;AACL,YAAM,YAAY,SAAS,cAAc,YAAY,EAAE;AACvD,UAAI,aAAa,SAAU,aAAa,OAAQ;AAC5C,eAAO,OAAO,cAAc,SAAS;AAAA,MACxC;AAGD,aAAO;AAAA,IACV;AAAA,EACJ;AACL;AAlBS;AAmBT,SAAS,aAAa,UAAU,IAAI;AAChC,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,EAAE,SAAS,OAAQ,IAAG;AAC5B,WAAS,UAAU,UAAUN,OAAM,OAAO,WAAW,MAAM;AACvD,UAAM,MAAM,SAAS;AACrB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,OAAO,GAAG,IAAI;AACpD,YAAM,MAAM,mBAAmBA,OAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MAChB,CAAa;AACD,cAAQ,GAAG;AAAA,IACd;AAAA,EACJ;AAZQ;AAaT,WAAS,SAAS,UAAUA,OAAM,OAAO,WAAW,MAAM;AACtD,UAAM,MAAM,SAAS;AACrB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,QAAQ;AACR,YAAM,MAAM,WAAW,eAAe,OAAO,GAAG,IAAI;AACpD,aAAO,kBAAkBA,OAAM,KAAK,IAAI,CAAC;AAAA,IAC5C;AAAA,EACJ;AARQ;AAST,WAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,UAAM,OAAO,EAAE;AACf,QAAI,UAAU;AACV,WAAK,QAAQ;AACb,WAAK,MAAM;AACX,WAAK,MAAM,EAAE,OAAO,KAAK,KAAK;IACjC;AACD,WAAO;AAAA,EACV;AARQ;AAST,WAAS,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtC,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IACf;AACD,QAAI,UAAU;AACV,WAAK,MAAM;AACX,UAAI,KAAK,KAAK;AACV,aAAK,IAAI,MAAM;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AAVQ;AAWT,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU;AAC1B,UAAM,OAAO,UAAU,GAAwB,QAAQ,QAAQ,QAAQ,QAAQ;AAC/E,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AANQ;AAOT,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU;AAC1B,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAG,IAAK;AAClD,UAAM,OAAO,UAAU,GAAwB,QAAQ,GAAG;AAC1D,SAAK,QAAQ,SAAS,OAAO,EAAE;AAC/B,cAAU,UAAS;AACnB,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AARQ;AAST,WAAS,WAAW,WAAW,KAAK,QAAQ;AACxC,UAAM,UAAU,UAAU;AAC1B,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAG,IAAK;AAClD,UAAM,OAAO,UAAU,GAAyB,QAAQ,GAAG;AAC3D,SAAK,MAAM;AACX,QAAI,WAAW,MAAM;AACjB,WAAK,SAAS;AAAA,IACjB;AACD,cAAU,UAAS;AACnB,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AAXQ;AAYT,WAAS,aAAa,WAAW,OAAO;AACpC,UAAM,UAAU,UAAU;AAC1B,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAG,IAAK;AAClD,UAAM,OAAO,UAAU,GAA2B,QAAQ,GAAG;AAC7D,SAAK,QAAQ,MAAM,QAAQ,eAAe,kBAAkB;AAC5D,cAAU,UAAS;AACnB,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AARQ;AAST,WAAS,oBAAoB,WAAW;AACpC,UAAM,QAAQ,UAAU;AACxB,UAAM,UAAU,UAAU;AAC1B,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAG,IAAK;AAClD,UAAM,OAAO,UAAU,GAAkC,QAAQ,GAAG;AACpE,QAAI,MAAM,SAAS,IAAoC;AAEnD,gBAAU,WAAW,kBAAkB,kCAAkC,QAAQ,cAAc,CAAC;AAChG,WAAK,QAAQ;AACb,cAAQ,MAAM,QAAQ,GAAG;AACzB,aAAO;AAAA,QACH,kBAAkB;AAAA,QAClB;AAAA,MAChB;AAAA,IACS;AAED,QAAI,MAAM,SAAS,MAAM;AACrB,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACtH;AACD,SAAK,QAAQ,MAAM,SAAS;AAC5B,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,MACH;AAAA,IACZ;AAAA,EACK;AAxBQ;AAyBT,WAAS,eAAe,WAAW,OAAO;AACtC,UAAM,UAAU,UAAU;AAC1B,UAAM,OAAO,UAAU,GAA6B,QAAQ,QAAQ,QAAQ,QAAQ;AACpF,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AANQ;AAOT,WAAS,YAAY,WAAW;AAC5B,UAAM,UAAU,UAAU;AAC1B,UAAM,aAAa,UAAU,GAA0B,QAAQ,QAAQ,QAAQ,QAAQ;AACvF,QAAI,QAAQ,UAAU;AACtB,QAAI,MAAM,SAAS,GAA8B;AAC7C,YAAM,SAAS,oBAAoB,SAAS;AAC5C,iBAAW,WAAW,OAAO;AAC7B,cAAQ,OAAO,oBAAoB,UAAU,UAAS;AAAA,IACzD;AAED,QAAI,MAAM,SAAS,IAAqC;AACpD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACtH;AACD,YAAQ,UAAU;AAElB,QAAI,MAAM,SAAS,GAA8B;AAC7C,cAAQ,UAAU;IACrB;AACD,YAAQ,MAAM,MAAI;AAAA,MACd,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACtH;AACD,mBAAW,MAAM,eAAe,WAAW,MAAM,SAAS,EAAE;AAC5D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACtH;AACD,mBAAW,MAAM,WAAW,WAAW,MAAM,SAAS,EAAE;AACxD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACtH;AACD,mBAAW,MAAM,UAAU,WAAW,MAAM,SAAS,EAAE;AACvD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACtH;AACD,mBAAW,MAAM,aAAa,WAAW,MAAM,SAAS,EAAE;AAC1D;AAAA,MACJ,SAAS;AAEL,kBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,CAAC;AAC3F,cAAM,cAAc,UAAU;AAC9B,cAAM,qBAAqB,UAAU,GAA6B,YAAY,QAAQ,YAAY,QAAQ;AAC1G,2BAAmB,QAAQ;AAC3B,gBAAQ,oBAAoB,YAAY,QAAQ,YAAY,QAAQ;AACpE,mBAAW,MAAM;AACjB,gBAAQ,YAAY,YAAY,QAAQ,YAAY,QAAQ;AAC5D,eAAO;AAAA,UACH,kBAAkB;AAAA,UAClB,MAAM;AAAA,QAC1B;AAAA,MACa;AAAA,IACJ;AACD,YAAQ,YAAY,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AAC1E,WAAO;AAAA,MACH,MAAM;AAAA,IAClB;AAAA,EACK;AA9DQ;AA+DT,WAAS,aAAa,WAAW;AAC7B,UAAM,UAAU,UAAU;AAC1B,UAAM,cAAc,QAAQ,gBAAgB,IACtC,UAAU,cAAe,IACzB,QAAQ;AACd,UAAM,WAAW,QAAQ,gBAAgB,IACnC,QAAQ,SACR,QAAQ;AACd,UAAM,OAAO,UAAU,GAA2B,aAAa,QAAQ;AACvE,SAAK,QAAQ;AACb,QAAI,YAAY;AAChB,QAAI,SAAS;AACb,OAAG;AACC,YAAM,QAAQ,aAAa,UAAU,UAAS;AAC9C,kBAAY;AACZ,cAAQ,MAAM,MAAI;AAAA,QACd,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACtH;AACD,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACtH;AACD,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,mBAAS;AACT;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACtH;AACD,eAAK,MAAM,KAAK,WAAW,WAAW,MAAM,SAAS,IAAI,CAAC,CAAC,MAAM,CAAC;AAClE,cAAI,QAAQ;AACR,qBAAS,WAAW,iBAAiB,mBAAmB,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AACvG,qBAAS;AAAA,UACZ;AACD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACtH;AACD,eAAK,MAAM,KAAK,aAAa,WAAW,MAAM,SAAS,EAAE,CAAC;AAC1D;AAAA,QACJ,KAAK,GAAgC;AACjC,gBAAM,SAAS,YAAY,SAAS;AACpC,eAAK,MAAM,KAAK,OAAO,IAAI;AAC3B,sBAAY,OAAO,oBAAoB;AACvC;AAAA,QACH;AAAA,MACJ;AAAA,IACb,SAAiB,QAAQ,gBAAgB,MAC7B,QAAQ,gBAAgB;AAE5B,UAAM,YAAY,QAAQ,gBAAgB,IACpC,QAAQ,aACR,UAAU,cAAa;AAC7B,UAAM,SAAS,QAAQ,gBAAgB,IACjC,QAAQ,aACR,UAAU,gBAAe;AAC/B,YAAQ,MAAM,WAAW,MAAM;AAC/B,WAAO;AAAA,EACV;AAjEQ;AAkET,WAAS,YAAY,WAAW,QAAQ,KAAK,SAAS;AAClD,UAAM,UAAU,UAAU;AAC1B,QAAI,kBAAkB,QAAQ,MAAM,WAAW;AAC/C,UAAM,OAAO,UAAU,GAA0B,QAAQ,GAAG;AAC5D,SAAK,QAAQ;AACb,SAAK,MAAM,KAAK,OAAO;AACvB,OAAG;AACC,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,CAAC,iBAAiB;AAClB,0BAAkB,IAAI,MAAM,WAAW;AAAA,MAC1C;AACD,WAAK,MAAM,KAAK,GAAG;AAAA,IAC/B,SAAiB,QAAQ,gBAAgB;AACjC,QAAI,iBAAiB;AACjB,gBAAU,WAAW,kBAAkB,8BAA8B,KAAK,CAAC;AAAA,IAC9E;AACD,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AAlBQ;AAmBT,WAAS,cAAc,WAAW;AAC9B,UAAM,UAAU,UAAU;AAC1B,UAAM,EAAE,QAAQ,SAAU,IAAG;AAC7B,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,aAAO;AAAA,IACV,OACI;AACD,aAAO,YAAY,WAAW,QAAQ,UAAU,OAAO;AAAA,IAC1D;AAAA,EACJ;AAVQ;AAWT,WAASQ,OAAM,QAAQ;AACnB,UAAM,YAAY,gBAAgB,QAAQ,OAAO,CAAE,GAAE,OAAO,CAAC;AAC7D,UAAM,UAAU,UAAU;AAC1B,UAAM,OAAO,UAAU,GAA4B,QAAQ,QAAQ,QAAQ,QAAQ;AACnF,QAAI,YAAY,KAAK,KAAK;AACtB,WAAK,IAAI,SAAS;AAAA,IACrB;AACD,SAAK,OAAO,cAAc,SAAS;AACnC,QAAI,QAAQ,YAAY;AACpB,WAAK,WAAW,QAAQ,WAAW,MAAM;AAAA,IAC5C;AAED,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,IAC5H;AACD,YAAQ,MAAM,UAAU,cAAe,GAAE,UAAU,gBAAe,CAAE;AACpE,WAAO;AAAA,EACV;AAjBQ,SAAAA,QAAA;AAkBT,SAAO,EAAE,OAAAA,OAAK;AAClB;AApSS;AAqST,SAAS,gBAAgB,OAAO;AAC5B,MAAI,MAAM,SAAS,IAAyB;AACxC,WAAO;AAAA,EACV;AACD,QAAM,QAAQ,MAAM,SAAS,IAAI,QAAQ,WAAW,KAAK;AACzD,SAAO,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM;AACvD;AANS;AAQT,SAAS,kBAAkB,KAAK,UAAU,CAAE,GAC1C;AACE,QAAM,WAAW;AAAA,IACb;AAAA,IACA,SAAS,oBAAI,IAAK;AAAA,EAC1B;AACI,QAAM,UAAU,6BAAM,UAAN;AAChB,QAAM,SAAS,wBAAC,SAAS;AACrB,aAAS,QAAQ,IAAI,IAAI;AACzB,WAAO;AAAA,EACf,GAHmB;AAIf,SAAO,EAAE,SAAS;AACtB;AAZS;AAaT,SAAS,cAAc,OAAO,aAAa;AACvC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,iBAAa,MAAM,CAAC,GAAG,WAAW;AAAA,EACrC;AACL;AAJS;AAKT,SAAS,aAAa,MAAM,aAAa;AAErC,UAAQ,KAAK,MAAI;AAAA,IACb,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB;AAAA,IACJ,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC;AAAA,IACJ,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,mBAAa,OAAO,KAAK,WAAW;AACpC,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB;AAAA,IACH;AAAA,IACD,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB;AAAA,IACJ,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB,kBAAY;AAAA,QAAO;AAAA;AAAA;AACnB;AAAA,EACP;AAEL;AA3BS;AA6BT,SAAS,UAAU,KAAK,UAAU,CAAE,GAClC;AACE,QAAM,cAAc,kBAAkB,GAAG;AACzC,cAAY;AAAA,IAAO;AAAA;AAAA;AAEnB,MAAI,QAAQ,aAAa,IAAI,MAAM,WAAW;AAE9C,QAAM,UAAU,YAAY;AAC5B,MAAI,UAAU,MAAM,KAAK,QAAQ,OAAO;AAC5C;AATS;AAWT,SAAS,SAAS,KAAK;AACnB,QAAM,OAAO,IAAI;AACjB,MAAI,KAAK,SAAS,GAA2B;AACzC,wBAAoB,IAAI;AAAA,EAC3B,OACI;AACD,SAAK,MAAM,QAAQ,OAAK,oBAAoB,CAAC,CAAC;AAAA,EACjD;AACD,SAAO;AACX;AATS;AAUT,SAAS,oBAAoB,SAAS;AAClC,MAAI,QAAQ,MAAM,WAAW,GAAG;AAC5B,UAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,QAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,cAAQ,SAAS,KAAK;AACtB,aAAO,KAAK;AAAA,IACf;AAAA,EACJ,OACI;AACD,UAAM,SAAS,CAAA;AACf,aAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,UAAI,EAAE,KAAK,SAAS,KAA0B,KAAK,SAAS,IAA4B;AACpF;AAAA,MACH;AACD,UAAI,KAAK,SAAS,MAAM;AACpB;AAAA,MACH;AACD,aAAO,KAAK,KAAK,KAAK;AAAA,IACzB;AACD,QAAI,OAAO,WAAW,QAAQ,MAAM,QAAQ;AACxC,cAAQ,SAAS,KAAK,MAAM;AAC5B,eAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,cAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,YAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,iBAAO,KAAK;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACL;AA9BS;AAgCT,MAAM,iBAAiB;AAEvB,SAAS,OAAO,MAAM;AAClB,OAAK,IAAI,KAAK;AACd,UAAQ,KAAK,MAAI;AAAA,IACb,KAAK,GAA4B;AAC7B,YAAM,WAAW;AACjB,aAAO,SAAS,IAAI;AACpB,eAAS,IAAI,SAAS;AACtB,aAAO,SAAS;AAChB;AAAA,IACH;AAAA,IACD,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,YAAM,QAAQ,OAAO;AACrB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MAClB;AACD,aAAO,IAAI;AACX,aAAO,OAAO;AACd;AAAA,IACH;AAAA,IACD,KAAK,GAA2B;AAC5B,YAAM,UAAU;AAChB,YAAM,QAAQ,QAAQ;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MAClB;AACD,cAAQ,IAAI;AACZ,aAAO,QAAQ;AACf,UAAI,QAAQ,QAAQ;AAChB,gBAAQ,IAAI,QAAQ;AACpB,eAAO,QAAQ;AAAA,MAClB;AACD;AAAA,IACH;AAAA,IACD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,GAA6B;AAC9B,YAAM,YAAY;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,IAAI,UAAU;AACxB,eAAO,UAAU;AAAA,MACpB;AACD;AAAA,IACH;AAAA,IACD,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,aAAO,OAAO,GAAG;AACjB,aAAO,IAAI,OAAO;AAClB,aAAO,OAAO;AACd,UAAI,OAAO,UAAU;AACjB,eAAO,OAAO,QAAQ;AACtB,eAAO,IAAI,OAAO;AAClB,eAAO,OAAO;AAAA,MACjB;AACD;AAAA,IACH;AAAA,IACD,KAAK,GAAwB;AACzB,YAAM,OAAO;AACb,WAAK,IAAI,KAAK;AACd,aAAO,KAAK;AACZ;AAAA,IACH;AAAA,IACD,KAAK,GAAyB;AAC1B,YAAM,QAAQ;AACd,YAAM,IAAI,MAAM;AAChB,aAAO,MAAM;AACb;AAAA,IACH;AAAA,IACD,SACI;AACI,YAAM,mBAAmB,kBAAkB,8BAA8B,MAAM;AAAA,QAC3E,QAAQ;AAAA,QACR,MAAM,CAAC,KAAK,IAAI;AAAA,MACpC,CAAiB;AAAA,IACJ;AAAA,EACR;AACD,SAAO,KAAK;AAChB;AA9ES;AAmFT,MAAM,eAAe;AACrB,SAAS,oBAAoB,KAAK,SAAS;AACvC,QAAM,EAAE,WAAW,UAAU,eAAe,YAAY,YAAa,IAAG;AACxE,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,EACrB;AACI,MAAI,YAAY,IAAI,KAAK;AACrB,aAAS,SAAS,IAAI,IAAI;AAAA,EAC7B;AACD,QAAM,UAAU,6BAAM,UAAN;AAChB,WAAS,KAAKR,OAAM,MAAM;AACtB,aAAS,QAAQA;AAAA,EACpB;AAFQ;AAGT,WAAS,SAAS,GAAG,gBAAgB,MAAM;AACvC,UAAM,iBAAiB,gBAAgB,gBAAgB;AACvD,SAAK,cAAc,iBAAiB,KAAK,OAAO,CAAC,IAAI,cAAc;AAAA,EACtE;AAHQ;AAIT,WAAS,OAAO,cAAc,MAAM;AAChC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EAChC;AAHQ;AAIT,WAAS,SAAS,cAAc,MAAM;AAClC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EAChC;AAHQ;AAIT,WAAS,UAAU;AACf,aAAS,SAAS,WAAW;AAAA,EAChC;AAFQ;AAGT,QAAM,SAAS,wBAAC,QAAQ,IAAI,GAAG,IAAhB;AACf,QAAM,aAAa,6BAAM,SAAS,YAAf;AACnB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR;AACA;AA/CS;AAgDT,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,OAAQ,IAAG;AACnB,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAAoC,CAAA,GAAG;AAChE,eAAa,WAAW,KAAK,GAAG;AAChC,MAAI,KAAK,UAAU;AACf,cAAU,KAAK,IAAI;AACnB,iBAAa,WAAW,KAAK,QAAQ;AACrC,cAAU,KAAK,SAAS;AAAA,EAC3B,OACI;AACD,cAAU,KAAK,oBAAoB;AAAA,EACtC;AACD,YAAU,KAAK,GAAG;AACtB;AAbS;AAcT,SAAS,oBAAoB,WAAW,MAAM;AAC1C,QAAM,EAAE,QAAQ,WAAY,IAAG;AAC/B,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAA0C,CAAA,IAAI;AACvE,YAAU,OAAO,WAAU,CAAE;AAC7B,QAAM,SAAS,KAAK,MAAM;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,iBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,QAAI,MAAM,SAAS,GAAG;AAClB;AAAA,IACH;AACD,cAAU,KAAK,IAAI;AAAA,EACtB;AACD,YAAU,SAAS,WAAU,CAAE;AAC/B,YAAU,KAAK,IAAI;AACvB;AAdS;AAeT,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,QAAQ,WAAY,IAAG;AAC/B,MAAI,KAAK,MAAM,SAAS,GAAG;AACvB,cAAU,KAAK,GAAG;AAAA,MAAO;AAAA;AAAA,IAAoC,CAAA,IAAI;AACjE,cAAU,OAAO,WAAU,CAAE;AAC7B,UAAM,SAAS,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,mBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,UAAI,MAAM,SAAS,GAAG;AAClB;AAAA,MACH;AACD,gBAAU,KAAK,IAAI;AAAA,IACtB;AACD,cAAU,SAAS,WAAU,CAAE;AAC/B,cAAU,KAAK,IAAI;AAAA,EACtB;AACL;AAhBS;AAiBT,SAAS,iBAAiB,WAAW,MAAM;AACvC,MAAI,KAAK,MAAM;AACX,iBAAa,WAAW,KAAK,IAAI;AAAA,EACpC,OACI;AACD,cAAU,KAAK,MAAM;AAAA,EACxB;AACL;AAPS;AAQT,SAAS,aAAa,WAAW,MAAM;AACnC,QAAM,EAAE,OAAQ,IAAG;AACnB,UAAQ,KAAK,MAAI;AAAA,IACb,KAAK;AACD,uBAAiB,WAAW,IAAI;AAChC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,0BAAoB,WAAW,IAAI;AACnC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAAa,CAAiC,IAAI;AAAA,QAAO;AAAA;AAAA,MAAgC,CAAA,IAAI,KAAK,KAAK,MAAM,IAAI;AAC1I;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,OAA8C,IAAI;AAAA,QAAO;AAAA;AAAA,MAAO,CAA2B,IAAI,KAAK,UAAU,KAAK,GAAG,CAAC,MAAM,IAAI;AAC1J;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,SACI;AACI,YAAM,mBAAmB,kBAAkB,6BAA6B,MAAM;AAAA,QAC1E,QAAQ;AAAA,QACR,MAAM,CAAC,KAAK,IAAI;AAAA,MACpC,CAAiB;AAAA,IACJ;AAAA,EACR;AACL;AAzCS;AA2CT,MAAM,WAAW,wBAAC,KAAK,UAAU,CAAE,MAC9B;AACD,QAAM,OAAO,SAAS,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACrD,QAAM,WAAW,SAAS,QAAQ,QAAQ,IACpC,QAAQ,WACR;AACN,QAAM,YAAY,CAAC,CAAC,QAAQ;AAE5B,QAAM,gBAAgB,QAAQ,iBAAiB,OACzC,QAAQ,gBACR,SAAS,UACL,MACA;AACV,QAAM,aAAa,QAAQ,aAAa,QAAQ,aAAa,SAAS;AACtE,QAAM,UAAU,IAAI,WAAW;AAC/B,QAAM,YAAY,oBAAoB,KAAK;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACR,CAAK;AACD,YAAU,KAAK,SAAS,WAAW,6BAA6B,YAAY;AAC5E,YAAU,OAAO,UAAU;AAC3B,MAAI,QAAQ,SAAS,GAAG;AACpB,cAAU,KAAK,WAAW,KAAK,QAAQ,IAAI,OAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU;AAC/E,cAAU,QAAO;AAAA,EACpB;AACD,YAAU,KAAK,SAAS;AACxB,eAAa,WAAW,GAAG;AAC3B,YAAU,SAAS,UAAU;AAC7B,YAAU,KAAK,GAAG;AAClB,SAAO,IAAI;AACX,QAAM,EAAE,MAAAA,OAAM,IAAK,IAAG,UAAU,QAAO;AACvC,SAAO;AAAA,IACH;AAAA,IACA,MAAAA;AAAA,IACA,KAAK,MAAM,IAAI,OAAQ,IAAG;AAAA;AAAA,EAClC;AACA,GAvCiB;AAyCjB,SAASS,cAAY,QAAQ,UAAU,IAAI;AACvC,QAAM,kBAAkB,OAAO,CAAE,GAAE,OAAO;AAC1C,QAAM,MAAM,CAAC,CAAC,gBAAgB;AAC9B,QAAM,eAAe,CAAC,CAAC,gBAAgB;AACvC,QAAM,iBAAiB,gBAAgB,YAAY,OAAO,OAAO,gBAAgB;AAEjF,QAAM,SAAS,aAAa,eAAe;AAC3C,QAAM,MAAM,OAAO,MAAM,MAAM;AAC/B,MAAI,CAAC,KAAK;AAEN,cAAU,KAAK,eAAe;AAE9B,WAAO,SAAS,KAAK,eAAe;AAAA,EACvC,OACI;AAED,sBAAkB,SAAS,GAAG;AAE9B,oBAAgB,OAAO,GAAG;AAE1B,WAAO,EAAE,KAAK,MAAM;EACvB;AACL;AAtBSA;AChkDT;AAAA;AAAA;AAAA;AAAA;AAaA,SAASC,qBAAmB;AACpB,MAAA,OAAO,8BAA8B,WAAW;AAChD,oBAAgB,4BAA4B;AAAA,EAChD;AACI,MAAA,OAAO,gCAAgC,WAAW;AAClD,oBAAgB,8BAA8B;AAAA,EAClD;AACI,MAAA,OAAO,sCAAsC,WAAW;AACxD,oBAAgB,oCAAoC;AAAA,EACxD;AACJ;AAVSA;AAYT,MAAM,mBAAoB,CAAA;AAC1B;AAAA,EAAiB;AAAA;AAA0B,IAAI;AAAA,EAC3C;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAA;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EAChE;AAAA,IAAC;AAAA;AAAA,EAAgC,GAAA;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAA;AAAA,IAAC;AAAA;AAAA,EAAyB;AACrE;AACA;AAAA,EAAiB;AAAA;AAAsB,IAAI;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAA;AAAA,IAAC;AAAA;AAAA,EAAsB;AAAA,EAC5D;AAAA,IAAC;AAAA;AAAA,EAA8B,GAAA;AAAA,IAAC;AAAA;AAAA,EAA2B;AAAA,EAC3D;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAA;AAAA,IAAC;AAAA;AAAA,EAAyB;AACrE;AACA;AAAA,EAAiB;AAAA;AAA2B,IAAI;AAAA,EAC5C;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAA;AAAA,IAAC;AAAA;AAAA,EAA2B;AAAA,EACjE;AAAA,IAAC;AAAA;AAAA,EAAgC,GAAA;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAA+B,GAAA;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AACpF;AACA;AAAA,EAAiB;AAAA;AAAuB,IAAI;AAAA,EACxC;AAAA,IAAC;AAAA;AAAA,EAAgC,GAAA;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAA+B,GAAA;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EAChF;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAA;AAAA,IAAC;AAAA,IAAwB;AAAA;AAAA,EAAoB;AAAA,EAClF;AAAA,IAAC;AAAA;AAAA,EAA8B,GAAA;AAAA,IAAC;AAAA,IAA6B;AAAA;AAAA,EAAoB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAoB;AAAA,EACzF;AAAA,IAAC;AAAA;AAAA,EAAsC,GAAA;AAAA,IAAC;AAAA,IAA2B;AAAA;AAAA,EAAoB;AAC3F;AACA;AAAA,EAAiB;AAAA;AAA0B,IAAI;AAAA,EAC3C;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAAA,EAC/F;AAAA,IAAC;AAAA;AAAA,EAAwC,GAAA;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAAA,EAChG;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IACpC;AAAA,IACA;AAAA;AAAA,EACJ;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAwC,GAAA;AAAA,IAAC;AAAA,IAAwB;AAAA;AAAA,EAA6B;AAAA,EAC/F;AAAA,IAAC;AAAA;AAAA,EAAA,GAAsC;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA+B,GAAA;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AACvF;AACA;AAAA,EAAiB;AAAA;AAA8B,IAAI;AAAA,EAC/C;AAAA,IAAC;AAAA;AAAA,EAAuC,GAAA;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AAAA,EAC3F;AAAA,IAAC;AAAA;AAAA,EAAA,GAAsC;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA+B,GAAA;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAC3F;AACA;AAAA,EAAiB;AAAA;AAA8B,IAAI;AAAA,EAC/C;AAAA,IAAC;AAAA;AAAA,EAAwC,GAAA;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AAAA,EAC5F;AAAA,IAAC;AAAA;AAAA,EAAA,GAAsC;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA+B,GAAA;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAC3F;AAIA,MAAM,iBAAiB;AACvB,SAAS,UAAU,KAAK;AACb,SAAA,eAAe,KAAK,GAAG;AAClC;AAFS;AAMT,SAAS,YAAY,KAAK;AAChB,QAAA,IAAI,IAAI,WAAW,CAAC;AAC1B,QAAM,IAAI,IAAI,WAAW,IAAI,SAAS,CAAC;AAChC,SAAA,MAAM,MAAM,MAAM,MAAQ,MAAM,MAAQ,IAAI,MAAM,GAAG,EAAE,IAAI;AACtE;AAJS;AAQT,SAAS,gBAAgB,IAAI;AACrB,MAAA,OAAO,UAAa,OAAO,MAAM;AAC1B,WAAA;AAAA,EACX;AACMV,QAAAA,QAAO,GAAG,WAAW,CAAC;AAC5B,UAAQA,OAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACM,aAAA;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACM,aAAA;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACM,aAAA;AAAA,EACf;AACO,SAAA;AACX;AA1BS;AAgCT,SAAS,cAAc,MAAM;AACnB,QAAA,UAAU,KAAK;AAEjB,MAAA,KAAK,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAAI,CAAC,GAAG;AAC1C,WAAA;AAAA,EACX;AACA,SAAO,UAAU,OAAO,IAClB,YAAY,OAAO,IACnB,MAAmC;AAC7C;AATS;AAaT,SAAS,MAAM,MAAM;AACjB,QAAM,OAAO,CAAA;AACb,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,eAAe;AACf,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACA,MAAA;AACJ,QAAM,UAAU,CAAA;AAChB;AAAA,IAAQ;AAAA;AAAA,EAAA,IAA0B,MAAM;AACpC,QAAI,QAAQ,QAAW;AACb,YAAA;AAAA,IAAA,OAEL;AACM,aAAA;AAAA,IACX;AAAA,EAAA;AAEJ;AAAA,IAAQ;AAAA;AAAA,EAAA,IAAwB,MAAM;AAClC,QAAI,QAAQ,QAAW;AACnB,WAAK,KAAK,GAAG;AACP,YAAA;AAAA,IACV;AAAA,EAAA;AAEJ;AAAA,IAAQ;AAAA;AAAA,EAAA,IAAsC,MAAM;AAChD;AAAA,MAAQ;AAAA;AAAA,IAAA;AACR;AAAA,EAAA;AAEJ;AAAA,IAAQ;AAAA;AAAA,EAAA,IAAiC,MAAM;AAC3C,QAAI,eAAe,GAAG;AAClB;AACO,aAAA;AACP;AAAA,QAAQ;AAAA;AAAA,MAAA;IAAwB,OAE/B;AACc,qBAAA;AACf,UAAI,QAAQ,QAAW;AACZ,eAAA;AAAA,MACX;AACA,YAAM,cAAc,GAAG;AACvB,UAAI,QAAQ,OAAO;AACR,eAAA;AAAA,MAAA,OAEN;AACD;AAAA,UAAQ;AAAA;AAAA,QAAA;MACZ;AAAA,IACJ;AAAA,EAAA;AAEJ,WAAS,qBAAqB;AACpB,UAAA,WAAW,KAAK,QAAQ,CAAC;AAC/B,QAAK,SAAS,KACV,aAAa,OACZ,SAAS,KACN,aAAa,KAAwC;AACzD;AACA,gBAAU,OAAO;AACjB;AAAA,QAAQ;AAAA;AAAA,MAAA;AACD,aAAA;AAAA,IACX;AAAA,EACJ;AAXS;AAYT,SAAO,SAAS,MAAM;AAClB;AACA,QAAI,KAAK,KAAK;AACV,QAAA,MAAM,QAAQ,sBAAsB;AACpC;AAAA,IACJ;AACA,WAAO,gBAAgB,CAAC;AACxB,cAAU,iBAAiB,IAAI;AAClB,iBAAA,QAAQ,IAAI,KAAK;AAAA,MAAQ;AAAA;AAAA,IAAiC,KAAA;AAEvE,QAAI,eAAe,GAAsB;AACrC;AAAA,IACJ;AACA,WAAO,WAAW,CAAC;AACf,QAAA,WAAW,CAAC,MAAM,QAAW;AACpB,eAAA,QAAQ,WAAW,CAAC,CAAC;AAC9B,UAAI,QAAQ;AACE,kBAAA;AACN,YAAA,aAAa,OAAO;AACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,SAAS,GAA2B;AAC7B,aAAA;AAAA,IACX;AAAA,EACJ;AACJ;AA3FS;AA6FT,MAAM,4BAAY;AAclB,SAAS,oBAAoB,KAAK,MAAM;AACpC,SAAOJ,WAAS,GAAG,IAAI,IAAI,IAAI,IAAI;AACvC;AAFS;AAgBT,SAAS,eAAe,KAAK,MAAM;AAE3B,MAAA,CAACA,WAAS,GAAG,GAAG;AACT,WAAA;AAAA,EACX;AAEI,MAAA,MAAM,MAAM,IAAI,IAAI;AACxB,MAAI,CAAC,KAAK;AACN,UAAM,MAAM,IAAI;AAChB,QAAI,KAAK;AACC,YAAA,IAAI,MAAM,GAAG;AAAA,IACvB;AAAA,EACJ;AAEA,MAAI,CAAC,KAAK;AACC,WAAA;AAAA,EACX;AAEA,QAAM,MAAM,IAAI;AAChB,MAAI,OAAO;AACX,MAAI,IAAI;AACR,SAAO,IAAI,KAAK;AACZ,UAAM,MAAM,KAAK,IAAI,CAAC,CAAC;AACvB,QAAI,QAAQ,QAAW;AACZ,aAAA;AAAA,IACX;AACI,QAAA,WAAW,IAAI,GAAG;AACX,aAAA;AAAA,IACX;AACO,WAAA;AACP;AAAA,EACJ;AACO,SAAA;AACX;AAjCS;AAmCT,MAAM,mBAAmB,wBAAC,QAAQ,KAAT;AACzB,MAAM,kBAAkB,wBAAC,QAAQ,IAAT;AACxB,MAAM,4BAA4B;AAClC,MAAM,oBAAoB,wBAAC,WAAW,OAAO,WAAW,IAAI,KAAKG,OAAK,MAAM,GAAlD;AAC1B,MAAM,sBAAsB;AAC5B,SAAS,cAAc,QAAQ,eAAe;AACjC,WAAA,KAAK,IAAI,MAAM;AACxB,MAAI,kBAAkB,GAAG;AAErB,WAAO,SACD,SAAS,IACL,IACA,IACJ;AAAA,EACV;AACA,SAAO,SAAS,KAAK,IAAI,QAAQ,CAAC,IAAI;AAC1C;AAXS;AAYT,SAAS,eAAe,SAAS;AAE7B,QAAM,QAAQ,SAAS,QAAQ,WAAW,IACpC,QAAQ,cACR;AAEN,SAAO,QAAQ,UAAU,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,CAAC,KAC5E,SAAS,QAAQ,MAAM,KAAK,IACxB,QAAQ,MAAM,QACd,SAAS,QAAQ,MAAM,CAAC,IACpB,QAAQ,MAAM,IACd,QACR;AACV;AAbS;AAcT,SAAS,eAAe,aAAa,OAAO;AACpC,MAAA,CAAC,MAAM,OAAO;AACd,UAAM,QAAQ;AAAA,EAClB;AACI,MAAA,CAAC,MAAM,GAAG;AACV,UAAM,IAAI;AAAA,EACd;AACJ;AAPS;AAQT,SAAS,qBAAqB,UAAU,IAAI;AACxC,QAAM,SAAS,QAAQ;AACjB,QAAA,cAAc,eAAe,OAAO;AAC1C,QAAM,aAAaH,WAAS,QAAQ,WAAW,KAC3CE,WAAS,MAAM,KACf,WAAW,QAAQ,YAAY,MAAM,CAAC,IACpC,QAAQ,YAAY,MAAM,IAC1B;AACN,QAAM,gBAAgBF,WAAS,QAAQ,WAAW,KAC9CE,WAAS,MAAM,KACf,WAAW,QAAQ,YAAY,MAAM,CAAC,IACpC,gBACA;AACA,QAAA,SAAS,wBAAC,aAAa;AACzB,WAAO,SAAS,WAAW,aAAa,SAAS,QAAQ,aAAa,CAAC;AAAA,EAAA,GAD5D;AAGT,QAAA,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,OAAO,wBAAC,UAAU,MAAM,KAAK,GAAtB;AAEP,QAAA,SAAS,QAAQ,SAAS,OAAO;AACvC,WAAS,QAAQ,WAAW,KAAK,eAAe,aAAa,MAAM;AACnE,QAAM,QAAQ,wBAAC,QAAQ,OAAO,GAAG,GAAnB;AACd,WAAS,QAAQ,KAAK;AAElB,UAAM,MAAM,WAAW,QAAQ,QAAQ,IACjC,QAAQ,SAAS,GAAG,IACpBF,WAAS,QAAQ,QAAQ,IACrB,QAAQ,SAAS,GAAG,IACpB;AACH,WAAA,CAAC,MACF,QAAQ,SACJ,QAAQ,OAAO,QAAQ,GAAG,IAC1B,kBACJ;AAAA,EACV;AAZS;AAaH,QAAA,YAAY,wBAAC,SAAS,QAAQ,YAC9B,QAAQ,UAAU,IAAI,IACtB,kBAFY;AAGlB,QAAM,YAAY,cAAc,QAAQ,SAAS,KAAK,WAAW,QAAQ,UAAU,SAAS,IACtF,QAAQ,UAAU,YAClB;AACN,QAAM,cAAc,cAAc,QAAQ,SAAS,KAC/C,WAAW,QAAQ,UAAU,WAAW,IACtC,QAAQ,UAAU,cAClB;AACN,QAAM,OAAO,cAAc,QAAQ,SAAS,KAAKE,WAAS,QAAQ,UAAU,IAAI,IAC1E,QAAQ,UAAU,OAClB;AACA,QAAA,SAAS,wBAAC,QAAQ,SAAS;AACvB,UAAA,CAAC,MAAM,IAAI,IAAI;AACrB,QAAIa,QAAO;AACX,QAAI,WAAW;AACX,QAAA,KAAK,WAAW,GAAG;AACf,UAAAf,WAAS,IAAI,GAAG;AAChB,mBAAW,KAAK,YAAY;AAC5Be,gBAAO,KAAK,QAAQA;AAAAA,MAAA,WAEfb,WAAS,IAAI,GAAG;AACrB,mBAAW,QAAQ;AAAA,MACvB;AAAA,IAAA,WAEK,KAAK,WAAW,GAAG;AACpB,UAAAA,WAAS,IAAI,GAAG;AAChB,mBAAW,QAAQ;AAAA,MACvB;AACI,UAAAA,WAAS,IAAI,GAAG;AAChBa,gBAAO,QAAQA;AAAAA,MACnB;AAAA,IACJ;AACA,UAAM,MAAM,QAAQ,GAAG,EAAE,GAAG;AACtB,UAAA;AAAA;AAAA,MAENA,UAAS,WAAW,QAAQ,GAAG,KAAK,WAC9B,IAAI,CAAC,IACL;AAAA;AACN,WAAO,WAAW,UAAU,QAAQ,EAAE,KAAKA,KAAI,IAAI;AAAA,EAAA,GA3BxC;AA6Bf,QAAM,MAAM;AAAA,IACR;AAAA,MAAC;AAAA;AAAA,IAAA,GAAkC;AAAA,IACnC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAoC;AAAA,IACrC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAsC;AAAA,IACvC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAsC;AAAA,IACvC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAwC;AAAA,IACzC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAkC;AAAA,IACnC;AAAA,MAAC;AAAA;AAAA,IAAA,GAAgD;AAAA,IACjD;AAAA,MAAC;AAAA;AAAA,IAAA,GAA4C;AAAA,IAC7C;AAAA,MAAC;AAAA;AAAA,IAAsCd,GAAAA,SAAO,OAAO,GAAG,OAAO,MAAM;AAAA,EAAA;AAElE,SAAA;AACX;AAzFS;AA2FT,IAAI,WAAW;AACf,SAAS,gBAAgB,MAAM;AAChB,aAAA;AACf;AAFS;AAGT,SAAS,kBAAkB;AAChB,SAAA;AACX;AAFS;AAGT,SAAS,iBAAiB,MAAM,SAAS,MAAM;AAGvC,cAAA,SAAS,KAAK,aAAiD;AAAA,IAC3D,WAAW,KAAK,IAAI;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACH;AACT;AATS;AAUT,MAAM,oBAAmC;AAAA,EAAmB;AAAA;AAAiE;AAC7H,SAAS,mBAAmB,MAAM;AAC9B,SAAO,CAAC,aAAa,YAAY,SAAS,KAAK,MAAM,QAAQ;AACjE;AAFS;AAIT,MAAMe,WAAS,iBAAiB;AAChC,MAAMC,UAAQ,YAAYD,QAAM;AAChC,MAAM,gBAAgB;AAAA,EAClB,eAAeA;AAAAA;AAAAA,EACf,uBAAuBC,QAAM;AAAA;AAAA,EAC7B,sBAAsBA,QAAM;AAAA;AAAA,EAC5B,2BAA2BA,QAAM;AAAA;AAAA,EACjC,oBAAoBA,QAAM;AAAA;AAAA,EAC1B,yBAAyBA,QAAM;AAAA;AAAA,EAC/B,sCAAsCA,QAAM;AAAA;AAAA,EAC5C,kBAAkBA,QAAM;AAAA;AAC5B;AAEA,MAAMV,iBAAe;AAAA,EACjB,CAAC,cAAc,aAAa,GAAG;AAAA,EAC/B,CAAC,cAAc,qBAAqB,GAAG;AAAA,EACvC,CAAC,cAAc,oBAAoB,GAAG;AAAA,EACtC,CAAC,cAAc,yBAAyB,GAAG;AAAA,EAC3C,CAAC,cAAc,kBAAkB,GAAG;AAAA,EACpC,CAAC,cAAc,uBAAuB,GAAG;AAAA,EACzC,CAAC,cAAc,oCAAoC,GAAG;AAC1D;AACA,SAASW,iBAAed,UAAS,MAAM;AACnC,SAAOe,SAASZ,eAAaH,KAAI,GAAG,GAAG,IAAI;AAC/C;AAFSc;AAIT,MAAMd,SAAO,kBAAkB;AAC/B,MAAMgB,QAAM,YAAYhB,MAAI;AAC5B,MAAM,iBAAiB;AAAA,EACnB,kBAAkBA;AAAAA;AAAAA,EAClB,uBAAuBgB,MAAI;AAAA;AAAA,EAC3B,2BAA2BA,MAAI;AAAA;AAAA,EAC/B,gCAAgCA,MAAI;AAAA;AAAA,EACpC,kCAAkCA,MAAI;AAAA;AAAA,EACtC,mCAAmCA,MAAI;AAAA;AAAA,EACvC,yBAAyBA,MAAI;AAAA;AAAA,EAC7B,kBAAkBA,MAAI;AAAA;AAC1B;AACA,SAAS,gBAAgBhB,OAAM;AACpB,SAAA,mBAAmBA,OAAM,MAAO,QAAyC,EAAE,UAAUI,gBAAA,IAAkB,MAAS;AAC3H;AAFS;AAIT,MAAMA,kBAAgB;AAAA,EAClB,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,qBAAqB,GAAG;AAAA,EAExC,CAAC,eAAe,yBAAyB,GAAG;AAAA,EAC5C,CAAC,eAAe,8BAA8B,GAAG;AAAA,EACjD,CAAC,eAAe,gCAAgC,GAAG;AAAA,EACnD,CAAC,eAAe,iCAAiC,GAAG;AAAA,EACpD,CAAC,eAAe,uBAAuB,GAAG;AAC9C;AAGA,SAAS,UAAU,SAAS,SAAS;AAC1B,SAAA,QAAQ,UAAU,OACnB,cAAc,QAAQ,MAAM,IAC5B,cAAc,QAAQ,MAAM;AACtC;AAJS;AAKT,IAAI;AAEJ,SAAS,cAAc,QAAQ;AACvB,MAAAN,WAAS,MAAM,GAAG;AACX,WAAA;AAAA,EAAA,OAEN;AACG,QAAA,WAAW,MAAM,GAAG;AAChB,UAAA,OAAO,gBAAgB,kBAAkB,MAAM;AACxC,eAAA;AAAA,MAEF,WAAA,OAAO,YAAY,SAAS,YAAY;AAC7C,cAAM,UAAU;AACZ,YAAA,UAAU,OAAO,GAAG;AACd,gBAAA,gBAAgB,eAAe,gCAAgC;AAAA,QACzE;AACA,eAAQ,iBAAiB;AAAA,MAAA,OAExB;AACK,cAAA,gBAAgB,eAAe,iCAAiC;AAAA,MAC1E;AAAA,IAAA,OAEC;AACK,YAAA,gBAAgB,eAAe,uBAAuB;AAAA,IAChE;AAAA,EACJ;AACJ;AAxBS;AAyCT,SAAS,mBAAmB,KAAK,UAAU,OACzC;AAES,SAAA,CAAC,GAAG,oBAAI,IAAI;AAAA,IACX;AAAA,IACA,GAAI,QAAQ,QAAQ,IACd,WACAF,WAAS,QAAQ,IACb,OAAO,KAAK,QAAQ,IACpBE,WAAS,QAAQ,IACb,CAAC,QAAQ,IACT,CAAC,KAAK;AAAA,EACvB,CAAA,CAAC;AACV;AAbS;AA8BT,SAAS,wBAAwB,KAAK,UAAU,OAAO;AACnD,QAAM,cAAcA,WAAS,KAAK,IAAI,QAAQ;AAC9C,QAAM,UAAU;AACZ,MAAA,CAAC,QAAQ,oBAAoB;AACrB,YAAA,yCAAyB;EACrC;AACA,MAAI,QAAQ,QAAQ,mBAAmB,IAAI,WAAW;AACtD,MAAI,CAAC,OAAO;AACR,YAAQ,CAAA;AAEJ,QAAA,QAAQ,CAAC,KAAK;AAEX,WAAA,QAAQ,KAAK,GAAG;AACX,cAAA,mBAAmB,OAAO,OAAO,QAAQ;AAAA,IACrD;AAGA,UAAM,WAAW,QAAQ,QAAQ,KAAK,CAAC,cAAc,QAAQ,IACvD,WACA,SAAS,SAAS,IACd,SAAS,SAAS,IAClB;AAEV,YAAQA,WAAS,QAAQ,IAAI,CAAC,QAAQ,IAAI;AACtC,QAAA,QAAQ,KAAK,GAAG;AACG,yBAAA,OAAO,OAAO,KAAK;AAAA,IAC1C;AACQ,YAAA,mBAAmB,IAAI,aAAa,KAAK;AAAA,EACrD;AACO,SAAA;AACX;AA9BS;AA+BT,SAAS,mBAAmB,OAAO,OAAO,QAAQ;AAC9C,MAAI,SAAS;AACJ,WAAA,IAAI,GAAG,IAAI,MAAM,UAAU,UAAU,MAAM,GAAG,KAAK;AAClD,UAAA,SAAS,MAAM,CAAC;AAClB,QAAAA,WAAS,MAAM,GAAG;AAClB,eAAS,oBAAoB,OAAO,MAAM,CAAC,GAAG,MAAM;AAAA,IACxD;AAAA,EACJ;AACO,SAAA;AACX;AATS;AAUT,SAAS,oBAAoB,OAAO,QAAQ,QAAQ;AAC5C,MAAA;AACE,QAAA,SAAS,OAAO,MAAM,GAAG;AAC5B,KAAA;AACO,UAAA,SAAS,OAAO,KAAK,GAAG;AACrB,aAAA,kBAAkB,OAAO,QAAQ,MAAM;AACzC,WAAA,OAAO,IAAI,CAAC;AAAA,EACvB,SAAS,OAAO,UAAU,WAAW;AAC9B,SAAA;AACX;AATS;AAUT,SAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAC9C,MAAI,SAAS;AACb,MAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AAChB,aAAA;AACT,QAAI,QAAQ;AACR,eAAS,OAAO,OAAO,SAAS,CAAC,MAAM;AACvC,YAAM,SAAS,OAAO,QAAQ,MAAM,EAAE;AACtC,YAAM,KAAK,MAAM;AACZ,WAAA,QAAQ,MAAM,KAAK,cAAc,MAAM,MACxC,OAAO,MAAM,GACf;AAEE,iBAAS,OAAO,MAAM;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AAjBS;AAwBT,MAAMmB,YAAU;AAChB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,wBAAwB;AAC9B,MAAM,aAAa,wBAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,EAAE,kBAAA,CAAmB,GAAG,IAAI,OAAO,CAAC,CAAC,IAA7D;AACnB,SAAS,4BAA4B;AAC1B,SAAA;AAAA,IACH,OAAO,wBAAC,KAAK,SAAS;AAElB,aAAO,SAAS,UAAUnB,WAAS,GAAG,IAChC,IAAI,YACJ,IAAA,SAAS,WAAWF,WAAS,GAAG,KAAK,iBAAiB,MAClD,IAAI,SAAS,YACb,IAAA;AAAA,IACd,GAPO;AAAA,IAQP,OAAO,wBAAC,KAAK,SAAS;AAElB,aAAO,SAAS,UAAUE,WAAS,GAAG,IAChC,IAAI,YACJ,IAAA,SAAS,WAAWF,WAAS,GAAG,KAAK,iBAAiB,MAClD,IAAI,SAAS,YACb,IAAA;AAAA,IACd,GAPO;AAAA,IAQP,YAAY,wBAAC,KAAK,SAAS;AAEvB,aAAQ,SAAS,UAAUE,WAAS,GAAG,IACjC,WAAW,GAAG,IACd,SAAS,WAAWF,WAAS,GAAG,KAAK,iBAAiB,MAClD,WAAW,IAAI,QAAQ,IACvB;AAAA,IACd,GAPY;AAAA,EAOZ;AAER;AA3BS;AA4BT,IAAI;AACJ,SAAS,wBAAwB,UAAU;AAC3B,cAAA;AAChB;AAFS;AAGT,IAAI;AAQJ,SAAS,wBAAwB,UAAU;AAC3B,cAAA;AAChB;AAFS;AAGT,IAAI;AAQJ,SAAS,yBAAyB,YAAY;AAC5B,gBAAA;AAClB;AAFS;AAIT,IAAI,kBAAmB;AAEvB,MAAM,oBAAoB,mDAAC,SAAS;AACd,oBAAA;AACtB,GAF0B;AAI1B,MAAM,oBAAoB,wDAAM,iBAAN;AAC1B,IAAI,mBAAmB;AACvB,MAAM,qBAAqB,wBAAC,YAAY;AACjB,qBAAA;AACvB,GAF2B;AAG3B,MAAM,qBAAqB,6BAAM,kBAAN;AAE3B,IAAI,OAAO;AACX,SAAS,kBAAkB,UAAU,IAAI;AAErC,QAAM,SAAS,WAAW,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAC7D,QAAM,UAAUE,WAAS,QAAQ,OAAO,IAAI,QAAQ,UAAUmB;AACxD,QAAA,SAASnB,WAAS,QAAQ,MAAM,KAAK,WAAW,QAAQ,MAAM,IAC9D,QAAQ,SACR;AACN,QAAM,UAAU,WAAW,MAAM,IAAI,iBAAiB;AACtD,QAAM,iBAAiB,QAAQ,QAAQ,cAAc,KACjD,cAAc,QAAQ,cAAc,KACpCA,WAAS,QAAQ,cAAc,KAC/B,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACA,QAAA,WAAW,cAAc,QAAQ,QAAQ,IACzC,QAAQ,WACR,gBAAgB,OAAO;AACvB,QAAA,kBAAkB,cAAc,QAAQ,eAAe,IACnD,QAAQ,kBACR,gBAAgB,OAAO;AAE3B,QAAA,gBAAgB,cAAc,QAAQ,aAAa,IAC/C,QAAQ,gBACR,gBAAgB,OAAO;AAEjC,QAAM,YAAYD,SAAO,OAAA,GAAU,QAAQ,WAAW,2BAA2B;AAC3E,QAAA,cAAc,QAAQ,eAAe,OAAO;AAClD,QAAM,UAAU,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC1D,QAAA,cAAc,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC5E,QAAQ,cACR;AACA,QAAA,eAAe,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC/E,QAAQ,eACR;AACA,QAAA,iBAAiB,CAAC,CAAC,QAAQ;AAC3B,QAAA,cAAc,CAAC,CAAC,QAAQ;AAC9B,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY;AACzE,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACA,QAAA,kBAAkB,CAAC,CAAC,QAAQ;AAClC,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,MAAK,OAGoC;AAC5B,aAAAiB,iBAAe,cAAc,oCAAoC,CAAC;AAAA,EAC/E;AACA,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR,aAAa;AACnB,QAAM,mBAAmB,WAAW,QAAQ,gBAAgB,IACtD,QAAQ,mBACR,eAAe;AACrB,QAAM,kBAAkBlB,WAAS,QAAQ,eAAe,IAClD,QAAQ,kBACR;AAEN,QAAM,kBAAkB;AAClB,QAAA,uBAAuBA,WAAS,gBAAgB,oBAAoB,IAChE,gBAAgB,2CACZ;AAER,QAAA,qBAAqBA,WAAS,gBAAgB,kBAAkB,IAC5D,gBAAgB,yCACZ;AAEd,QAAM,SAASA,WAAS,gBAAgB,MAAM,IAAI,gBAAgB,SAAS;AAC3E;AACA,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ;AACI,YAAQ,kBAAkB;AAC1B,YAAQ,gBAAgB;AACxB,YAAQ,uBAAuB;AAC/B,YAAQ,qBAAqB;AAAA,EACjC;AAEA,MAAK,OAAwC;AACzC,YAAQ,cACJ,gBAAgB,eAAe,OACzB,gBAAgB,cAChB;AAAA,EACd;AAEA,MAA+C,2BAA2B;AACrD,qBAAA,SAAS,SAAS,MAAM;AAAA,EAC7C;AACO,SAAA;AACX;AApHS;AAqHT,MAAM,kBAAkB,wBAAC,YAAY,EAAE,CAAC,MAAM,GAAG,OAAS,EAAA,IAAlC;AAExB,SAAS,wBAAwB,UAAU,KAAK;AAC5C,SAAO,oBAAoB,SAAS,SAAS,KAAK,GAAG,IAAI;AAC7D;AAFS;AAIT,SAAS,uBAAuB,SAAS,KAAK;AAC1C,SAAO,mBAAmB,SAAS,QAAQ,KAAK,GAAG,IAAI;AAC3D;AAFS;AAIT,SAAS,cAAc,SAAS,KAAK,QAAQ,aAAa,MAAM;AACtD,QAAA,EAAE,SAAS,OAAW,IAAA;AAE5B,MAAK,OAAwC;AACzC,UAAM,UAAU,QAAQ;AACxB,QAAI,SAAS;AACT,cAAQ,KAAK,WAAmD;AAAA,QAC5D;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,MAAA,CAC1B;AAAA,IACL;AAAA,EACJ;AACA,MAAI,YAAY,MAAM;AAClB,UAAM,MAAM,QAAQ,SAAS,QAAQ,KAAK,IAAI;AACvC,WAAAE,WAAS,GAAG,IAAI,MAAM;AAAA,EAAA,OAE5B;AACD,QAAK,OAAoF;AACrF,aAAOgB,iBAAe,cAAc,eAAe,EAAE,KAAK,OAAQ,CAAA,CAAC;AAAA,IACvE;AACO,WAAA;AAAA,EACX;AACJ;AAxBS;AA0BT,SAAS,qBAAqB,KAAK,QAAQ,UAAU;AACjD,QAAM,UAAU;AACR,UAAA,yCAAyB;AAC7B,MAAA,iBAAiB,KAAK,UAAU,MAAM;AAC9C;AAJS;AAMT,SAAS,mBAAmB,QAAQ,eAAe;AAC/C,MAAI,WAAW;AACJ,WAAA;AACJ,SAAA,OAAO,MAAM,GAAG,EAAE,CAAC,MAAM,cAAc,MAAM,GAAG,EAAE,CAAC;AAC9D;AAJS;AAMT,SAAS,mBAAmB,cAAc,SAAS;AACzC,QAAA,QAAQ,QAAQ,QAAQ,YAAY;AAC1C,MAAI,UAAU,IAAI;AACP,WAAA;AAAA,EACX;AACA,WAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAC7C,QAAI,mBAAmB,cAAc,QAAQ,CAAC,CAAC,GAAG;AACvC,aAAA;AAAA,IACX;AAAA,EACJ;AACO,SAAA;AACX;AAXS;AAcT,SAAS,OAAO,KAAK;AACjB,QAAM,MAAM,wBAAC,QAAQ,YAAY,KAAK,GAAG,GAA7B;AACL,SAAA;AACX;AAHS;AAIT,SAAS,YAAY,KAAK,KAAK;AACrB,QAAA,OAAO,YAAY,GAAG;AAC5B,MAAI,QAAQ,MAAM;AACR,UAAA;AAAA,MAAwB;AAAA;AAAA,IAAA;AAAA,EAClC;AACM,QAAA,OAAO,YAAY,IAAI;AAC7B,MAAI,SAAS,GAA0B;AACnC,UAAM,SAAS;AACT,UAAA,QAAQ,aAAa,MAAM;AACjC,WAAO,IAAI,OAAO,MAAM,OAAO,CAAC,UAAU,MAAM;AAAA,MAC5C,GAAG;AAAA,MACH,mBAAmB,KAAK,CAAC;AAAA,IAAA,GAC1B,CAAA,CAAE,CAAC;AAAA,EAAA,OAEL;AACM,WAAA,mBAAmB,KAAK,IAAI;AAAA,EACvC;AACJ;AAjBS;AAkBT,MAAM,aAAa,CAAC,KAAK,MAAM;AAC/B,SAAS,YAAY,MAAM;AAChB,SAAA,aAAa,MAAM,UAAU;AACxC;AAFS;AAGT,MAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,aAAa,MAAM;AACxB,SAAO,aAAa,MAAM,aAAa,CAAE,CAAA;AAC7C;AAFS;AAGT,SAAS,mBAAmB,KAAK,MAAM;AAC7B,QAAA,UAAU,cAAc,IAAI;AAClC,MAAI,WAAW,MAAM;AACV,WAAA,IAAI,SAAS,SACd,UACA,IAAI,UAAU,CAAC,OAAO,CAAC;AAAA,EAAA,OAE5B;AACD,UAAM,WAAW,aAAa,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,kBAAkB,KAAK,CAAC,CAAC,GAAG,CAAA,CAAE;AACvF,WAAA,IAAI,UAAU,QAAQ;AAAA,EACjC;AACJ;AAXS;AAYT,MAAM,eAAe,CAAC,KAAK,QAAQ;AACnC,SAAS,cAAc,MAAM;AAClB,SAAA,aAAa,MAAM,YAAY;AAC1C;AAFS;AAGT,MAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,aAAa,MAAM;AACxB,SAAO,aAAa,MAAM,aAAa,CAAE,CAAA;AAC7C;AAFS;AAGT,SAAS,kBAAkB,KAAK,MAAM;AAC5B,QAAA,OAAO,YAAY,IAAI;AAC7B,UAAQ,MAAM;AAAA,IACV,KAAK,GAAwB;AAClB,aAAA,aAAa,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,KAAK,GAA2B;AACrB,aAAA,aAAa,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,KAAK,GAAyB;AAC1B,YAAM,QAAQ;AACd,UAAI,OAAO,OAAO,GAAG,KAAK,MAAM,GAAG;AAC/B,eAAO,IAAI,YAAY,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,MAC7C;AACA,UAAI,OAAO,OAAO,KAAK,KAAK,MAAM,KAAK;AACnC,eAAO,IAAI,YAAY,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,MAC/C;AACA,YAAM,wBAAwB,IAAI;AAAA,IACtC;AAAA,IACA,KAAK,GAAwB;AACzB,YAAM,OAAO;AACb,UAAI,OAAO,MAAM,GAAG,KAAK,SAAS,KAAK,CAAC,GAAG;AACvC,eAAO,IAAI,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,MAC3C;AACA,UAAI,OAAO,MAAM,OAAO,KAAK,SAAS,KAAK,KAAK,GAAG;AAC/C,eAAO,IAAI,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAC/C;AACA,YAAM,wBAAwB,IAAI;AAAA,IACtC;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACT,YAAA,WAAW,sBAAsB,MAAM;AACvC,YAAA,MAAM,iBAAiB,MAAM;AACnC,aAAO,IAAI,OAAO,kBAAkB,KAAK,GAAG,GAAG,WAAW,kBAAkB,KAAK,QAAQ,IAAI,QAAW,IAAI,IAAI;AAAA,IACpH;AAAA,IACA,KAAK,GAA6B;AACvB,aAAA,aAAa,MAAM,IAAI;AAAA,IAClC;AAAA,IACA,KAAK,GAAkC;AAC5B,aAAA,aAAa,MAAM,IAAI;AAAA,IAClC;AAAA,IACA;AACI,YAAM,IAAI,MAAM,0CAA0C,IAAI,EAAE;AAAA,EACxE;AACJ;AA5CS;AA6CT,MAAM,aAAa,CAAC,KAAK,MAAM;AAC/B,SAAS,YAAY,MAAM;AAChB,SAAA,aAAa,MAAM,UAAU;AACxC;AAFS;AAGT,MAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,aAAa,MAAM,MAAM;AACxB,QAAA,WAAW,aAAa,MAAM,WAAW;AAC/C,MAAI,UAAU;AACH,WAAA;AAAA,EAAA,OAEN;AACD,UAAM,wBAAwB,IAAI;AAAA,EACtC;AACJ;AARS;AAST,MAAM,iBAAiB,CAAC,KAAK,UAAU;AACvC,SAAS,sBAAsB,MAAM;AAC1B,SAAA,aAAa,MAAM,cAAc;AAC5C;AAFS;AAGT,MAAM,YAAY,CAAC,KAAK,KAAK;AAC7B,SAAS,iBAAiB,MAAM;AACtB,QAAA,WAAW,aAAa,MAAM,SAAS;AAC7C,MAAI,UAAU;AACH,WAAA;AAAA,EAAA,OAEN;AACK,UAAA;AAAA,MAAwB;AAAA;AAAA,IAAA;AAAA,EAClC;AACJ;AARS;AAST,SAAS,aAAa,MAAM,OAAO,cAAc;AAC7C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC7B,UAAA,OAAO,MAAM,CAAC;AAEpB,QAAI,OAAO,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM;AAE1C,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AACO,SAAA;AACX;AAVS;AAWT,SAAS,wBAAwB,MAAM;AACnC,SAAO,IAAI,MAAM,wBAAwB,IAAI,EAAE;AACnD;AAFS;AAIT,MAAM,eAAe;AACrB,SAAS,iBAAiB,QAAQ,iBAAiB;AAC3C,MAAA,mBAAmB,cAAc,MAAM,GAAG;AAC1C,SAAKC,SAAS,cAAc,EAAE,OAAA,CAAQ,CAAC;AAAA,EAC3C;AACJ;AAJS;AAKT,MAAM,oBAAoB,wBAAC,YAAY,SAAb;AAC1B,IAAI,eAAe,OAAO;AAC1B,SAAS,cAAc,OAAO;AACtB,MAAA,MAAM,SAAS,iBAAiB,mBAAmB;AAC9C,SAAA;AAAA;AAAA,qCAGqC,MAAM,OAAO,GAAG;AAAA,EAC9D;AACJ;AAPS;AAQT,SAAS,oBAAoB;AACzB,iBAAe,OAAO;AAC1B;AAFS;AAGT,SAAS,aAAa,KAAK;AACvB,SAAQnB,WAAS,GAAG,KAChB,YAAY,GAAG,MAAM,MACpB,OAAO,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAC/C;AAJS;AAKT,SAAS,YAAY,SAAS,UAAU,IAAI;AAExC,MAAI,cAAc;AACZ,QAAA,UAAU,QAAQ,WAAW;AAC3B,UAAA,UAAU,CAAC,QAAQ;AACT,kBAAA;AACd,YAAQ,GAAG;AAAA,EAAA;AAGf,SAAO,EAAE,GAAG,cAAc,SAAS,OAAO,GAAG,YAAY;AAC7D;AAVS;AAYT,MAAM,oBAAoB,mDAAC,SAAS,YAAY;AACxC,MAAA,CAACE,WAAS,OAAO,GAAG;AACd,UAAA,gBAAgB,eAAe,8BAA8B;AAAA,EACvE;AAEA,MAAK,OAAwC;AACzC,YAAQ,SAAS;AAAA,EACrB;AACA;AAEI,UAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AAGA,UAAA,aAAa,QAAQ,cAAc;AACnC,UAAA,WAAW,WAAW,OAAO;AAC7B,UAAA,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACD,aAAA;AAAA,IACX;AAEA,UAAM,EAAE,MAAAE,OAAM,YAAgB,IAAA,YAAY,SAAS,OAAO;AAE1D,UAAM,MAAM,IAAI,SAAS,UAAUA,KAAI,EAAE;AAEzC,WAAO,CAAC,cACD,aAAa,QAAQ,IAAI,MAC1B;AAAA,EACV;AACJ,GA9B0B;AA+B1B,SAAS,QAAQ,SAAS,SAAS;AAE/B,MAAK,OAAwC;AACzC,YAAQ,SAAS;AAAA,EACrB;AACA,MAAM,+BAA+B,CAAC,qCAClCF,WAAS,OAAO,GAAG;AAEnB,UAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AAGA,UAAA,aAAa,QAAQ,cAAc;AACnC,UAAA,WAAW,WAAW,OAAO;AAC7B,UAAA,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACD,aAAA;AAAA,IACX;AAEA,UAAM,EAAE,KAAK,gBAAgB,YAAY,SAAS;AAAA,MAC9C,GAAG;AAAA,MACH,UAAW;AAAA,MACX,KAAK;AAAA,IAAA,CACR;AAEK,UAAA,MAAM,OAAO,GAAG;AAEtB,WAAO,CAAC,cACD,aAAa,QAAQ,IAAI,MAC1B;AAAA,EAAA,OAEL;AACD,QAAK,OAAkE;AAC9D,WAAA,yCAAyC,QAAQ,GAAG,wCAAwC;AACjG,aAAQ,MAAM;AAAA,IAClB;AAEA,UAAM,WAAW,QAAQ;AACzB,QAAI,UAAU;AACJ,YAAA,SAAS,aAAa,QAAQ;AACpC,UAAI,QAAQ;AACD,eAAA;AAAA,MACX;AAEA,aAAQ,aAAa,QAAQ,IACzB,OAAO,OAAO;AAAA,IAAA,OAEjB;AACD,aAAO,OAAO,OAAO;AAAA,IACzB;AAAA,EACJ;AACJ;AApDS;AAsDT,MAAM,wBAAwB,6BAAM,IAAN;AAC9B,MAAM,oBAAoB,wBAAC,QAAQ,WAAW,GAAG,GAAvB;AAE1B,SAAS,UAAU,YAAY,MAAM;AACjC,QAAM,EAAE,gBAAgB,iBAAiB,aAAa,iBAAiB,gBAAgB,SAAa,IAAA;AACpG,QAAM,CAAC,KAAK,OAAO,IAAI,mBAAmB,GAAG,IAAI;AACjD,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR,QAAQ;AACR,QAAA,kBAAkB,CAAC,CAAC,QAAQ;AAE5B,QAAA,kBAAkBA,WAAS,QAAQ,OAAO,KAAK,UAAU,QAAQ,OAAO,IACxE,CAAC,UAAU,QAAQ,OAAO,IACtB,QAAQ,UACP,CAAC,kBAAkB,MAAM,MAAM,MACpC,iBACK,CAAC,kBAAkB,MAAM,MAAM,MAChC;AACJ,QAAA,mBAAmB,kBAAkB,oBAAoB;AACzD,QAAA,SAAS,UAAU,SAAS,OAAO;AAEzC,qBAAmB,aAAa,OAAO;AAGvC,MAAI,CAAC,aAAa,cAAc,OAAO,IAAI,CAAC,kBACtC,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,WAAW,IACpF;AAAA,IACE;AAAA,IACA;AAAA,IACA,SAAS,MAAM,KAAK,OAAO;AAAA,EAAA;AAOnC,MAAIH,UAAS;AAEb,MAAI,eAAe;AACf,MAAA,CAAC,mBACD,EAAEG,WAASH,OAAM,KACb,aAAaA,OAAM,KACnB,kBAAkBA,OAAM,IAAI;AAChC,QAAI,kBAAkB;AAClBA,gBAAS;AACMA,qBAAAA;AAAAA,IACnB;AAAA,EACJ;AAEA,MAAI,CAAC,oBACA,EAAEG,WAASH,OAAM,KACd,aAAaA,OAAM,KACnB,kBAAkBA,OAAM,MACxB,CAACG,WAAS,YAAY,IAAI;AAC9B,WAAO,cAAc,eAAe;AAAA,EACxC;AAEA,MAAK,OAA+F;AAC3F,SAAA,yLAGgC,GAAG,IAAI;AACrC,WAAA;AAAA,EACX;AAEA,MAAI,WAAW;AACf,QAAM,UAAU,6BAAM;AACP,eAAA;AAAA,EAAA,GADC;AAIhB,QAAM,MAAM,CAAC,kBAAkBH,OAAM,IAC/B,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,OAAO,IAC9EA;AAEN,MAAI,UAAU;AACHA,WAAAA;AAAAA,EACX;AAEA,QAAM,aAAa,yBAAyB,SAAS,cAAc,SAAS,OAAO;AAC7E,QAAA,aAAa,qBAAqB,UAAU;AAClD,QAAM,WAAW,gBAAgB,SAAS,KAAK,UAAU;AAEzD,QAAM,MAAM,kBACN,gBAAgB,UAAU,GAAG,IAC7B;AAEN,MAA+C,2BAA2B;AAEtE,UAAM,WAAW;AAAA,MACb,WAAW,KAAK,IAAI;AAAA,MACpB,KAAKG,WAAS,GAAG,IACX,MACA,kBAAkBH,OAAM,IACpBA,QAAO,MACP;AAAA,MACV,QAAQ,iBAAiB,kBAAkBA,OAAM,IAC3CA,QAAO,SACP;AAAA,MACN,QAAQG,WAASH,OAAM,IACjBA,UACA,kBAAkBA,OAAM,IACpBA,QAAO,SACP;AAAA,MACV,SAAS;AAAA,IAAA;AAEJ,aAAA,OAAOE,SAAO,CAAC,GAAG,QAAQ,QAAQ,uCAAuB,CAAA,CAAE;AACpE,sBAAkB,QAAQ;AAAA,EAC9B;AACO,SAAA;AACX;AAhHS;AAiHT,SAAS,aAAa,SAAS;AACvB,MAAA,QAAQ,QAAQ,IAAI,GAAG;AACf,YAAA,OAAO,QAAQ,KAAK,IAAI,CAAA,SAAQC,WAAS,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI;AAAA,EAE3E,WAAAF,WAAS,QAAQ,KAAK,GAAG;AAC9B,WAAO,KAAK,QAAQ,KAAK,EAAE,QAAQ,CAAO,QAAA;AACtC,UAAIE,WAAS,QAAQ,MAAM,GAAG,CAAC,GAAG;AAC9B,gBAAQ,MAAM,GAAG,IAAI,WAAW,QAAQ,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IAAA,CACH;AAAA,EACL;AACJ;AAXS;AAYT,SAAS,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,aAAa;AAC3F,QAAM,EAAE,UAAU,QAAQ,iBAAiBoB,eAAc,iBAAqB,IAAA;AAC9E,QAAM,UAAU,iBAAiB,SAAS,gBAAgB,MAAM;AAChE,MAAI,UAAU;AACV,MAAA;AACJ,MAAIvB,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACtB,mBAAA,KAAK,QAAQ,CAAC;AAC7B,QAAK,OAG2C;AACrC,aAAAmB,iBAAe,cAAc,uBAAuB;AAAA,QACvD;AAAA,QACA,QAAQ;AAAA,MACX,CAAA,CAAC;AAAA,IACN;AAEA,QAAK,OAAmE;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAAA,CAC1B;AAAA,MACL;AAAA,IACJ;AAEI,cAAA,SAAS,YAAY,KAAK,OAAO;AAErC,QAAI,QAAQ;AACR,QAAA;AACA,QAAA;AACJ,QAAK,OAAqD;AAC9C,cAAA,OAAO,YAAY;AAChB,iBAAA;AACF,eAAA;AACT,cAAQ,KAAK,QAAQ;AAAA,IACzB;AACA,SAAKnB,UAASuB,cAAa,SAAS,GAAG,OAAO,MAAM;AAEhDvB,gBAAS,QAAQ,GAAG;AAAA,IACxB;AAEA,QAAK,OAAqD;AAChD,YAAA,MAAM,OAAO,YAAY,IAAI;AACnC,YAAM,UAAU,QAAQ;AACpB,UAAA,WAAW,SAASA,SAAQ;AAC5B,gBAAQ,KAAK,mBAAmE;AAAA,UAC5E,MAAM;AAAA,UACN;AAAA,UACA,SAASA;AAAAA,UACT,MAAM,MAAM;AAAA,UACZ,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAAA,CAC1B;AAAA,MACL;AACI,UAAA,YAAY,UAAU,QAAQ,SAAS;AACvC,aAAK,MAAM;AACH,gBAAA,2BAA2B,UAAU,MAAM;AAAA,MACvD;AAAA,IACJ;AACI,QAAAG,WAASH,OAAM,KAAK,aAAaA,OAAM,KAAK,kBAAkBA,OAAM,GAAG;AACvE;AAAA,IACJ;AACA,QAAI,CAAC,mBAAmB,cAAc,OAAO,GAAG;AAC5C,YAAM,aAAa;AAAA,QAAc;AAAA;AAAA,QACjC;AAAA,QAAK;AAAA,QAAc;AAAA,QAAa;AAAA,MAAA;AAChC,UAAI,eAAe,KAAK;AACpBA,kBAAS;AAAA,MACb;AAAA,IACJ;AACO,WAAA;AAAA,EACX;AACO,SAAA,CAACA,SAAQ,cAAc,OAAO;AACzC;AAhFS;AAiFT,SAAS,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,SAAS;AAC/E,QAAA,EAAE,iBAAiB,gBAAoB,IAAA;AACzC,MAAA,kBAAkBA,OAAM,GAAG;AAC3B,UAAMwB,OAAMxB;AACZwB,SAAI,SAASA,KAAI,UAAU;AAC3BA,SAAI,MAAMA,KAAI,OAAO;AACdA,WAAAA;AAAAA,EACX;AACA,MAAI,mBAAmB,MAAM;AACzB,UAAMA,OAAO,6BAAMxB,SAAN;AACbwB,SAAI,SAAS;AACbA,SAAI,MAAM;AACHA,WAAAA;AAAAA,EACX;AAEA,MAAI,QAAQ;AACR,MAAA;AACA,MAAA;AACJ,MAAK,OAAqD;AAC9C,YAAA,OAAO,YAAY;AAChB,eAAA;AACF,aAAA;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACM,QAAA,MAAM,gBAAgBxB,SAAQ,kBAAkB,SAAS,cAAc,cAAcA,SAAQ,iBAAiB,OAAO,CAAC;AAE5H,MAAK,OAAqD;AAChD,UAAA,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,uBAA2E;AAAA,QACpF,MAAM;AAAA,QACN,SAASA;AAAAA,QACT,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,MAAA,CACjC;AAAA,IACL;AACI,QAAA,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACH,cAAA,+BAA+B,UAAU,MAAM;AAAA,IAC3D;AAAA,EACJ;AACA,MAAI,SAAS;AACb,MAAI,MAAM;AACV,MAAI,SAASA;AACN,SAAA;AACX;AA9CS;AA+CT,SAAS,gBAAgB,SAAS,KAAK,QAAQ;AAE3C,MAAI,QAAQ;AACR,MAAA;AACA,MAAA;AACJ,MAAK,OAAqD;AAC9C,YAAA,OAAO,YAAY;AAChB,eAAA;AACF,aAAA;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACM,QAAA,WAAW,IAAI,MAAM;AAE3B,MAAK,OAAqD;AAChD,UAAA,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,sBAAyE;AAAA,QAClF,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,WAAW,IAAI,IAAI,GAAG;AAAA,MAAA,CACrC;AAAA,IACL;AACI,QAAA,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACH,cAAA,8BAA8B,UAAU,MAAM;AAAA,IAC1D;AAAA,EACJ;AACO,SAAA;AACX;AA9BS;AAgCT,SAAS,sBAAsB,MAAM;AACjC,QAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,QAAM,UAAU;AAChB,MAAI,CAACG,WAAS,IAAI,KACd,CAAC,SAAS,IAAI,KACd,CAAC,kBAAkB,IAAI,KACvB,CAAC,aAAa,IAAI,GAAG;AACf,UAAA,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AAEM,QAAA,MAAM,SAAS,IAAI,IACnB,OAAO,IAAI,IACX,kBAAkB,IAAI,IAClB,OACA;AACN,MAAA,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EAAA,WAEZA,WAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EAAA,WAEb,cAAc,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AAClD,YAAQ,QAAQ;AAAA,EAAA,WAEX,QAAQ,IAAI,GAAG;AACpB,YAAQ,OAAO;AAAA,EACnB;AACI,MAAA,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EAAA,WAEZA,WAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EAAA,WAEb,cAAc,IAAI,GAAG;AAC1BD,aAAO,SAAS,IAAI;AAAA,EACxB;AACO,SAAA,CAAC,KAAK,OAAO;AACxB;AArCS;AAsCT,SAAS,kBAAkB,SAAS,QAAQ,KAAK,QAAQ,iBAAiB,SAAS;AACxE,SAAA;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,wBAAC,QAAQ;AACd,iBAAW,QAAQ,GAAG;AACtB,UAAK,OAAwC;AACnC,cAAA,UAAU,sBAAsB,MAAM;AACtC,cAAA,UAAU,8BAA8B,IAAI,OAAO;AACzD,cAAM,YAAY,IAAI,YAClB,WACA,kBAAkB,SAAS,IAAI,SAAS,MAAM,QAAQ,IAAI,SAAS,IAAI,MAAM;AACjF,cAAM,UAAU,QAAQ;AACxB,YAAI,WAAW,SAAS;AACpB,kBAAQ,KAAK,iBAA+D;AAAA,YACxE,SAAS;AAAA,YACT,OAAO,IAAI;AAAA,YACX,OAAO,IAAI,YAAY,IAAI,SAAS,MAAM;AAAA,YAC1C,KAAK,IAAI,YAAY,IAAI,SAAS,IAAI;AAAA,YACtC,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,UAAA,CACjC;AAAA,QACL;AACQ,gBAAA,MAAM,YAAY,GAAG,OAAO;AAAA,EAAK,SAAS,KAAK,OAAO;AAAA,MAAA,OAE7D;AACK,cAAA;AAAA,MACV;AAAA,IACJ,GAvBS;AAAA,IAwBT,YAAY,wBAACuB,YAAW,uBAAuB,QAAQ,KAAKA,OAAM,GAAtD;AAAA,EAAsD;AAE1E;AA/BS;AAgCT,SAAS,sBAAsB,QAAQ;AAC/B,MAAAtB,WAAS,MAAM,GAAG;AACX,WAAA;AAAA,EAAA,OAEN;AACD,QAAI,OAAO,OAAO,OAAO,IAAI,QAAQ;AACjC,aAAO,OAAO,IAAI;AAAA,IACtB;AAAA,EACJ;AACJ;AATS;AAUT,SAAS,yBAAyB,SAAS,QAAQ,SAAS,SAAS;AAC3D,QAAA,EAAE,WAAW,aAAa,iBAAiBoB,eAAc,gBAAgB,cAAc,aAAa,gBAAoB,IAAA;AACxH,QAAA,iBAAiB,wBAAC,QAAQ;AACxB,QAAA,MAAMA,cAAa,SAAS,GAAG;AAE/B,QAAA,OAAO,QAAQ,iBAAiB;AAC1B,YAAA,CAAKG,EAAAA,EAAAA,QAAO,IAAI,qBAAqB,iBAAiB,KAAK,QAAQ,gBAAgB,cAAc,WAAW;AAC5GH,YAAAA,cAAaG,UAAS,GAAG;AAAA,IACnC;AACA,QAAIvB,WAAS,GAAG,KAAK,aAAa,GAAG,GAAG;AACpC,UAAI,WAAW;AACf,YAAM,UAAU,6BAAM;AACP,mBAAA;AAAA,MAAA,GADC;AAGhB,YAAM,MAAM,qBAAqB,SAAS,KAAK,QAAQ,KAAK,KAAK,OAAO;AACjE,aAAA,CAAC,WACF,MACA;AAAA,IAAA,WAED,kBAAkB,GAAG,GAAG;AACtB,aAAA;AAAA,IAAA,OAEN;AAEM,aAAA;AAAA,IACX;AAAA,EAAA,GAvBmB;AAyBvB,QAAM,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EAAA;AAEd,MAAI,QAAQ,WAAW;AACnB,eAAW,YAAY,QAAQ;AAAA,EACnC;AACA,MAAI,QAAQ,MAAM;AACd,eAAW,OAAO,QAAQ;AAAA,EAC9B;AACA,MAAI,QAAQ,OAAO;AACf,eAAW,QAAQ,QAAQ;AAAA,EAC/B;AACI,MAAA,SAAS,QAAQ,MAAM,GAAG;AAC1B,eAAW,cAAc,QAAQ;AAAA,EACrC;AACO,SAAA;AACX;AA9CS;AAgDT,MAAM,cAAc,OAAO,SAAS;AACpC,MAAM,iBAAiB;AAAA,EACnB,gBAAgB,eAAe,OAAO,KAAK,mBAAmB;AAAA,EAC9D,cAAc,eAAe,OAAO,KAAK,iBAAiB;AAC9D;AAGA,SAAS,SAAS,YAAY,MAAM;AAChC,QAAM,EAAE,iBAAiB,aAAa,gBAAgB,QAAQ,iBAAqB,IAAA;AAC7E,QAAA,EAAE,qBAAyB,IAAA;AACjC,MAAK,OAA0E;AACpE,WAAAgB,iBAAe,cAAc,kBAAkB,CAAC;AAChD,WAAA;AAAA,EACX;AACM,QAAA,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,kBAAkB,GAAG,IAAI;AAClE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACR,QAAA,OAAO,CAAC,CAAC,QAAQ;AACjB,QAAA,SAAS,UAAU,SAAS,OAAO;AACzC,QAAM,UAAU;AAAA,IAAiB;AAAA;AAAA,IACjC;AAAA,IAAgB;AAAA,EAAA;AAChB,MAAI,CAAChB,WAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,eAAe,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,EAClE;AAEA,MAAI,iBAAiB,CAAA;AACjB,MAAA;AACJ,MAAIH,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACtB,mBAAA,KAAK,QAAQ,CAAC;AAC7B,QAAK,OAE2C;AACrC,aAAAmB,iBAAe,cAAc,yBAAyB;AAAA,QACzD;AAAA,QACA,QAAQ;AAAA,MACX,CAAA,CAAC;AAAA,IACN;AAEA,QAAK,OAAmE;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAAA,CAC1B;AAAA,MACL;AAAA,IACJ;AAEI,qBAAA,gBAAgB,YAAY,KAAK;AACrCnB,cAAS,eAAe,GAAG;AAC3B,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AACpD,WAAA;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAACG,WAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAC5B,MAAA,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,EAC5C;AACI,MAAA,YAAY,qBAAqB,IAAI,EAAE;AAC3C,MAAI,CAAC,WAAW;AACA,gBAAA,IAAI,KAAK,eAAe,cAAcD,SAAO,IAAIF,SAAQ,SAAS,CAAC;AAC1D,yBAAA,IAAI,IAAI,SAAS;AAAA,EAC1C;AACO,SAAA,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAzES;AA2ET,MAAM,+BAA+B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,qBAAqB,MAAM;AAChC,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,QAAM,UAAU;AAChB,MAAI,YAAY;AACZ,MAAA;AACA,MAAAG,WAAS,IAAI,GAAG;AAGV,UAAA,UAAU,KAAK,MAAM,gCAAgC;AAC3D,QAAI,CAAC,SAAS;AACJ,YAAA,gBAAgB,eAAe,yBAAyB;AAAA,IAClE;AAGA,UAAM,WAAW,QAAQ,CAAC,IACpB,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,IAC5B,GAAG,QAAQ,CAAC,EAAE,KAAA,CAAM,GAAG,QAAQ,CAAC,EAAE,MAAM,KACxC,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,IAAI,QAAQ,CAAC,EAAE,MAAM,KAC7C,QAAQ,CAAC,EAAE;AACT,YAAA,IAAI,KAAK,QAAQ;AACrB,QAAA;AAEA,YAAM,YAAY;AAAA,aAEf,GAAG;AACA,YAAA,gBAAgB,eAAe,yBAAyB;AAAA,IAClE;AAAA,EAAA,WAEK,OAAO,IAAI,GAAG;AACnB,QAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACjB,YAAA,gBAAgB,eAAe,qBAAqB;AAAA,IAC9D;AACQ,YAAA;AAAA,EAAA,WAEH,SAAS,IAAI,GAAG;AACb,YAAA;AAAA,EAAA,OAEP;AACK,UAAA,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AACI,MAAAA,WAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAAA,WAET,cAAc,IAAI,GAAG;AAC1B,WAAO,KAAK,IAAI,EAAE,QAAQ,CAAO,QAAA;AACzB,UAAA,6BAA6B,SAAS,GAAG,GAAG;AAClC,kBAAA,GAAG,IAAI,KAAK,GAAG;AAAA,MAAA,OAExB;AACO,gBAAA,GAAG,IAAI,KAAK,GAAG;AAAA,MAC3B;AAAA,IAAA,CACH;AAAA,EACL;AACI,MAAAA,WAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EAAA,WAEZ,cAAc,IAAI,GAAG;AACd,gBAAA;AAAA,EAChB;AACI,MAAA,cAAc,IAAI,GAAG;AACT,gBAAA;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AA/DS;AAiET,SAAS,oBAAoB,KAAK,QAAQH,SAAQ;AAC9C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,QAAI,CAAC,QAAQ,qBAAqB,IAAI,EAAE,GAAG;AACvC;AAAA,IACJ;AACQ,YAAA,qBAAqB,OAAO,EAAE;AAAA,EAC1C;AACJ;AATS;AAYT,SAAS,OAAO,YAAY,MAAM;AAC9B,QAAM,EAAE,eAAe,aAAa,gBAAgB,QAAQ,iBAAqB,IAAA;AAC3E,QAAA,EAAE,mBAAuB,IAAA;AAC/B,MAAK,OAAwE;AAClE,WAAAmB,iBAAe,cAAc,oBAAoB,CAAC;AAClD,WAAA;AAAA,EACX;AACM,QAAA,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,gBAAgB,GAAG,IAAI;AAChE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACR,QAAA,OAAO,CAAC,CAAC,QAAQ;AACjB,QAAA,SAAS,UAAU,SAAS,OAAO;AACzC,QAAM,UAAU;AAAA,IAAiB;AAAA;AAAA,IACjC;AAAA,IAAgB;AAAA,EAAA;AAChB,MAAI,CAAChB,WAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,aAAa,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,EAChE;AAEA,MAAI,eAAe,CAAA;AACf,MAAA;AACJ,MAAIH,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACtB,mBAAA,KAAK,QAAQ,CAAC;AAC7B,QAAK,OAE2C;AACrC,aAAAmB,iBAAe,cAAc,2BAA2B;AAAA,QAC3D;AAAA,QACA,QAAQ;AAAA,MACX,CAAA,CAAC;AAAA,IACN;AAEA,QAAK,OAAmE;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAAA,CAC1B;AAAA,MACL;AAAA,IACJ;AAEI,mBAAA,cAAc,YAAY,KAAK;AACnCnB,cAAS,aAAa,GAAG;AACzB,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AACpD,WAAA;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAACG,WAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAC5B,MAAA,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,EAC5C;AACI,MAAA,YAAY,mBAAmB,IAAI,EAAE;AACzC,MAAI,CAAC,WAAW;AACA,gBAAA,IAAI,KAAK,aAAa,cAAcD,SAAO,IAAIF,SAAQ,SAAS,CAAC;AAC1D,uBAAA,IAAI,IAAI,SAAS;AAAA,EACxC;AACO,SAAA,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAzES;AA2ET,MAAM,6BAA6B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,mBAAmB,MAAM;AAC9B,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,QAAM,UAAU;AAChB,MAAI,YAAY;AACZ,MAAA,CAAC,SAAS,IAAI,GAAG;AACX,UAAA,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AACA,QAAM,QAAQ;AACV,MAAAG,WAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAAA,WAET,cAAc,IAAI,GAAG;AAC1B,WAAO,KAAK,IAAI,EAAE,QAAQ,CAAO,QAAA;AACzB,UAAA,2BAA2B,SAAS,GAAG,GAAG;AAChC,kBAAA,GAAG,IAAI,KAAK,GAAG;AAAA,MAAA,OAExB;AACO,gBAAA,GAAG,IAAI,KAAK,GAAG;AAAA,MAC3B;AAAA,IAAA,CACH;AAAA,EACL;AACI,MAAAA,WAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EAAA,WAEZ,cAAc,IAAI,GAAG;AACd,gBAAA;AAAA,EAChB;AACI,MAAA,cAAc,IAAI,GAAG;AACT,gBAAA;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AA/BS;AAiCT,SAAS,kBAAkB,KAAK,QAAQH,SAAQ;AAC5C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,QAAI,CAAC,QAAQ,mBAAmB,IAAI,EAAE,GAAG;AACrC;AAAA,IACJ;AACQ,YAAA,mBAAmB,OAAO,EAAE;AAAA,EACxC;AACJ;AATS;AAWT;AACqBe;AACrB;ACl3DA;AAAA;AAAA;AAAA;AAAA;AAkBA,MAAM,UAAU;AAKhB,SAAS,mBAAmB;AACpB,MAAA,OAAO,8BAA8B,WAAW;AAChD,oBAAgB,4BAA4B;AAAA,EAChD;AACI,MAAA,OAAO,4BAA4B,WAAW;AAC9C,oBAAgB,0BAA0B;AAAA,EAC9C;AACI,MAAA,OAAO,gCAAgC,WAAW;AAClD,oBAAgB,8BAA8B;AAAA,EAClD;AACI,MAAA,OAAO,sCAAsC,WAAW;AACxD,oBAAgB,oCAAoC;AAAA,EACxD;AACI,MAAA,OAAO,8BAA8B,WAAW;AAChD,oBAAgB,4BAA4B;AAAA,EAChD;AACJ;AAhBS;AAkBT,MAAM,SAAS,cAAc;AAC7B,MAAM,QAAQ,YAAY,MAAM;AAChC,MAAM,gBAAgB;AAAA,EAClB,kBAAkB;AAAA;AAAA,EAClB,wBAAwB,MAAM;AAAA;AAAA,EAC9B,yBAAyB,MAAM;AAAA;AAAA,EAC/B,kCAAkC,MAAM;AAAA;AAAA,EACxC,gCAAgC,MAAM;AAAA;AAAA,EACtC,kCAAkC,MAAM;AAAA;AAAA,EACxC,wBAAwB,MAAM;AAAA;AAAA,EAC9B,oBAAoB,MAAM;AAAA;AAAA,EAC1B,+BAA+B,MAAM;AAAA;AAAA,EACrC,6CAA6C,MAAM;AAAA;AACvD;AACA,MAAM,eAAe;AAAA,EACjB,CAAC,cAAc,gBAAgB,GAAG;AAAA,EAClC,CAAC,cAAc,sBAAsB,GAAG;AAAA,EACxC,CAAC,cAAc,uBAAuB,GAAG;AAAA,EACzC,CAAC,cAAc,gCAAgC,GAAG;AAAA,EAClD,CAAC,cAAc,8BAA8B,GAAG;AAAA,EAChD,CAAC,cAAc,gCAAgC,GAAG;AAAA,EAClD,CAAC,cAAc,sBAAsB,GAAG;AAAA,EACxC,CAAC,cAAc,kBAAkB,GAAG;AAAA,EACpC,CAAC,cAAc,6BAA6B,GAAG;AAAA,EAC/C,CAAC,cAAc,2CAA2C,GAAG;AACjE;AACA,SAAS,eAAeV,UAAS,MAAM;AACnC,SAAOL,SAAO,aAAaK,KAAI,GAAG,GAAG,IAAI;AAC7C;AAFS;AAIT,MAAM,OAAO,eAAe;AAC5B,MAAM,MAAM,YAAY,IAAI;AAC5B,MAAM,iBAAiB;AAAA;AAAA,EAEnB,wBAAwB;AAAA;AAAA;AAAA,EAExB,kBAAkB,IAAI;AAAA;AAAA;AAAA,EAEtB,wBAAwB,IAAI;AAAA;AAAA,EAC5B,eAAe,IAAI;AAAA;AAAA,EACnB,8BAA8B,IAAI;AAAA;AAAA;AAAA,EAElC,gBAAgB,IAAI;AAAA;AAAA,EACpB,eAAe,IAAI;AAAA;AAAA;AAAA,EAEnB,kCAAkC,IAAI;AAAA;AAAA,EACtC,4BAA4B,IAAI;AAAA;AAAA;AAAA,EAEhC,kBAAkB,IAAI;AAAA;AAAA;AAAA,EAEtB,gCAAgC,IAAI;AAAA;AAAA;AAAA,EAEpC,2BAA2B,IAAI;AAAA;AAAA;AAAA,EAE/B,8CAA8C,IAAI;AAAA;AAAA;AAAA,EAElD,qCAAqC,IAAI;AAAA;AAAA;AAAA,EAEzC,kBAAkB,IAAI;AAAA;AAC1B;AACA,SAAS,gBAAgBA,UAAS,MAAM;AAC7B,SAAA,mBAAmBA,OAAM,MAAO,QAAyC,EAAE,UAAU,eAAe,KAAK,IAAI,MAAS;AACjI;AAFS;AAGT,MAAM,gBAAgB;AAAA,EAClB,CAAC,eAAe,sBAAsB,GAAG;AAAA,EACzC,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,sBAAsB,GAAG;AAAA,EACzC,CAAC,eAAe,aAAa,GAAG;AAAA,EAChC,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,4BAA4B,GAAG;AAAA,EAC/C,CAAC,eAAe,cAAc,GAAG;AAAA,EACjC,CAAC,eAAe,aAAa,GAAG;AAAA,EAChC,CAAC,eAAe,gCAAgC,GAAG;AAAA,EACnD,CAAC,eAAe,0BAA0B,GAAG;AAAA,EAC7C,CAAC,eAAe,8BAA8B,GAAG;AAAA,EACjD,CAAC,eAAe,yBAAyB,GAAG;AAAA,EAC5C,CAAC,eAAe,4CAA4C,GAAG;AAAA,EAC/D,CAAC,eAAe,mCAAmC,GAAG;AAC1D;AAEA,MAAM,kDACoB,kBAAkB;AAC5C,MAAM,iDAAgD,iBAAiB;AACvE,MAAM,+CAA8C,eAAe;AACnE,MAAM,2CAA0C,iBAAiB;AACjE,MAAM,4CAA2C,kBAAkB;AACnE,MAAM,uBAAuB,WAAW,kBAAkB;AAC1D,WAAW,eAAe;AAC1B,MAAM,oDACoB,oBAAoB;AAC9C,MAAM,2CAA0C,WAAW;AAC3D,MAAM,sBAAuB;AAM7B,SAAS,eAAe,KAAK;AAErB,MAAA,CAACJ,WAAS,GAAG,GAAG;AACT,WAAA;AAAA,EACX;AACA,aAAW,OAAO,KAAK;AAEnB,QAAI,CAAC,OAAO,KAAK,GAAG,GAAG;AACnB;AAAA,IACJ;AAEA,QAAI,CAAC,IAAI,SAAS,GAAG,GAAG;AAEpB,UAAIA,WAAS,IAAI,GAAG,CAAC,GAAG;AACL,uBAAA,IAAI,GAAG,CAAC;AAAA,MAC3B;AAAA,IAAA,OAGC;AAEK,YAAA,UAAU,IAAI,MAAM,GAAG;AACvB,YAAA,YAAY,QAAQ,SAAS;AACnC,UAAI,aAAa;AACjB,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAC5B,YAAA,QAAQ,CAAC,MAAM,aAAa;AAC5B,gBAAM,IAAI,MAAM,eAAe,QAAQ,CAAC,CAAC,EAAE;AAAA,QAC/C;AACA,YAAI,EAAE,QAAQ,CAAC,KAAK,aAAa;AAC7B,qBAAW,QAAQ,CAAC,CAAC,IAAI,OAAO;AAAA,QACpC;AACA,YAAI,CAACA,WAAS,WAAW,QAAQ,CAAC,CAAC,CAAC,GAAG;AAKlB,2BAAA;AACjB;AAAA,QACJ;AACa,qBAAA,WAAW,QAAQ,CAAC,CAAC;AAAA,MACtC;AAEA,UAAI,CAAC,gBAAgB;AACjB,mBAAW,QAAQ,SAAS,CAAC,IAAI,IAAI,GAAG;AACxC,eAAO,IAAI,GAAG;AAAA,MAClB;AAEA,UAAIA,WAAS,WAAW,QAAQ,SAAS,CAAC,CAAC,GAAG;AAC1C,uBAAe,WAAW,QAAQ,SAAS,CAAC,CAAC;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AArDS;AAsDT,SAAS,kBAAkB,QAAQ,SAAS;AACxC,QAAM,EAAE,UAAU,QAAQ,iBAAiB,aAAa;AAExD,QAAM,MAAO,cAAc,QAAQ,IAC7B,WACA,QAAQ,MAAM,IACV,OAAA,IACA,EAAE,CAAC,MAAM,GAAG,OAAS,EAAA;AAE3B,MAAA,QAAQ,MAAM,GAAG;AACjB,WAAO,QAAQ,CAAU,WAAA;AACjB,UAAA,YAAY,UAAU,cAAc,QAAQ;AAC5C,cAAM,EAAE,QAAA0B,SAAQ,SAAA,IAAa;AAC7B,YAAIA,SAAQ;AACR,cAAIA,OAAM,IAAI,IAAIA,OAAM,KAAK,OAAO;AAC3B,mBAAA,UAAU,IAAIA,OAAM,CAAC;AAAA,QAAA,OAE7B;AACD,mBAAS,UAAU,GAAG;AAAA,QAC1B;AAAA,MAAA,OAEC;AACDxB,mBAAS,MAAM,KAAK,SAAS,KAAK,MAAM,MAAM,GAAG,GAAG;AAAA,MACxD;AAAA,IAAA,CACH;AAAA,EACL;AAEI,MAAA,mBAAmB,QAAQ,UAAU;AACrC,eAAW,OAAO,KAAK;AACf,UAAA,OAAO,KAAK,GAAG,GAAG;AACH,uBAAA,IAAI,GAAG,CAAC;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AAnCS;AAqCT,SAAS,oBAAoB,UAAU;AACnC,SAAO,SAAS;AACpB;AAFS;AAGT,SAAS,oBAAoB,IAAI,SAAS,kBACxC;AACE,MAAI,WAAWF,WAAS,QAAQ,QAAQ,IAClC,QAAQ,WACR;AACN,MAAI,kBAAkB,kBAAkB;AACzB,eAAA,kBAAkB,GAAG,OAAO,OAAO;AAAA,MAC1C;AAAA,MACA,QAAQ,iBAAiB;AAAA,IAAA,CAC5B;AAAA,EACL;AAEM,QAAA,UAAU,OAAO,KAAK,QAAQ;AACpC,MAAI,QAAQ,QAAQ;AAChB,YAAQ,QAAQ,CAAU,WAAA;AACtB,SAAG,mBAAmB,QAAQ,SAAS,MAAM,CAAC;AAAA,IAAA,CACjD;AAAA,EACL;AACA;AAEQ,QAAAA,WAAS,QAAQ,eAAe,GAAG;AACnC,YAAM2B,WAAU,OAAO,KAAK,QAAQ,eAAe;AACnD,UAAIA,SAAQ,QAAQ;AAChBA,iBAAQ,QAAQ,CAAU,WAAA;AACtB,aAAG,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC;AAAA,QAAA,CACjE;AAAA,MACL;AAAA,IACJ;AAEI,QAAA3B,WAAS,QAAQ,aAAa,GAAG;AACjC,YAAM2B,WAAU,OAAO,KAAK,QAAQ,aAAa;AACjD,UAAIA,SAAQ,QAAQ;AAChBA,iBAAQ,QAAQ,CAAU,WAAA;AACtB,aAAG,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC;AAAA,QAAA,CAC7D;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AAtCS;AAuCT,SAAS,eAAe,KAAK;AACzB,SAAO,YAAY,MAAM,MAAM,KAAK,CAAC;AAEzC;AAHS;AAQT,MAAM,gBAAgB;AACtB,MAAM,oBAAoB,6BAAM,CAAA,GAAN;AAC1B,MAAM,oBAAoB,6BAAM,OAAN;AAC1B,IAAI,aAAa;AACjB,SAAS,yBAAyB,SAAS;AACvC,SAAQ,CAAC,KAAK,QAAQ,KAAK,SAAS;AAChC,WAAO,QAAQ,QAAQ,KAAK,mBAAmB,KAAK,QAAW,IAAI;AAAA,EAAA;AAE3E;AAJS;AAOT,MAAM,cAAc,wDAAM;AACtB,QAAM,WAAW;AACjB,MAAI,OAAO;AACX,SAAO,aAAa,OAAO,oBAAoB,QAAQ,EAAE,aAAa,KAChE,EAAE,CAAC,aAAa,GAAG,KAAA,IACnB;AACV,GANoB;AAapB,SAAS,eAAe,UAAU,CAAC,GAAG,eAAe;AAC3C,QAAA,EAAE,QAAQ,mBAAuB,IAAA;AACvC,QAAM,YAAY,WAAW;AAC7B,QAAM,WAAW,QAAQ;AACnB,QAAA,OAAO,YAAY,MAAM;AACzB,QAAA,2BAA2B,CAAC,CAAC,QAAQ;AAC3C,MAAK,OAAwC;AACzC,QAAI,4BAA4B,MAAQ;AAC3B,eAAA,eAAe,cAAc,2CAA2C,CAAC;AAAA,IACtF;AAAA,EACJ;AACA,MAAI,iBAAiB,UAAU,QAAQ,aAAa,IAC9C,QAAQ,gBACR;AACN,QAAM,UAAU;AAAA;AAAA,IAEhB,UAAU,iBACJ,OAAO,OAAO,QACdzB,WAAS,QAAQ,MAAM,IACnB,QAAQ,SACR;AAAA,EAAA;AACV,QAAM,kBAAkB;AAAA;AAAA,IAExB,UAAU,iBACJ,OAAO,eAAe,QACtBA,WAAS,QAAQ,cAAc,KAC7B,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,EAAA;AAClB,QAAM,YAAY,KAAK,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAEhE,QAAM,mBAAmB,KAAK,cAAc,QAAQ,eAAe,IACzD,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAA,EAAI,CAAA;AAGjC,QAAM,iBAAiB,KAAK,cAAc,QAAQ,aAAa,IACrD,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAA,EAAI,CAAA;AAIjC,MAAI,eAAe,SACb,OAAO,cACP,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC1D,QAAQ,cACR;AAEV,MAAI,gBAAgB,SACd,OAAO,eACP,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC5D,QAAQ,eACR;AAEN,MAAA,gBAAgB,SACd,OAAO,eACP,UAAU,QAAQ,YAAY,IAC1B,QAAQ,eACR;AAEN,MAAA,kBAAkB,CAAC,CAAC,QAAQ;AAEhC,MAAI,WAAW,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC3D,MAAA,kBAAkB,WAAW,QAAQ,OAAO,IAC1C,yBAAyB,QAAQ,OAAO,IACxC;AAEN,MAAI,mBAAmB,WAAW,QAAQ,eAAe,IACnD,QAAQ,kBACR;AAEF,MAAA,mBAAmB,SACjB,OAAO,kBACP,UAAU,QAAQ,eAAe,IAC7B,QAAQ,kBACR;AACN,MAAA,mBAAmB,CAAC,CAAC,QAAQ;AAG3B,QAAA,aAAa,SACb,OAAO,YACP,cAAc,QAAQ,SAAS,IAC3B,QAAQ,YACR,CAAA;AAEV,MAAI,eAAe,QAAQ,eAAgB,UAAU,OAAO;AAGxD,MAAA;AACJ,QAAM,iBAAiB,6BAAM;AACzB,iBAAa,mBAAmB,IAAI;AACpC,UAAM,aAAa;AAAA,MACf,SAAS;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,gBAAgB,gBAAgB;AAAA,MAChC,UAAU,UAAU;AAAA,MACpB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,SAAS,oBAAoB,OAAO,SAAY;AAAA,MAChD,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB,qBAAqB,OAAO,SAAY;AAAA,MACzD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB,QAAQ;AAAA,MACzB,iBAAiB,QAAQ;AAAA,MACzB,QAAQ,EAAE,WAAW,MAAM;AAAA,IAAA;AAE/B;AACI,iBAAW,kBAAkB,iBAAiB;AAC9C,iBAAW,gBAAgB,eAAe;AAC1C,iBAAW,uBAAuB,cAAc,QAAQ,IAClD,SAAS,uBACT;AACN,iBAAW,qBAAqB,cAAc,QAAQ,IAChD,SAAS,qBACT;AAAA,IACV;AACA,QAAK,OAAwC;AACzC,iBAAW,cAAc,cAAc,QAAQ,IACzC,SAAS,cACT;AAAA,IACV;AACM,UAAA,MAAM,kBAAkB,UAAU;AACxC,iBAAa,mBAAmB,GAAG;AAC5B,WAAA;AAAA,EAAA,GAtCY;AAwCvB,aAAW,eAAe;AAC1B,uBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAEnE,WAAS,wBAAwB;AACtB,WAAA;AAAA,MACC,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,eAAe;AAAA,IAAA;AAAA,EAG3B;AATS;AAWT,QAAM,SAAS,SAAS;AAAA,IACpB,KAAK,6BAAM,QAAQ,OAAd;AAAA,IACL,KAAK,wBAAO,QAAA;AACR,cAAQ,QAAQ;AAChB,eAAS,SAAS,QAAQ;AAAA,IAC9B,GAHK;AAAA,EAGL,CACH;AAED,QAAM,iBAAiB,SAAS;AAAA,IAC5B,KAAK,6BAAM,gBAAgB,OAAtB;AAAA,IACL,KAAK,wBAAO,QAAA;AACR,sBAAgB,QAAQ;AACxB,eAAS,iBAAiB,gBAAgB;AACrB,2BAAA,UAAU,QAAQ,OAAO,GAAG;AAAA,IACrD,GAJK;AAAA,EAIL,CACH;AAED,QAAM,WAAW,SAAS,MAAM,UAAU,KAAK;AAE/C,QAAM,kBAAiC,yBAAS,MAAM,iBAAiB,KAAK;AAE5E,QAAM,gBAA+B,yBAAS,MAAM,eAAe,KAAK;AAExE,WAAS,4BAA4B;AAC1B,WAAA,WAAW,gBAAgB,IAAI,mBAAmB;AAAA,EAC7D;AAFS;AAIT,WAAS,0BAA0B,SAAS;AACrB,uBAAA;AACnB,aAAS,kBAAkB;AAAA,EAC/B;AAHS;AAKT,WAAS,oBAAoB;AAClB,WAAA;AAAA,EACX;AAFS;AAIT,WAAS,kBAAkB,SAAS;AAChC,QAAI,YAAY,MAAM;AAClB,wBAAkB,yBAAyB,OAAO;AAAA,IACtD;AACW,eAAA;AACX,aAAS,UAAU;AAAA,EACvB;AANS;AAOA,WAAA,2BAA2B,MAAM,KACxC;AACS,WAAA,SAAS,eAAe,CAAC,IAAI;AAAA,EACxC;AAHS;AAIT,QAAM,eAAe,wBAAC,IAAI,gBAAgB,UAAU,iBAAiB,cAAc,qBAAqB;AAC9E;AAElB,QAAA;AACA,QAAA;AACA,UAA+C,2BAA2B;AACtE,uEAA+B;AAAA,MACnC;AACA,UAAI,CAAC,WAAW;AACH,iBAAA,kBAAkB,SACrB,mBACA,IAAA;AAAA,MACV;AACA,YAAM,GAAG,QAAQ;AAAA,IAAA,UAErB;AACI,UAA+C,2BAA2B;AACtE,0CAAkB,IAAI;AAAA,MAC1B;AACA,UAAI,CAAC,WAAW;AACZ,iBAAS,kBAAkB;AAAA,MAC/B;AAAA,IACJ;AACA,QAAK,aAAa;AAAA,IACd,SAAS,GAAG,KACZ,QAAQ,gBACP,aAAa,sBAAsB,CAAC,KACvC;AACE,YAAM,CAAC,KAAK,IAAI,IAAI,eAAe;AACnC,UAAK,OAG2C;AACxC,YAAA,kBACC,wBAAwB,eAAe,GAAG,KACvC,uBAAuB,cAAc,GAAG,IAAI;AAC3C,eAAA,eAAe,cAAc,kBAAkB;AAAA,YAChD;AAAA,YACA,MAAM;AAAA,UACT,CAAA,CAAC;AAAA,QACN;AAEA,YAAK,OAAwC;AACnC,gBAAA,EAAE,aAAa,QAAY,IAAA;AACjC,cAAI,WAAW,eAAe;AAC1B,oBAAQ,KAAK,YAAoD;AAAA,cAC7D,MAAM;AAAA,cACN;AAAA,cACA,IAAI;AAAA,cACJ,SAAS,GAAG,QAAQ,IAAI,GAAG;AAAA,YAAA,CAC9B;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,UAAU,gBACX,gBAAgB,MAAM,IACtB,aAAa,GAAG;AAAA,IAAA,WAEjB,iBAAiB,GAAG,GAAG;AACrB,aAAA;AAAA,IAAA,OAEN;AAEK,YAAA,gBAAgB,eAAe,sBAAsB;AAAA,IAC/D;AAAA,EAAA,GAhEiB;AAmErB,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,CAAA,YAAW,QAAQ,MAAM,WAAW,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,mBAAmB,GAAG,IAAI,GAAG,aAAa,CAAA,SAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAO,QAAA,KAAK,CAAO,QAAAA,WAAS,GAAG,CAAC;AAAA,EACvN;AAFS;AAIT,WAAS,MAAM,MAAM;AACjB,UAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,QAAI,QAAQ,CAACF,WAAS,IAAI,GAAG;AACnB,YAAA,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,WAAO,EAAE,GAAG,CAAC,MAAM,MAAMC,SAAO,EAAE,iBAAiB,KAAA,GAAQ,QAAQ,CAAE,CAAA,CAAC,CAAC;AAAA,EAC3E;AANS;AAQT,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,CAAA,YAAW,QAAQ,MAAM,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,kBAAkB,GAAG,IAAI,GAAG,mBAAmB,CAAA,SAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,uBAAuB,CAAO,QAAAC,WAAS,GAAG,CAAC;AAAA,EAC5O;AAFS;AAIT,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,CAAA,YAAW,QAAQ,MAAM,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,gBAAgB,GAAG,IAAI,GAAG,iBAAiB,CAAA,SAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,uBAAuB,CAAO,QAAAA,WAAS,GAAG,CAAC;AAAA,EACtO;AAFS;AAIT,WAAS,UAAU,QAAQ;AACvB,WAAO,OAAO,IAAI,CAAA,QAAOA,WAAS,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,GAAG,IAClE,eAAe,OAAO,GAAG,CAAC,IAC1B,GAAG;AAAA,EACb;AAJS;AAKH,QAAA,cAAc,wBAAC,QAAQ,KAAT;AACpB,QAAM,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EAAA;AAGV,WAAS,kBAAkB,MAAM;AACtB,WAAA;AAAA,MAAa,CAAW,YAAA;AACvB,YAAA;AACJ,cAAM0B,YAAW;AACb,YAAA;AACAA,oBAAS,YAAY;AACf,gBAAA,QAAQ,MAAM,WAAW,MAAM,CAACA,WAAU,GAAG,IAAI,CAAC;AAAA,QAAA,UAE5D;AACIA,oBAAS,YAAY;AAAA,QACzB;AACO,eAAA;AAAA,MACX;AAAA,MAAG,MAAM,mBAAmB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEtC,CAAQ,SAAA,KAAK,oBAAoB,EAAE,GAAG,IAAI;AAAA,MAAG,CAAO,QAAA,CAAC,eAAe,GAAG,CAAC;AAAA,MAAG,CAAA,QAAO,QAAQ,GAAG;AAAA,IAAA;AAAA,EACjG;AAfS;AAiBT,WAAS,eAAe,MAAM;AACnB,WAAA;AAAA,MAAa,CAAA,YAAW,QAAQ,MAAM,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,MAAG,MAAM,gBAAgB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEhH,CAAQ,SAAA,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,MAAG;AAAA,MAAmB,CAAO,QAAA1B,WAAS,GAAG,KAAK,QAAQ,GAAG;AAAA,IAAA;AAAA,EACpG;AAJS;AAMT,WAAS,iBAAiB,MAAM;AACrB,WAAA;AAAA,MAAa,CAAA,YAAW,QAAQ,MAAM,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,MAAG,MAAM,kBAAkB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEpH,CAAQ,SAAA,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,MAAG;AAAA,MAAmB,CAAO,QAAAA,WAAS,GAAG,KAAK,QAAQ,GAAG;AAAA,IAAA;AAAA,EACtG;AAJS;AAKT,WAAS,eAAe,OAAO;AACZ,mBAAA;AACf,aAAS,cAAc;AAAA,EAC3B;AAHS;AAKA,WAAA,GAAG,KAAKwB,SAAQ;AACrB,WAAO,aAAa,MAAM;AACtB,UAAI,CAAC,KAAK;AACC,eAAA;AAAA,MACX;AACA,YAAM,eAAexB,WAASwB,OAAM,IAAIA,UAAS,QAAQ;AACnD,YAAA,UAAU,iBAAiB,YAAY;AAC7C,YAAM,WAAW,SAAS,gBAAgB,SAAS,GAAG;AAC/C,aAAA,CAAC,2BACF,aAAa,QAAQ,KACnB,kBAAkB,QAAQ,KAC1BxB,WAAS,QAAQ,IACnB,YAAY;AAAA,OACnB,MAAM,CAAC,GAAG,GAAG,oBAAoB,CAAQ,SAAA;AACjC,aAAA,QAAQ,MAAM,KAAK,IAAI,MAAM,CAAC,KAAKwB,OAAM,CAAC;AAAA,IAClD,GAAA,mBAAmB,CAAO,QAAA,UAAU,GAAG,CAAC;AAAA,EAC/C;AAhBS;AAiBT,WAAS,gBAAgB,KAAK;AAC1B,QAAIG,YAAW;AACf,UAAM,UAAU,wBAAwB,UAAU,gBAAgB,OAAO,QAAQ,KAAK;AACtF,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAM,uBAAuB,UAAU,MAAM,QAAQ,CAAC,CAAC,KAAK;AAC5D,YAAM,eAAe,SAAS,gBAAgB,sBAAsB,GAAG;AACvE,UAAI,gBAAgB,MAAM;AACtBA,oBAAW;AACX;AAAA,MACJ;AAAA,IACJ;AACOA,WAAAA;AAAAA,EACX;AAZS;AAcT,WAAS,GAAG,KAAK;AACPA,UAAAA,YAAW,gBAAgB,GAAG;AAE7BA,WAAAA,aAAY,OACbA,YACA,SACI,OAAO,GAAG,GAAG,KAAK,CAAC,IACnB;EACd;AARS;AAUT,WAAS,iBAAiBH,SAAQ;AAC9B,WAAQ,UAAU,MAAMA,OAAM,KAAK,CAAA;AAAA,EACvC;AAFS;AAIA,WAAA,iBAAiBA,SAAQ,SAAS;AACvC,QAAI,UAAU;AACV,YAAM,WAAW,EAAE,CAACA,OAAM,GAAG,QAAQ;AACrC,iBAAW,OAAO,UAAU;AACpB,YAAA,OAAO,UAAU,GAAG,GAAG;AACR,yBAAA,SAAS,GAAG,CAAC;AAAA,QAChC;AAAA,MACJ;AACA,gBAAU,SAASA,OAAM;AAAA,IAC7B;AACU,cAAA,MAAMA,OAAM,IAAI;AAC1B,aAAS,WAAW,UAAU;AAAA,EAClC;AAZS;AAcA,WAAA,mBAAmBA,SAAQ,SAAS;AACzC,cAAU,MAAMA,OAAM,IAAI,UAAU,MAAMA,OAAM,KAAK;AACrD,UAAM,WAAW,EAAE,CAACA,OAAM,GAAG,QAAQ;AACrC,QAAI,UAAU;AACV,iBAAW,OAAO,UAAU;AACpB,YAAA,OAAO,UAAU,GAAG,GAAG;AACR,yBAAA,SAAS,GAAG,CAAC;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AACA,cAAU,SAASA,OAAM;AACzB,aAAS,SAAS,UAAU,MAAMA,OAAM,CAAC;AACzC,aAAS,WAAW,UAAU;AAAA,EAClC;AAbS;AAeT,WAAS,kBAAkBA,SAAQ;AAC/B,WAAO,iBAAiB,MAAMA,OAAM,KAAK,CAAA;AAAA,EAC7C;AAFS;AAIA,WAAA,kBAAkBA,SAAQ3B,SAAQ;AACtB,qBAAA,MAAM2B,OAAM,IAAI3B;AACjC,aAAS,kBAAkB,iBAAiB;AACxB,wBAAA,UAAU2B,SAAQ3B,OAAM;AAAA,EAChD;AAJS;AAMA,WAAA,oBAAoB2B,SAAQ3B,SAAQ;AACxB,qBAAA,MAAM2B,OAAM,IAAIzB,SAAO,iBAAiB,MAAMyB,OAAM,KAAK,IAAI3B,OAAM;AACpF,aAAS,kBAAkB,iBAAiB;AACxB,wBAAA,UAAU2B,SAAQ3B,OAAM;AAAA,EAChD;AAJS;AAMT,WAAS,gBAAgB2B,SAAQ;AAC7B,WAAO,eAAe,MAAMA,OAAM,KAAK,CAAA;AAAA,EAC3C;AAFS;AAIA,WAAA,gBAAgBA,SAAQ3B,SAAQ;AACtB,mBAAA,MAAM2B,OAAM,IAAI3B;AAC/B,aAAS,gBAAgB,eAAe;AACtB,sBAAA,UAAU2B,SAAQ3B,OAAM;AAAA,EAC9C;AAJS;AAMA,WAAA,kBAAkB2B,SAAQ3B,SAAQ;AACxB,mBAAA,MAAM2B,OAAM,IAAIzB,SAAO,eAAe,MAAMyB,OAAM,KAAK,IAAI3B,OAAM;AAChF,aAAS,gBAAgB,eAAe;AACtB,sBAAA,UAAU2B,SAAQ3B,OAAM;AAAA,EAC9C;AAJS;AAMT;AAEA,MAAI,UAAU,WAAW;AACf,UAAA,OAAO,QAAQ,CAAC,QAAQ;AAC1B,UAAI,gBAAgB;AAChB,gBAAQ,QAAQ;AAChB,iBAAS,SAAS;AAClB,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IAAA,CACH;AACK,UAAA,OAAO,gBAAgB,CAAC,QAAQ;AAClC,UAAI,gBAAgB;AAChB,wBAAgB,QAAQ;AACxB,iBAAS,iBAAiB;AAC1B,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IAAA,CACH;AAAA,EACL;AAEA,QAAM,WAAW;AAAA,IACb,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA,IAAI,gBAAgB;AACT,aAAA;AAAA,IACX;AAAA,IACA,IAAI,cAAc,KAAK;AACF,uBAAA;AACjB,UAAI,OAAO,QAAQ;AACP,gBAAA,QAAQ,OAAO,OAAO;AACd,wBAAA,QAAQ,OAAO,eAAe;AAC9C,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IACJ;AAAA,IACA,IAAI,mBAAmB;AACnB,aAAO,OAAO,KAAK,UAAU,KAAK,EAAE,KAAK;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,IAAI,YAAY;AACL,aAAA;AAAA,IACX;AAAA,IACA,IAAI,cAAc;AACd,aAAO,gBAAgB,CAAA;AAAA,IAC3B;AAAA,IACA,IAAI,WAAW;AACJ,aAAA;AAAA,IACX;AAAA,IACA,IAAI,cAAc;AACP,aAAA;AAAA,IACX;AAAA,IACA,IAAI,YAAY,KAAK;AACF,qBAAA;AACf,eAAS,cAAc;AAAA,IAC3B;AAAA,IACA,IAAI,eAAe;AACR,aAAA;AAAA,IACX;AAAA,IACA,IAAI,aAAa,KAAK;AACF,sBAAA;AAChB,eAAS,eAAe;AAAA,IAC5B;AAAA,IACA,IAAI,eAAe;AACR,aAAA;AAAA,IACX;AAAA,IACA,IAAI,aAAa,KAAK;AACF,sBAAA;AAAA,IACpB;AAAA,IACA,IAAI,iBAAiB;AACV,aAAA;AAAA,IACX;AAAA,IACA,IAAI,eAAe,KAAK;AACF,wBAAA;AAClB,eAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,IAAI,kBAAkB;AACX,aAAA;AAAA,IACX;AAAA,IACA,IAAI,gBAAgB,KAAK;AACF,yBAAA;AACnB,eAAS,kBAAkB;AAAA,IAC/B;AAAA,IACA,IAAI,kBAAkB;AACX,aAAA;AAAA,IACX;AAAA,IACA,IAAI,gBAAgB,KAAK;AACF,yBAAA;AACnB,eAAS,kBAAkB;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,oBAAoB,GAAG;AAAA,EAAA;AAE5B;AACI,aAAS,kBAAkB;AAC3B,aAAS,gBAAgB;AACzB,aAAS,KAAK;AACd,aAAS,KAAK;AACd,aAAS,KAAK;AACd,aAAS,IAAI;AACb,aAAS,IAAI;AACb,aAAS,oBAAoB;AAC7B,aAAS,oBAAoB;AAC7B,aAAS,sBAAsB;AAC/B,aAAS,kBAAkB;AAC3B,aAAS,kBAAkB;AAC3B,aAAS,oBAAoB;AAC7B,aAAS,sBAAsB,IAAI;AACnC,aAAS,oBAAoB,IAAI;AACjC,aAAS,mBAAmB,IAAI;AAChC,aAAS,iBAAiB,IAAI;AAAA,EAClC;AAEA,MAAK,OAAwC;AAChC,aAAA,aAAa,IAAI,CAAC,YAAY;AACnC,eAAS,cAAc;AAAA,IAAA;AAElB,aAAA,cAAc,IAAI,MAAM;AAC7B,eAAS,cAAc;AAAA,IAAA;AAAA,EAE/B;AACO,SAAA;AACX;AA7iBS;AAsjBT,SAAS,uBAAuB,SAAS;AACrC,QAAM,SAASG,WAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAC3D,QAAM,iBAAiBA,WAAS,QAAQ,cAAc,KAClD,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,QAAM,UAAU,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC1D,QAAA,cAAc,UAAU,QAAQ,qBAAqB,KACvD,SAAS,QAAQ,qBAAqB,IACpC,CAAC,QAAQ,wBACT;AACA,QAAA,eAAe,UAAU,QAAQ,kBAAkB,KACrD,SAAS,QAAQ,kBAAkB,IACjC,CAAC,QAAQ,qBACT;AACN,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR;AACA,QAAA,iBAAiB,CAAC,CAAC,QAAQ;AACjC,QAAM,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY;AACzE,QAAM,qBAAqB,QAAQ;AACnC,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,kBAAkBA,WAAS,QAAQ,iBAAiB,IACpD,QAAQ,sBAAsB,QAC9B;AACA,QAAA,kBAAkB,CAAC,CAAC,QAAQ;AAClC,QAAM,gBAAgB,UAAU,QAAQ,IAAI,IAAI,QAAQ,OAAO;AAC/D,MAAK,OAA6D;AACzD,SAAA,eAAe,cAAc,uBAAuB,CAAC;AAAA,EAC9D;AACA,MAAK,OAA4E;AACxE,SAAA,eAAe,cAAc,gCAAgC,CAAC;AAAA,EACvE;AACA,MAAI,WAAW,QAAQ;AACnB,MAAA,cAAc,QAAQ,cAAc,GAAG;AACvC,UAAM,iBAAiB,QAAQ;AACzB,UAAA,UAAU,OAAO,KAAK,cAAc;AAC1C,eAAW,QAAQ,OAAO,CAAC2B,WAAUH,YAAW;AAC5C,YAAM,UAAUG,UAASH,OAAM,MAAMG,UAASH,OAAM,IAAI,CAAA;AACjDzB,eAAA,SAAS,eAAeyB,OAAM,CAAC;AAC/BG,aAAAA;AAAAA,IAAA,GACP,YAAY,CAAA,CAAG;AAAA,EACvB;AACA,QAAM,EAAE,QAAQ,QAAQ,mBAAA,IAAuB;AAC/C,QAAM,kBAAkB,QAAQ;AAChC,QAAM,gBAAgB,QAAQ;AAC9B,QAAM,WAAW,QAAQ;AACzB,QAAM,2BAA2B,QAC5B;AACE,SAAA;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,QAAQ;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAER;AA7ES;AAoFT,SAAS,cAAc,UAAU,CAAC,GAAG,eAAe;AAChD;AACI,UAAM,WAAW,eAAe,uBAAuB,OAAO,CAAC;AACzD,UAAA,EAAE,WAAe,IAAA;AAEvB,UAAM,UAAU;AAAA;AAAA,MAEZ,IAAI,SAAS;AAAA;AAAA,MAEb,IAAI,SAAS;AACT,eAAO,SAAS,OAAO;AAAA,MAC3B;AAAA,MACA,IAAI,OAAO,KAAK;AACZ,iBAAS,OAAO,QAAQ;AAAA,MAC5B;AAAA;AAAA,MAEA,IAAI,iBAAiB;AACjB,eAAO,SAAS,eAAe;AAAA,MACnC;AAAA,MACA,IAAI,eAAe,KAAK;AACpB,iBAAS,eAAe,QAAQ;AAAA,MACpC;AAAA;AAAA,MAEA,IAAI,WAAW;AACX,eAAO,SAAS,SAAS;AAAA,MAC7B;AAAA;AAAA,MAEA,IAAI,kBAAkB;AAClB,eAAO,SAAS,gBAAgB;AAAA,MACpC;AAAA;AAAA,MAEA,IAAI,gBAAgB;AAChB,eAAO,SAAS,cAAc;AAAA,MAClC;AAAA;AAAA,MAEA,IAAI,mBAAmB;AACnB,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA,MAEA,IAAI,YAAY;AAGL,eAAA;AAAA,UACH,cAAc;AACV,mBAAO;UACX;AAAA,QAAA;AAAA,MAER;AAAA,MACA,IAAI,UAAU,KAAK;AAAA,MAEnB;AAAA;AAAA,MAEA,IAAI,UAAU;AACV,eAAO,SAAS;MACpB;AAAA,MACA,IAAI,QAAQ,SAAS;AACjB,iBAAS,kBAAkB,OAAO;AAAA,MACtC;AAAA;AAAA,MAEA,IAAI,wBAAwB;AACxB,eAAO,UAAU,SAAS,WAAW,IAC/B,CAAC,SAAS,cACV,SAAS;AAAA,MACnB;AAAA,MACA,IAAI,sBAAsB,KAAK;AAC3B,iBAAS,cAAc,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,MACnD;AAAA;AAAA,MAEA,IAAI,qBAAqB;AACrB,eAAO,UAAU,SAAS,YAAY,IAChC,CAAC,SAAS,eACV,SAAS;AAAA,MACnB;AAAA,MACA,IAAI,mBAAmB,KAAK;AACxB,iBAAS,eAAe,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,MACpD;AAAA;AAAA,MAEA,IAAI,YAAY;AACZ,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA,MAEA,IAAI,yBAAyB;AACzB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,uBAAuB,KAAK;AAC5B,iBAAS,iBAAiB;AAAA,MAC9B;AAAA;AAAA,MAEA,IAAI,kBAAkB;AAClB,eAAO,SAAS;MACpB;AAAA,MACA,IAAI,gBAAgB,SAAS;AACzB,iBAAS,0BAA0B,OAAO;AAAA,MAC9C;AAAA;AAAA,MAEA,IAAI,OAAO;AACP,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,KAAK,KAAK;AACV,iBAAS,gBAAgB;AAAA,MAC7B;AAAA;AAAA,MAEA,IAAI,oBAAoB;AACb,eAAA,SAAS,kBAAkB,SAAS;AAAA,MAC/C;AAAA,MACA,IAAI,kBAAkB,KAAK;AACvB,iBAAS,kBAAkB,QAAQ;AAAA,MACvC;AAAA;AAAA,MAEA,IAAI,sBAAsB;AACtB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,oBAAoB,KAAK;AACzB,iBAAS,kBAAkB;AAAA,MAC/B;AAAA;AAAA,MAEA,IAAI,2BAA2B;AAGpB,eAAA;AAAA,MACX;AAAA,MACA,IAAI,yBAAyB,KAAK;AAAA,MAGlC;AAAA;AAAA,MAEA,IAAI,qBAAqB;AACd,eAAA,SAAS,eAAe;MACnC;AAAA;AAAA,MAEA,YAAY;AAAA;AAAA,MAEZ,KAAK,MAAM;AACP,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,cAAMC,WAAU,CAAA;AAChB,YAAI,OAAO;AACX,YAAI,QAAQ;AACR,YAAA,CAAC5B,WAAS,IAAI,GAAG;AACX,gBAAA,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,MAAM;AACR,YAAAA,WAAS,IAAI,GAAG;AAChB4B,mBAAQ,SAAS;AAAA,QAAA,WAEZ,QAAQ,IAAI,GAAG;AACb,iBAAA;AAAA,QAAA,WAEF,cAAc,IAAI,GAAG;AAClB,kBAAA;AAAA,QACZ;AACI,YAAA,QAAQ,IAAI,GAAG;AACR,iBAAA;AAAA,QAAA,WAEF,cAAc,IAAI,GAAG;AAClB,kBAAA;AAAA,QACZ;AAEA,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,UACvC;AAAA,UACC,QAAQ,SAAS,CAAC;AAAA,UACnBA;AAAAA,QAAA,CACH;AAAA,MACL;AAAA,MACA,MAAM,MAAM;AACD,eAAA,QAAQ,MAAM,SAAS,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACzD;AAAA;AAAA,MAEA,MAAM,MAAM;AACR,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AACrBA,cAAAA,WAAU,EAAE,QAAQ;AAC1B,YAAI,OAAO;AACX,YAAI,QAAQ;AACR,YAAA,CAAC5B,WAAS,IAAI,GAAG;AACX,gBAAA,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,MAAM;AACR,YAAAA,WAAS,IAAI,GAAG;AAChB4B,mBAAQ,SAAS;AAAA,QAAA,WAEZ,SAAS,IAAI,GAAG;AACrBA,mBAAQ,SAAS;AAAA,QAAA,WAEZ,QAAQ,IAAI,GAAG;AACb,iBAAA;AAAA,QAAA,WAEF,cAAc,IAAI,GAAG;AAClB,kBAAA;AAAA,QACZ;AACI,YAAA5B,WAAS,IAAI,GAAG;AAChB4B,mBAAQ,SAAS;AAAA,QAAA,WAEZ,QAAQ,IAAI,GAAG;AACb,iBAAA;AAAA,QAAA,WAEF,cAAc,IAAI,GAAG;AAClB,kBAAA;AAAA,QACZ;AAEA,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,UACvC;AAAA,UACC,QAAQ,SAAS,CAAC;AAAA,UACnBA;AAAAA,QAAA,CACH;AAAA,MACL;AAAA;AAAA,MAEA,GAAG,KAAK,QAAQ;AACL,eAAA,SAAS,GAAG,KAAK,MAAM;AAAA,MAClC;AAAA;AAAA,MAEA,GAAG,KAAK;AACG,eAAA,SAAS,GAAG,GAAG;AAAA,MAC1B;AAAA;AAAA,MAEA,iBAAiB,QAAQ;AACd,eAAA,SAAS,iBAAiB,MAAM;AAAA,MAC3C;AAAA;AAAA,MAEA,iBAAiB,QAAQ,SAAS;AACrB,iBAAA,iBAAiB,QAAQ,OAAO;AAAA,MAC7C;AAAA;AAAA,MAEA,mBAAmB,QAAQ,SAAS;AACvB,iBAAA,mBAAmB,QAAQ,OAAO;AAAA,MAC/C;AAAA;AAAA,MAEA,KAAK,MAAM;AACA,eAAA,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACxD;AAAA;AAAA,MAEA,kBAAkB,QAAQ;AACf,eAAA,SAAS,kBAAkB,MAAM;AAAA,MAC5C;AAAA;AAAA,MAEA,kBAAkB,QAAQ/B,SAAQ;AACrB,iBAAA,kBAAkB,QAAQA,OAAM;AAAA,MAC7C;AAAA;AAAA,MAEA,oBAAoB,QAAQA,SAAQ;AACvB,iBAAA,oBAAoB,QAAQA,OAAM;AAAA,MAC/C;AAAA;AAAA,MAEA,KAAK,MAAM;AACA,eAAA,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACxD;AAAA;AAAA,MAEA,gBAAgB,QAAQ;AACb,eAAA,SAAS,gBAAgB,MAAM;AAAA,MAC1C;AAAA;AAAA,MAEA,gBAAgB,QAAQA,SAAQ;AACnB,iBAAA,gBAAgB,QAAQA,OAAM;AAAA,MAC3C;AAAA;AAAA,MAEA,kBAAkB,QAAQA,SAAQ;AACrB,iBAAA,kBAAkB,QAAQA,OAAM;AAAA,MAC7C;AAAA;AAAA;AAAA,MAGA,eAAe,QAAQ,eAAe;AAG3B,eAAA;AAAA,MACX;AAAA,IAAA;AAEJ,YAAQ,aAAa;AAErB,QAAK,OAAwC;AACjC,cAAA,kBAAkB,CAAC,YAAY;AACnC,cAAM,aAAa;AACnB,mBAAW,aAAa,KAAK,WAAW,aAAa,EAAE,OAAO;AAAA,MAAA;AAElE,cAAQ,mBAAmB,MAAM;AAC7B,cAAM,aAAa;AACnB,mBAAW,cAAc,KAAK,WAAW,cAAc,EAAE;AAAA,MAAA;AAAA,IAEjE;AACO,WAAA;AAAA,EACX;AACJ;AAtRS;AAyRT,MAAM,kBAAkB;AAAA,EACpB,KAAK;AAAA,IACD,MAAM,CAAC,QAAQ,MAAM;AAAA,EACzB;AAAA,EACA,QAAQ;AAAA,IACJ,MAAM;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACH,MAAM;AAAA;AAAA,IAEN,WAAW,wBAAC,QAAiC,QAAQ,YAAY,QAAQ,UAA9D;AAAA,IACX,SAAS;AAAA;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,EACV;AACJ;AAEA,SAAS,kBAET,EAAE,MAAM,GACR,MAAM;AACF,MAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,WAAW;AAE5C,UAAM,MAAM,MAAM,UAAU,MAAM,YAAY;AAE9C,WAAO,IAAI,OAAO,CAAC,MAAM,YAAY;AAC1B,aAAA;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAI,QAAQ,SAAS,WAAW,QAAQ,WAAW,CAAC,OAAO;AAAA,MAAA;AAAA,IAGnE,GAAG,CAAE,CAAA;AAAA,EAAA,OAEJ;AAED,WAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AACvB,YAAA,OAAO,MAAM,GAAG;AACtB,UAAI,MAAM;AACF,YAAA,GAAG,IAAI;MACf;AACO,aAAA;AAAA,IAAA,GACR,OAAQ,CAAA;AAAA,EACf;AACJ;AA3BS;AA6BT,SAAS,mBAAmB,KAAK;AACtB,SAAA;AACX;AAFS;AAIT,MAAM,kBAAgD,gCAAA;AAAA;AAAA,EAElD,MAAM;AAAA,EACN,OAAOE,SAAO;AAAA,IACV,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA;AAAA,MAErB,WAAW,wBAAC,QAAQ,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,GAApC;AAAA,IACf;AAAA,KACD,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AACZ,UAAA,EAAE,OAAO,MAAU,IAAA;AAEnB,UAAA,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IAAA,CACnB;AACL,WAAO,MAAM;AACH,YAAA,OAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAA,QAAO,QAAQ,GAAG;AACzD,YAAM,UAAU;AAChB,UAAI,MAAM,QAAQ;AACd,gBAAQ,SAAS,MAAM;AAAA,MAC3B;AACI,UAAA,MAAM,WAAW,QAAW;AACpB,gBAAA,SAASC,WAAS,MAAM,MAAM,IAAI,CAAC,MAAM,SAAS,MAAM;AAAA,MACpE;AACM,YAAA,MAAM,kBAAkB,SAAS,IAAI;AAE3C,YAAM,WAAW,KAAK,oBAAoB,EAAE,MAAM,SAAS,KAAK,OAAO;AACvE,YAAM,gBAAgBD,SAAO,OAAO,GAAG,KAAK;AACtC,YAAA,MAAMC,WAAS,MAAM,GAAG,KAAKF,WAAS,MAAM,GAAG,IAC/C,MAAM,MACN,mBAAmB;AAClB,aAAA,EAAE,KAAK,eAAe,QAAQ;AAAA,IAAA;AAAA,EAE7C;AACJ,CAAC;AAsDD,MAAM,cAAc;AACpB,MAAM,QAAQ;AAEd,SAAS,QAAQ,QAAQ;AACrB,SAAO,QAAQ,MAAM,KAAK,CAACE,WAAS,OAAO,CAAC,CAAC;AACjD;AAFS;AAGT,SAAS,gBAAgB,OAAO,SAAS,UAAU,eAAe;AACxD,QAAA,EAAE,OAAO,MAAU,IAAA;AACzB,SAAO,MAAM;AACH,UAAA,UAAU,EAAE,MAAM;AACxB,QAAI,YAAY;AAChB,QAAI,MAAM,QAAQ;AACd,cAAQ,SAAS,MAAM;AAAA,IAC3B;AACI,QAAAA,WAAS,MAAM,MAAM,GAAG;AACxB,cAAQ,MAAM,MAAM;AAAA,IAEf,WAAAF,WAAS,MAAM,MAAM,GAAG;AAE7B,UAAIE,WAAS,MAAM,OAAO,GAAG,GAAG;AAEpB,gBAAA,MAAM,MAAM,OAAO;AAAA,MAC/B;AAEY,kBAAA,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAAC4B,UAAS,SAAS;AAC5D,eAAO,SAAS,SAAS,IAAI,IACvB7B,SAAO,UAAU6B,UAAS,EAAE,CAAC,IAAI,GAAG,MAAM,OAAO,IAAI,EAAA,CAAG,IACxDA;AAAAA,MAAA,GACP,OAAQ,CAAA;AAAA,IACf;AACM,UAAA,QAAQ,cAAc,GAAG,CAAC,MAAM,OAAO,SAAS,SAAS,CAAC;AAC5D,QAAA,WAAW,CAAC,QAAQ,GAAG;AACvB,QAAA,QAAQ,KAAK,GAAG;AAChB,iBAAW,MAAM,IAAI,CAAC,MAAM,UAAU;AAC5B,cAAA,OAAO,MAAM,KAAK,IAAI;AAC5B,cAAM,OAAO,OACP,KAAK,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,OAAO,OAAO,MAAO,CAAA,IAC9C,CAAC,KAAK,KAAK;AACb,YAAA,QAAQ,IAAI,GAAG;AACf,eAAK,CAAC,EAAE,MAAM,GAAG,KAAK,IAAI,IAAI,KAAK;AAAA,QACvC;AACO,eAAA;AAAA,MAAA,CACV;AAAA,IAAA,WAEI5B,WAAS,KAAK,GAAG;AACtB,iBAAW,CAAC,KAAK;AAAA,IACrB;AACA,UAAM,gBAAgBD,SAAO,OAAO,GAAG,KAAK;AACtC,UAAA,MAAMC,WAAS,MAAM,GAAG,KAAKF,WAAS,MAAM,GAAG,IAC/C,MAAM,MACN,mBAAmB;AAClB,WAAA,EAAE,KAAK,eAAe,QAAQ;AAAA,EAAA;AAE7C;AA/CS;AAiDT,MAAM,mBAAiD,gCAAA;AAAA;AAAA,EAEnD,MAAM;AAAA,EACN,OAAOC,SAAO;AAAA,IACV,OAAO;AAAA,MACH,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,KACD,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AACZ,UAAA,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IAAA,CACnB;AACL,WAAO,gBAAgB,OAAO,SAAS,4BAA4B,IAAI;AAAA;AAAA,MAEvE,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,KAAC;AAAA,EACpC;AACJ,CAAC;AAsBD,MAAM,eAAe;AACrB,MAAM,QAAQ;AAEd,MAAM,qBAAoD,gCAAA;AAAA;AAAA,EAEtD,MAAM;AAAA,EACN,OAAOA,SAAO;AAAA,IACV,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,KACD,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AACZ,UAAA,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IAAA,CACnB;AACL,WAAO,gBAAgB,OAAO,SAAS,8BAA8B,IAAI;AAAA;AAAA,MAEzE,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,KAAC;AAAA,EACtC;AACJ,CAAC;AAkBD,MAAM,iBAAiB;AACvB,MAAM,QAAQ;AAEd,SAAS,cAAc,MAAM,UAAU;AACnC,QAAM,eAAe;AACjB,MAAA,KAAK,SAAS,eAAe;AAC7B,WAAQ,aAAa,cAAc,QAAQ,KAAK,KAAK;AAAA,EAAA,OAEpD;AACK,UAAA,UAAU,aAAa,cAAc,QAAQ;AACnD,WAAO,WAAW,OACZ,QAAQ,aACR,KAAK,OAAO;AAAA,EACtB;AACJ;AAXS;AAYT,SAAS,YAAY,MAAM;AACjB,QAAA,WAAW,wBAAC,YAAY;AAC1B,UAAM,EAAE,UAAU,WAAW,MAAA,IAAU;AAEvC,QAAI,CAAC,YAAY,CAAC,SAAS,GAAG;AACpB,YAAA,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,UAAM,WAAW,cAAc,MAAM,SAAS,CAAC;AAC/C,QAAK,OAA8D;AAC1D,WAAA,eAAe,cAAc,sBAAsB,CAAC;AAAA,IAC7D;AACM,UAAA,cAAc,WAAW,KAAK;AAC7B,WAAA;AAAA,MACH,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,WAAW,WAAW,CAAC,CAAC;AAAA,MAChE;AAAA,IAAA;AAAA,EACJ,GAda;AAgBX,QAAA,WAAW,wBAAC,IAAI,YAAY;AAC9B,UAAM,CAAC,aAAa,QAAQ,IAAI,SAAS,OAAO;AAC5C,QAAA,aAAa,KAAK,WAAW,UAAU;AAEvC,SAAG,gBAAgB,MAAM,SAAS,QAAQ,MAAM;AACpC,gBAAA,YAAY,QAAQ,SAAS,aAAa;AAAA,MAAA,CACrD;AAAA,IACL;AACA,OAAG,aAAa;AAChB,OAAG,cAAc;AAAA,EAAA,GATJ;AAWX,QAAA,aAAa,wBAAC,OAAO;AACnB,QAAA,aAAa,GAAG,eAAe;AAC/B,SAAG,cAAc;AACjB,SAAG,gBAAgB;AACnB,aAAO,GAAG;AAAA,IACd;AACA,QAAI,GAAG,YAAY;AACf,SAAG,aAAa;AAChB,aAAO,GAAG;AAAA,IACd;AAAA,EAAA,GATe;AAWnB,QAAM,SAAS,wBAAC,IAAI,EAAE,YAAY;AAC9B,QAAI,GAAG,YAAY;AACf,YAAM,WAAW,GAAG;AACd,YAAA,cAAc,WAAW,KAAK;AACpC,SAAG,cAAc,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,QACjD,GAAG,WAAW,WAAW;AAAA,MAAA,CAC5B;AAAA,IACL;AAAA,EAAA,GAPW;AAST,QAAA,cAAc,wBAAC,YAAY;AAC7B,UAAM,CAAC,WAAW,IAAI,SAAS,OAAO;AACtC,WAAO,EAAE,YAAY;AAAA,EAAA,GAFL;AAIb,SAAA;AAAA,IACH,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd;AAAA,EAAA;AAER;AA1DS;AA2DT,SAAS,WAAW,OAAO;AACnB,MAAAC,WAAS,KAAK,GAAG;AACV,WAAA,EAAE,MAAM;EAAM,WAEhB,cAAc,KAAK,GAAG;AACvB,QAAA,EAAE,UAAU,QAAQ;AACd,YAAA,gBAAgB,eAAe,gBAAgB,MAAM;AAAA,IAC/D;AACO,WAAA;AAAA,EAAA,OAEN;AACK,UAAA,gBAAgB,eAAe,aAAa;AAAA,EACtD;AACJ;AAbS;AAcT,SAAS,WAAW,OAAO;AACvB,QAAM,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAW,IAAA;AAC/C,QAAM,UAAU,CAAA;AACV,QAAA,QAAQ,QAAQ;AAClB,MAAAA,WAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACI,MAAA,SAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACI,MAAA,SAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACO,SAAA,CAAC,MAAM,OAAO,OAAO;AAChC;AAdS;AAgBT,SAAS,MAAM,KAAK,SAAS,SAAS;AAC5B,QAAA,gBAAgB,cAAc,QAAQ,CAAC,CAAC,IACxC,QAAQ,CAAC,IACT;AACA,QAAA,uBAAuB,CAAC,CAAC,cAAc;AAC7C,QAAM,gBAAgB,UAAU,cAAc,aAAa,IACrD,cAAc,gBACd;AACN,MAAK,OAAiF;AAC7E,SAAA,eAAe,cAAc,kCAAkC;AAAA,MAChE,MAAM,YAAY;AAAA,IACrB,CAAA,CAAC;AAAA,EACN;AACA,MAAI,eAAe;AACf,KAAC,CAAC,uBAAuB,YAAY,OAAO,QAAQ,OAAO,EAAE,QAAQ,CAAQ,SAAA,IAAI,UAAU,MAAM,WAAW,CAAC;AAC5G,KAAA,aAAa,MAAM,OAAO,EAAE,QAAQ,UAAQ,IAAI,UAAU,MAAM,YAAY,CAAC;AAC7E,KAAA,eAAe,MAAM,OAAO,EAAE,QAAQ,UAAQ,IAAI,UAAU,MAAM,cAAc,CAAC;AAAA,EACtF;AAEA;AACI,QAAI,UAAU,KAAK,YAAY,IAAI,CAAC;AAAA,EACxC;AACJ;AAtBS;AAwBT,MAAM,oBAAoB;AAAA,EACtB;AAAA,IAAC;AAAA;AAAA,EAAA,GAA6D;AAAA,EAC9D;AAAA,IAAC;AAAA;AAAA,EAAA,GAAsE;AAAA,EACvE;AAAA,IAAC;AAAA;AAAA,EAAA,GAAoD;AACzD;AACA,MAAM,0BAA0B;AAAA,EAC5B;AAAA,IAAC;AAAA;AAAA,EAAA,GAAsE;AAC3E;AACA,MAAM,4BAA4B;AAAA,EAC9B;AAAA,IAAC;AAAA;AAAA,EAAA,GAAoD;AACzD;AAEA,MAAM,2BAA2B;AACjC,IAAI;AACJ,eAAe,eAAe,KAAK,MAAM;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAChC,QAAA;AACoB,0BAAA;AAAA,QAChB,IAAI;AAAA,QACJ,OAAO;AAAA,UAAkB;AAAA;AAAA,QAA0D;AAAA,QACnF,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,qBAAqB,CAAC,wBAAwB;AAAA,QAC9C;AAAA;AAAA,SACD,CAAO,QAAA;AACQ,sBAAA;AACd,YAAI,GAAG,mBAAmB,CAAC,EAAE,mBAAmB,eAAe;AACnC,kCAAA,mBAAmB,UAAU,IAAI;AAAA,QAAA,CAC5D;AACD,YAAI,GAAG,iBAAiB,CAAC,EAAE,mBAAmB,mBAAmB;AAC7D,cAAI,kBAAkB,MAAM,MACxB,kBAAkB,MAAM,GAAG,gBAC3B,cAAc;AACV,gBAAA,KAAK,SAAS,UAAU;AAExB,kBAAI,kBAAkB,MAAM,GAAG,iBAC3B,KAAK,OAAO,YAAY;AACxB,gCAAgB,cAAc,kBAAkB,MAAM,GAAG,YAAY;AAAA,cACzE;AAAA,YAAA,OAEC;AACD,8BAAgB,cAAc,kBAAkB,MAAM,GAAG,YAAY;AAAA,YACzE;AAAA,UACJ;AAAA,QAAA,CACH;AACD,YAAI,aAAa;AAAA,UACb,IAAI;AAAA,UACJ,OAAO;AAAA,YAAkB;AAAA;AAAA,UAAmE;AAAA,UAC5F,MAAM;AAAA,UACN,uBAAuB;AAAA,YAAwB;AAAA;AAAA,UAAmE;AAAA,QAAA,CACrH;AACG,YAAA,GAAG,iBAAiB,CAAW,YAAA;AAC/B,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,0BAAc,SAAS,IAAI;AAAA,UAC/B;AAAA,QAAA,CACH;AACK,cAAA,4BAAY;AACd,YAAA,GAAG,kBAAkB,OAAO,YAAY;AACxC,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,gBAAI,mBAAmB;AACvB,yBAAa,SAAS,IAAI;AACtB,gBAAA,QAAQ,WAAW,UAAU;AAC7B,kBAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,GAAG;AACzB,sBAAM,CAAC,IAAI,IAAI,MAAM,IAAI,sBAAsB,QAAQ,GAAG;AACpD,sBAAA,IAAI,QAAQ,KAAK,IAAI;AAAA,cAC/B;AACA,kBAAI,iBAAiB,MAAM,IAAI,QAAQ,GAAG,CAAC;AAAA,YAAA,OAE1C;AACD,oBAAM,WAAW,qBAAqB,QAAQ,QAAQ,IAAI;AAC9C,0BAAA,IAAI,iBAAiB,QAAQ;AAAA,YAC7C;AAAA,UACJ;AAAA,QAAA,CACH;AACG,YAAA,GAAG,mBAAmB,CAAW,YAAA;AACjC,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,sBAAU,SAAS,IAAI;AAAA,UAC3B;AAAA,QAAA,CACH;AACD,YAAI,iBAAiB;AAAA,UACjB,IAAI;AAAA,UACJ,OAAO;AAAA,YAAkB;AAAA;AAAA,UAAiD;AAAA,UAC1E,OAAO;AAAA,YAA0B;AAAA;AAAA,UAAiD;AAAA,QAAA,CACrF;AACD,gBAAQ,IAAI;AAAA,MAAA,CACf;AAAA,aAEE,GAAG;AACN,cAAQ,MAAM,CAAC;AACf,aAAO,KAAK;AAAA,IAChB;AAAA,EAAA,CACH;AACL;AAlFe;AAoFf,SAAS,kBAAkB,UAAU;AACzB,SAAA,SAAS,KAAK,QAClB,SAAS,KAAK,eACd,SAAS,KAAK,UACd;AACR;AALS;AAMT,SAAS,wBAAwB,UACjC,UAAU,MAAM;AAEZ,QAAM6B,UAAS,KAAK,SAAS,gBACvB,KAAK,SACL,KAAK,OAAO;AAClB,MAAI,YAAY,SAAS,MAAM,MAAM,SAAS,MAAM,GAAG,cAAc;AAEjE,QAAI,SAAS,MAAM,GAAG,iBAAiBA,SAAQ;AAC3C,YAAM,MAAM;AAAA,QACR,OAAO,SAAS,kBAAkB,QAAQ,CAAC;AAAA,QAC3C,WAAW;AAAA,QACX,iBAAiB;AAAA,MAAA;AAEZ,eAAA,KAAK,KAAK,GAAG;AAAA,IAC1B;AAAA,EACJ;AACJ;AAjBS;AAkBT,SAAS,gBAAgB,cAAc,UAAU;AAC7C,QAAM,OAAO;AACb,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS,OAAO;AAAA,EAAA,CAC1B;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS;AAAA,EAAA,CACnB;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS,eAAe;AAAA,EAAA,CAClC;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS;AAAA,EAAA,CACnB;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,sBAAsB,SAAS,SAAS,KAAK;AAAA,EAAA,CACvD;AACD;AACI,iBAAa,MAAM,KAAK;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,gBAAgB;AAAA,IAAA,CACnC;AACD,iBAAa,MAAM,KAAK;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,cAAc;AAAA,IAAA,CACjC;AAAA,EACL;AACJ;AA9CS;AAgDT,SAAS,sBAAsB,UAAU;AACrC,QAAM,QAAQ,CAAA;AACd,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AAC7B,UAAA,IAAI,SAAS,GAAG;AACtB,QAAI,WAAW,CAAC,KAAK,YAAY,GAAG;AAC1B,YAAA,GAAG,IAAI,0BAA0B,CAAC;AAAA,IAAA,WAEnC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,QAAQ;AACzC,YAAA,GAAG,IAAI,EAAE,IAAI;AAAA,IAAA,WAEd/B,WAAS,CAAC,GAAG;AACZ,YAAA,GAAG,IAAI,sBAAsB,CAAC;AAAA,IAAA,OAEnC;AACD,YAAM,GAAG,IAAI;AAAA,IACjB;AAAA,EAAA,CACH;AACM,SAAA;AACX;AAlBS;AAmBT,MAAM,MAAM;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACT;AACA,SAAS,OAAO,GAAG;AACR,SAAA,EAAE,QAAQ,WAAW,UAAU;AAC1C;AAFS;AAGT,SAAS,WAAW,GAAG;AACZ,SAAA,IAAI,CAAC,KAAK;AACrB;AAFS;AAIT,SAAS,0BAA0B,MAAM;AAC/B,QAAA,YAAY,KAAK,SAAS,KAAK,OAAO,KAAK,MAAM,CAAC,OAAO;AACxD,SAAA;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,kBAAkB,SAAS;AAAA,IACxC;AAAA,EAAA;AAER;AARS;AAST,SAAS,cAAc,SAAS,MAAM;AAClC,UAAQ,UAAU,KAAK;AAAA,IACnB,IAAI;AAAA,IACJ,OAAO;AAAA,EAAA,CACV;AAED,QAAM+B,UAAS,KAAK,SAAS,gBACvB,KAAK,SACL,KAAK,OAAO;AAClB,aAAW,CAAC,aAAa,QAAQ,KAAK,KAAK,aAAa;AAEpD,UAAM,WAAW,KAAK,SAAS,gBACzB,WACA,SAAS;AACf,QAAIA,YAAW,UAAU;AACrB;AAAA,IACJ;AACA,YAAQ,UAAU,KAAK;AAAA,MACnB,IAAI,SAAS,GAAG,SAAS;AAAA,MACzB,OAAO,GAAG,kBAAkB,WAAW,CAAC;AAAA,IAAA,CAC3C;AAAA,EACL;AACJ;AAtBS;AAuBT,SAAS,qBAAqB,QAAQ,MAAM;AACxC,MAAI,WAAW;AACf,MAAI,WAAW,UAAU;AACrB,eAAW,CAAC,WAAW,QAAQ,KAAK,KAAK,YAAY,WAAW;AAC5D,UAAI,SAAS,GAAG,SAAS,MAAM,QAAQ;AACxB,mBAAA;AACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACO,SAAA;AACX;AAXS;AAYT,SAAS,cAAc,QAAQ,MAAM;AACjC,MAAI,WAAW,UAAU;AACrB,WAAO,KAAK,SAAS,gBACf,KAAK,SACL,KAAK,OAAO;AAAA,EAAA,OAEjB;AACD,UAAM,WAAW,MAAM,KAAK,KAAK,YAAY,OAAQ,CAAA,EAAE,KAAK,CAAQ,SAAA,KAAK,GAAG,SAAA,MAAe,MAAM;AACjG,QAAI,UAAU;AACV,aAAO,KAAK,SAAS,gBACf,WACA,SAAS;AAAA,IAAA,OAEd;AACM,aAAA;AAAA,IACX;AAAA,EACJ;AACJ;AAjBS;AAkBT,SAAS,aAAa,SAAS,MAE7B;AACE,QAAM,WAAW,cAAc,QAAQ,QAAQ,IAAI;AACnD,MAAI,UAAU;AAGF,YAAA,QAAQ,sBAAsB,QAAQ;AAAA,EAClD;AACO,SAAA;AACX;AAVS;AAWT,SAAS,sBAAsB,UAAU;AACrC,QAAM,QAAQ,CAAA;AACd,QAAM,aAAa;AACnB,QAAM,eAAe;AAAA,IACjB;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS;AAAA,IACpB;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS;AAAA,IACpB;AAAA,EAAA;AAEJ,QAAM,UAAU,IAAI;AACpB,QAAM,qBAAqB;AAC3B,QAAM,uBAAuB;AAAA,IACzB;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,sBAAsB,SAAS,SAAS,KAAK;AAAA,IACxD;AAAA,EAAA;AAEJ,QAAM,kBAAkB,IAAI;AAC5B;AACI,UAAM,sBAAsB;AAC5B,UAAM,wBAAwB;AAAA,MAC1B;AAAA,QACI,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO,SAAS,gBAAgB;AAAA,MACpC;AAAA,IAAA;AAEJ,UAAM,mBAAmB,IAAI;AAC7B,UAAM,oBAAoB;AAC1B,UAAM,sBAAsB;AAAA,MACxB;AAAA,QACI,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO,SAAS,cAAc;AAAA,MAClC;AAAA,IAAA;AAEJ,UAAM,iBAAiB,IAAI;AAAA,EAC/B;AACO,SAAA;AACX;AA/DS;AAgET,SAAS,iBAAiB,OAAO,SAAS;AACtC,MAAI,aAAa;AACT,QAAA;AACA,QAAA,WAAW,aAAa,SAAS;AACjC,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACnB;AACA,gBAAY,iBAAiB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,QACH,OAAO;AAAA,QACP;AAAA,QACA,MAAM,KAAK,IAAI;AAAA,QACf,MAAM,CAAC;AAAA,QACP,MAAM,WAAW,CAAC;AAAA,QAClB,SAAS,UAAU,kBACb,UACA,UAAU,cACR,UAAU,YACR,YACA;AAAA,MACd;AAAA,IAAA,CACH;AAAA,EACL;AACJ;AAxBS;AAyBT,SAAS,UAAU,SAAS,MAAM;AAC9B,QAAM,WAAW,cAAc,QAAQ,QAAQ,IAAI;AACnD,MAAI,UAAU;AACJ,UAAA,CAAC,KAAK,IAAI,QAAQ;AACxB,QAAI,UAAU,YAAY7B,WAAS,QAAQ,MAAM,KAAK,GAAG;AAC5C,eAAA,OAAO,QAAQ,QAAQ,MAAM;AAAA,IAAA,WAEjC,UAAU,qBACdA,WAAS,QAAQ,MAAM,KAAK,KACzB,QAAQ,QAAQ,MAAM,KAAK,KAC3BF,WAAS,QAAQ,MAAM,KAAK,IAAI;AAC3B,eAAA,eAAe,QAAQ,QAAQ,MAAM;AAAA,IAAA,WAEzC,UAAU,mBAAmB,UAAU,QAAQ,MAAM,KAAK,GAAG;AACzD,eAAA,gBAAgB,QAAQ,MAAM;AAAA,IAC3C;AAAA,EACJ;AACJ;AAjBS;AAuBT,SAAS,YAAY,SAAS,UAAU,MAAM;AACnC,SAAA;AAAA,IACH,eAAe;AACX,YAAM,WAAW;AAEjB,UAAI,CAAC,UAAU;AACL,cAAA,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,YAAM,UAAU,KAAK;AACrB,UAAI,QAAQ,MAAM;AACd,cAAM,cAAc,QAAQ;AAC5B,YAAI,QAAQ,QAAQ;AAChB,sBAAY,SAAS,QAAQ;AAAA,QACjC;AACA,oBAAY,SAAS;AACjB,YAAA,SAAS,KAAK,OAAO;AAEhB,eAAA,QAAQ,cAAc,SAAS,WAAW;AAAA,QAAA,OAE9C;AACD,sBAAY,qBAAqB;AACjC,sBAAY,aAAa,KAAK;AAEzB,eAAA,QAAQ,cAAc,WAAW;AAEtC,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,YAAY;AACrB,qBAAS,aAAa,SAAS,WAAW,KAAK,KAAK;AAAA,UACxD;AAAA,QACJ;AAAA,MAAA,WAEK,QAAQ,QAAQ;AACjB,YAAA,SAAS,KAAK,OAAO;AAEhB,eAAA,QAAQ,cAAc,SAAS,OAAO;AAAA,QAAA,OAE1C;AAED,eAAK,QAAQ,cAAc;AAAA,YACvB,QAAQ,QAAQ;AAAA,YAChB,oBAAoB;AAAA,YACpB,YAAY,KAAK;AAAA,YACjB,QAAQ;AAAA,UAAA,CACX;AAED,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,YAAY;AACrB,qBAAS,aAAa,SAAS,WAAW,KAAK,KAAK;AAAA,UACxD;AAAA,QACJ;AAAA,MAAA,OAEC;AAED,aAAK,QAAQ;AAAA,MACjB;AACA,UAAI,QAAQ,cAAc;AACF,4BAAA,UAAU,SAAS,OAAO;AAAA,MAClD;AAEA,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,WAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AACxC,WAAA,MAAM,CAAC,KAAK,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AACrD,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;AAChC,WAAA,cAAc,UAAU,KAAK,KAAK;AAAA,IAC3C;AAAA,IACA,UAAU;AAEN,UAAgD,OAGhC;AACZ,cAAM,WAAW,KAAK;AACjB,aAAA,IAAI,eAAe,SAAS;AAC3B,cAAA,UAAW,KAAK,cAClB,cAAc;AACT,iBAAA,mBAAmB,SAAS,gBAAgB,OAAO;AACpD,gBAAA,GAAG,KAAK,gBAAgB;AAAA,MACpC;AAAA,IACJ;AAAA,IACA,YAAY;AACR,YAAM,WAAW;AAEjB,UAAI,CAAC,UAAU;AACL,cAAA,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,YAAM,WAAW,KAAK;AAEtB,UAAgD,OAGrB;AACvB,YAAI,KAAK,aAAa;AACb,eAAA,YAAY,IAAI,KAAK,gBAAgB;AAC1C,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,KAAK,OAAO;AACH,mBAAA,oBAAoB,SAAS;AACtC,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ;AACA,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,UAAI,SAAS,YAAY;AACrB,iBAAS,WAAW;AACpB,eAAO,SAAS;AAChB,eAAO,SAAS;AAAA,MACpB;AACA,WAAK,iBAAiB,QAAQ;AAC9B,aAAO,KAAK;AAAA,IAChB;AAAA,EAAA;AAER;AAvHS;AAwHT,SAAS,cAAc,GAAG,SAAS;AAC7B,IAAA,SAAS,QAAQ,UAAU,EAAE;AAC7B,IAAA,iBAAiB,QAAQ,kBAAkB,EAAE;AAC7C,IAAA,UAAU,QAAQ,WAAW,EAAE;AAC/B,IAAA,wBACE,QAAQ,yBAAyB,EAAE;AACrC,IAAA,qBAAqB,QAAQ,sBAAsB,EAAE;AACrD,IAAA,yBACE,QAAQ,0BAA0B,EAAE;AACtC,IAAA,kBAAkB,QAAQ,mBAAmB,EAAE;AAC/C,IAAA,oBAAoB,QAAQ,qBAAqB,EAAE;AACnD,IAAA,sBAAsB,QAAQ,uBAAuB,EAAE;AACvD,IAAA,OAAO,QAAQ,QAAQ,EAAE;AAC3B,IAAE,WAAW,oBAAoB,EAAE,QAAQ,sBAAsB,EAAE,kBAAkB;AAC/E,QAAA,WAAW,kBAAkB,EAAE,QAAQ;AAAA,IACzC,UAAU,QAAQ;AAAA,IAClB,QAAQ,QAAQ;AAAA,EAAA,CACnB;AACM,SAAA,KAAK,QAAQ,EAAE,QAAQ,CAAA,WAAU,EAAE,mBAAmB,QAAQ,SAAS,MAAM,CAAC,CAAC;AACtF,MAAI,QAAQ,iBAAiB;AACzB,WAAO,KAAK,QAAQ,eAAe,EAAE,QAAQ,CAAA,WAAU,EAAE,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC,CAAC;AAAA,EACzH;AACA,MAAI,QAAQ,eAAe;AACvB,WAAO,KAAK,QAAQ,aAAa,EAAE,QAAQ,CAAA,WAAU,EAAE,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,EACnH;AACO,SAAA;AACX;AA1BS;AAqCT,MAAM,8CACoB,iBAAiB;AAE3C,SAAS,WAAW,UAAU,CAAC,GAAG,eAAe;AAE7C,QAAM,eAAe,2BAA2B,UAAU,QAAQ,MAAM,IAC9D,QAAQ,SACR;AAEV,QAAM,oBAAoB,UAAU,QAAQ,eAAe,IACrD,QAAQ,kBACR;AAEN,QAAM,qBAAqB,2BAA2B,eAC5C,CAAC,CAAC,QAAQ,mBACV;AACJ,QAAA,kCAAkB;AACxB,QAAM,CAAC,aAAa,QAAQ,IAAI,aAAa,SAAS,YAAY;AAClE,QAAM,SAAwB,2BAAY,QAAyC,aAAa,EAAE;AAClG,MAAK,OAAwC;AACrC,QAAA,gBAAgB,sBAAsB,MAAQ;AACzC,WAAA,eAAe,cAAc,6BAA6B,CAAC;AAAA,IACpE;AAAA,EACJ;AACA,WAAS,cAAc,WAAW;AACvB,WAAA,YAAY,IAAI,SAAS,KAAK;AAAA,EACzC;AAFS;AAGA,WAAA,cAAc,WAAW,UAAU;AAC5B,gBAAA,IAAI,WAAW,QAAQ;AAAA,EACvC;AAFS;AAGT,WAAS,iBAAiB,WAAW;AACjC,gBAAY,OAAO,SAAS;AAAA,EAChC;AAFS;AAGT;AACI,UAAM,OAAO;AAAA;AAAA,MAET,IAAI,OAAO;AACA,eAAA,2BAA2B,eAC5B,WACA;AAAA,MACV;AAAA;AAAA,MAEA,IAAI,mBAAmB;AACZ,eAAA;AAAA,MACX;AAAA;AAAA,MAEA,MAAM,QAAQ,QAAQ8B,UAAS;AAC3B,YAAgD,OACpC;AACR,cAAI,eAAe;AAAA,QACvB;AAEA,YAAI,sBAAsB;AACtB,YAAA,QAAQ,IAAI,qBAAqB,IAAI;AAEzC,YAAI,cAAcA,SAAQ,CAAC,CAAC,GAAG;AACrB,gBAAA,OAAOA,SAAQ,CAAC;AACtB,eAAK,mBACD,KAAK;AACT,eAAK,kBACD,KAAK;AAAA,QACb;AAEA,YAAI,uBAAuB;AACvB,YAAA,CAAC,gBAAgB,mBAAmB;AACb,iCAAA,mBAAmB,KAAK,KAAK,MAAM;AAAA,QAC9D;AAEA,YAAI,2BAA2B;AACrB,gBAAA,KAAK,MAAM,GAAGA,QAAO;AAAA,QAC/B;AAEA,YAAI,2BAA2B,cAAc;AACzC,cAAI,MAAM,YAAY,UAAU,SAAS,YAAY,IAAI,CAAC;AAAA,QAC9D;AAEA,cAAM,aAAa,IAAI;AACvB,YAAI,UAAU,MAAM;AAChB,kCAAwB,qBAAqB;AAC7C,eAAK,QAAQ;AACF;QAAA;AAGf,YAAgD,OAAkC;AAC9E,gBAAM,MAAM,MAAM,eAAe,KAAK,IAAI;AAC1C,cAAI,CAAC,KAAK;AACA,kBAAA,gBAAgB,eAAe,gCAAgC;AAAA,UACzE;AACA,gBAAM,UAAU;AAChB,cAAI,cAAc;AACd,kBAAM,WAAW;AACR,qBAAA,mBAAmB,SAAS,gBAAgB,OAAO;AAAA,UAAA,OAE3D;AAED,kBAAM,YAAY;AAClB,sBAAU,aAAa,KAAK,UAAU,aAAa,EAAE,OAAO;AAAA,UAChE;AACQ,kBAAA,GAAG,KAAK,gBAAgB;AAAA,QACpC;AAAA,MACJ;AAAA;AAAA,MAEA,IAAI,SAAS;AACF,eAAA;AAAA,MACX;AAAA,MACA,UAAU;AACN,oBAAY,KAAK;AAAA,MACrB;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,IAAA;AAEG,WAAA;AAAA,EACX;AACJ;AApHS;AAsHT,SAAS,QAAQ,UAAU,IAAI;AAC3B,QAAM,WAAW;AACjB,MAAI,YAAY,MAAM;AACZ,UAAA,gBAAgB,eAAe,sBAAsB;AAAA,EAC/D;AACI,MAAA,CAAC,SAAS,QACV,SAAS,WAAW,OAAO,QAC3B,CAAC,SAAS,WAAW,IAAI,qBAAqB;AACxC,UAAA,gBAAgB,eAAe,aAAa;AAAA,EACtD;AACM,QAAA,OAAO,gBAAgB,QAAQ;AAC/B,QAAA,KAAK,kBAAkB,IAAI;AAC3B,QAAA,mBAAmB,oBAAoB,QAAQ;AAC/C,QAAA,QAAQ,SAAS,SAAS,gBAAgB;AAChD,MAAI,yBAAyB;AAEzB,QAAI,KAAK,SAAS,YAAY,CAAC,QAAQ,gBAAgB;AAC/C,UAAA,CAAC,KAAK,kBAAkB;AAClB,cAAA,gBAAgB,eAAe,4BAA4B;AAAA,MACrE;AACA,aAAO,iBAAiB,UAAU,OAAO,IAAI,OAAO;AAAA,IACxD;AAAA,EACJ;AACA,MAAI,UAAU,UAAU;AACA,wBAAA,IAAI,SAAS,gBAAgB;AAC1C,WAAA;AAAA,EACX;AACA,MAAI,UAAU,UAAU;AAEpB,QAAIE,YAAW,YAAY,MAAM,UAAU,QAAQ,cAAc;AACjE,QAAIA,aAAY,MAAM;AAClB,UAAK,OAAwC;AACpC,aAAA,eAAe,cAAc,sBAAsB,CAAC;AAAA,MAC7D;AACAA,kBAAW;AAAA,IACf;AACOA,WAAAA;AAAAA,EACX;AACA,QAAM,eAAe;AACjB,MAAA,WAAW,aAAa,cAAc,QAAQ;AAClD,MAAI,YAAY,MAAM;AAClB,UAAM,kBAAkB/B,SAAO,CAAC,GAAG,OAAO;AAC1C,QAAI,YAAY,kBAAkB;AAC9B,sBAAgB,SAAS,iBAAiB;AAAA,IAC9C;AACA,QAAI,IAAI;AACJ,sBAAgB,SAAS;AAAA,IAC7B;AACA,eAAW,eAAe,eAAe;AACzC,QAAI,aAAa,kBAAkB;AAC/B,eAAS,aAAa,IAClB,aAAa,iBAAiB,QAAQ;AAAA,IAC9C;AACe,mBAAA,cAAc,UAAU,QAAQ;AAClC,iBAAA,cAAc,UAAU,QAAQ;AAAA,EACjD;AACO,SAAA;AACX;AAzDS;AA2ET,MAAM,gBAAgB,mDAAC,SAElB;AACG,MAAA,EAAE,uBAAuB,OAAO;AAC1B,UAAA,gBAAgB,eAAe,8BAA8B;AAAA,EACvE;AACO,SAAA;AACX,GAPsB;AAQtB,SAAS,aAAa,SAAS,YAAY,eACzC;AACE,QAAM,QAAQ;AACd;AACI,UAAM,MAAM,2BAA2B,aACjC,MAAM,IAAI,MAAM,cAAc,OAAO,CAAC,IACtC,MAAM,IAAI,MAAM,eAAe,OAAO,CAAC;AAC7C,QAAI,OAAO,MAAM;AACP,YAAA,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACO,WAAA,CAAC,OAAO,GAAG;AAAA,EACtB;AACJ;AAZS;AAaT,SAAS,gBAAgB,UAAU;AAC/B;AACU,UAAA,OAAO,OAAO,CAAC,SAAS,OACxB,SAAS,WAAW,IAAI,sBACxB,gBAAgB;AAEtB,QAAI,CAAC,MAAM;AACP,YAAM,gBAAgB,CAAC,SAAS,OAC1B,eAAe,mBACf,eAAe,0BAA0B;AAAA,IACnD;AACO,WAAA;AAAA,EACX;AACJ;AAbS;AAeT,SAAS,SAAS,SAAS,kBAAkB;AAElC,SAAA,cAAc,OAAO,IACrB,YAAY,mBACT,UACA,WACJ,CAAC,QAAQ,WACL,UACA,QAAQ;AACtB;AATS;AAUT,SAAS,kBAAkB,MAAM;AAE7B,SAAO,KAAK,SAAS,gBACX,KAAK,SACL,KAAK,OAAO;AAE1B;AANS;AAOT,SAAS,YAAY,MAAM,QAAQ,eAAe,OAAO;AACrD,MAAI,WAAW;AACf,QAAM,OAAO,OAAO;AAChB,MAAA,UAAU,2BAA2B,QAAQ,YAAY;AAC7D,SAAO,WAAW,MAAM;AACpB,UAAM,eAAe;AACjB,QAAA,KAAK,SAAS,eAAe;AAClB,iBAAA,aAAa,cAAc,OAAO;AAAA,IAAA,OAE5C;AACD,UAAI,yBAAyB;AACnB,cAAA,UAAU,aAAa,cAAc,OAAO;AAClD,YAAI,WAAW,MAAM;AACjB,qBAAW,QACN;AACL,cAAI,gBACA,YACA,CAAC,SAAS,sBAAsB,GAClC;AACa,uBAAA;AAAA,UACf;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,YAAY,MAAM;AAClB;AAAA,IACJ;AACA,QAAI,SAAS,SAAS;AAClB;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB;AACO,SAAA;AACX;AAjCS;AAkCT,SAAS,2BAA2B,QAAQ,eAAe,OAAO;AAC9D,MAAI,UAAU,MAAM;AACT,WAAA;AAAA,EACX;AACA;AAEI,WAAO,CAAC,eACF,OAAO,SACP,OAAO,MAAM,OAAO,OAAO;AAAA,EACrC;AACJ;AAVS;AAWT,SAAS,eAAe,MAAM,QAAQ,UAAU;AAC5C,MAAI,UAAU;AACd;AACI,cAAU,MAAM;AAEZ,UAAgD,OAE3B;AACV,eAAA,MAAM,GAAG,eAAe;AAC/B,kBAAU,cAAc;AAExB,cAAM,YAAY;AAClB,kBAAU,aAAa,KAAK,UAAU,aAAa,EAAE,OAAO;AACpD,gBAAA,GAAG,KAAK,gBAAgB;AAAA,MACpC;AAAA,OACD,MAAM;AACT,gBAAY,MAAM;AAEd,YAAM,YAAY;AAElB,UAAgD,OAGd;AACnB,mBAAA,QAAQ,IAAI,KAAK,gBAAgB;AAC5C,kBAAU,cAAc,KAAK,UAAU,cAAc,EAAE;AAChD,eAAA,OAAO,MAAM,GAAG;AAAA,MAC3B;AACA,WAAK,iBAAiB,MAAM;AAEtB,YAAA,UAAU,UAAU,aAAa;AACvC,UAAI,SAAS;AACD;AACR,eAAO,UAAU,aAAa;AAAA,MAClC;AAAA,OACD,MAAM;AAAA,EACb;AACJ;AArCS;AAsCT,SAAS,iBAAiB,UAAU,OAAO,MAAM,UAAU,CAAA,GACzD;AACE,QAAM,eAAe,UAAU;AACzB,QAAA,YAAY,WAAW,IAAI;AAC7B,MAAA,gBACA,SAAS,SACT,EAAE,SAAS,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,SAAS;AAC7D,UAAA,gBAAgB,eAAe,4CAA4C;AAAA,EACrF;AACM,QAAA,iBAAiB,UAAU,QAAQ,aAAa,IAChD,QAAQ,gBACR,CAACC,WAAS,QAAQ,MAAM;AAC9B,QAAM,UAAU;AAAA;AAAA,IAEhB,CAAC,gBAAgB,iBACX,KAAK,OAAO,QACZA,WAAS,QAAQ,MAAM,IACnB,QAAQ,SACR;AAAA,EAAA;AACV,QAAM,kBAAkB;AAAA;AAAA,IAExB,CAAC,gBAAgB,iBACX,KAAK,eAAe,QACpBA,WAAS,QAAQ,cAAc,KAC7B,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,EAAA;AAClB,QAAM,YAAY,IAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAE/D,QAAM,mBAAmB,IAAI,cAAc,QAAQ,eAAe,IAC5D,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAA,EAAI,CAAA;AAE7B,QAAM,iBAAiB,IAAI,cAAc,QAAQ,aAAa,IACxD,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAA,EAAI,CAAA;AAE7B,QAAM,eAAe,eACf,KAAK,cACL,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC1D,QAAQ,cACR;AAEV,QAAM,gBAAgB,eAChB,KAAK,eACL,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC5D,QAAQ,eACR;AAEJ,QAAA,gBAAgB,eAChB,KAAK,eACL,UAAU,QAAQ,YAAY,IAC1B,QAAQ,eACR;AAEJ,QAAA,kBAAkB,CAAC,CAAC,QAAQ;AAElC,QAAM,WAAW,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAEjE,QAAM,mBAAmB,WAAW,QAAQ,eAAe,IACrD,QAAQ,kBACR;AAEA,QAAA,mBAAmB,eACnB,KAAK,kBACL,UAAU,QAAQ,eAAe,IAC7B,QAAQ,kBACR;AACJ,QAAA,mBAAmB,CAAC,CAAC,QAAQ;AAE7B,QAAA,aAAa,eACb,KAAK,YACL,cAAc,QAAQ,SAAS,IAC3B,QAAQ,YACR,CAAA;AAEV,QAAM,eAAe,QAAQ,eAAgB,gBAAgB,KAAK;AAElE,WAAS,wBAAwB;AACtB,WAAA;AAAA,MACH,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,eAAe;AAAA,IAAA;AAAA,EAEvB;AARS;AAUT,QAAM,SAAS,SAAS;AAAA,IACpB,KAAK,6BAAM;AACP,aAAO,UAAU,QAAQ,UAAU,MAAM,OAAO,QAAQ,QAAQ;AAAA,IACpE,GAFK;AAAA,IAGL,KAAK,wBAAO,QAAA;AACR,UAAI,UAAU,OAAO;AACP,kBAAA,MAAM,OAAO,QAAQ;AAAA,MACnC;AACA,cAAQ,QAAQ;AAAA,IACpB,GALK;AAAA,EAKL,CACH;AAED,QAAM,iBAAiB,SAAS;AAAA,IAC5B,KAAK,6BAAM;AACP,aAAO,UAAU,QACX,UAAU,MAAM,eAAe,QAC/B,gBAAgB;AAAA,IAC1B,GAJK;AAAA,IAKL,KAAK,wBAAO,QAAA;AACR,UAAI,UAAU,OAAO;AACP,kBAAA,MAAM,eAAe,QAAQ;AAAA,MAC3C;AACA,sBAAgB,QAAQ;AAAA,IAC5B,GALK;AAAA,EAKL,CACH;AAEK,QAAA,WAAW,SAAS,MAAM;AAC5B,QAAI,UAAU,OAAO;AAEV,aAAA,UAAU,MAAM,SAAS;AAAA,IAAA,OAE/B;AAED,aAAO,UAAU;AAAA,IACrB;AAAA,EAAA,CACH;AACD,QAAM,kBAAkB,SAAS,MAAM,iBAAiB,KAAK;AAC7D,QAAM,gBAAgB,SAAS,MAAM,eAAe,KAAK;AACzD,WAAS,4BAA4B;AACjC,WAAO,UAAU,QACX,UAAU,MAAM,0BAChB,IAAA;AAAA,EACV;AAJS;AAKT,WAAS,0BAA0B,SAAS;AACxC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,0BAA0B,OAAO;AAAA,IACrD;AAAA,EACJ;AAJS;AAKT,WAAS,oBAAoB;AACzB,WAAO,UAAU,QAAQ,UAAU,MAAM,kBAAsB,IAAA;AAAA,EACnE;AAFS;AAGT,WAAS,kBAAkB,SAAS;AAChC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,kBAAkB,OAAO;AAAA,IAC7C;AAAA,EACJ;AAJS;AAKT,WAAS,aAAa,IAAI;AACA;AACtB,WAAO,GAAG;AAAA,EACd;AAHS;AAIT,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AAJS;AAKT,WAAS,MAAM,MAAM;AACjB,WAAO,UAAU,QACX,QAAQ,MAAM,UAAU,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,IACjD;AAAA,EACV;AAJS;AAKT,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AAJS;AAKT,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AAJS;AAKT,WAAS,GAAG,KAAK;AACb,WAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,GAAG,IAAI;EACvD;AAFS;AAGA,WAAA,GAAG,KAAKwB,SAAQ;AACrB,WAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,KAAKA,OAAM,IAAI;AAAA,EAC/D;AAFS;AAGT,WAAS,iBAAiBA,SAAQ;AAC9B,WAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiBA,OAAM,IAAI;EACxE;AAFS;AAGA,WAAA,iBAAiBA,SAAQ,SAAS;AACvC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,iBAAiBA,SAAQ,OAAO;AACtC,gBAAA,MAAMA,OAAM,IAAI;AAAA,IAC9B;AAAA,EACJ;AALS;AAMA,WAAA,mBAAmBA,SAAQ,SAAS;AACzC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,mBAAmBA,SAAQ,OAAO;AAAA,IACtD;AAAA,EACJ;AAJS;AAKT,WAAS,kBAAkBA,SAAQ;AAC/B,WAAO,UAAU,QAAQ,UAAU,MAAM,kBAAkBA,OAAM,IAAI;EACzE;AAFS;AAGA,WAAA,kBAAkBA,SAAQ3B,SAAQ;AACvC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,kBAAkB2B,SAAQ3B,OAAM;AAC/B,uBAAA,MAAM2B,OAAM,IAAI3B;AAAAA,IACrC;AAAA,EACJ;AALS;AAMA,WAAA,oBAAoB2B,SAAQ3B,SAAQ;AACzC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,oBAAoB2B,SAAQ3B,OAAM;AAAA,IACtD;AAAA,EACJ;AAJS;AAKT,WAAS,gBAAgB2B,SAAQ;AAC7B,WAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgBA,OAAM,IAAI;EACvE;AAFS;AAGA,WAAA,gBAAgBA,SAAQ3B,SAAQ;AACrC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,gBAAgB2B,SAAQ3B,OAAM;AAC/B,qBAAA,MAAM2B,OAAM,IAAI3B;AAAAA,IACnC;AAAA,EACJ;AALS;AAMA,WAAA,kBAAkB2B,SAAQ3B,SAAQ;AACvC,QAAI,UAAU,OAAO;AACP,gBAAA,MAAM,kBAAkB2B,SAAQ3B,OAAM;AAAA,IACpD;AAAA,EACJ;AAJS;AAKT,QAAM,UAAU;AAAA,IACZ,IAAI,KAAK;AACL,aAAO,UAAU,QAAQ,UAAU,MAAM,KAAK;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI,gBAAgB;AAChB,aAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgB;AAAA,IAC7D;AAAA,IACA,IAAI,cAAc,KAAK;AACnB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,gBAAgB;AAAA,MACpC;AAAA,IACJ;AAAA,IACA,IAAI,mBAAmB;AACZ,aAAA,UAAU,QACX,UAAU,MAAM,mBAChB,OAAO,KAAK,UAAU,KAAK;AAAA,IACrC;AAAA,IACA,IAAI,YAAY;AACZ,aAAQ,UAAU,QAAQ,UAAU,MAAM,YAAY;AAAA,IAC1D;AAAA,IACA,IAAI,cAAc;AACd,aAAQ,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,IAC5D;AAAA,IACA,IAAI,WAAW;AACX,aAAO,UAAU,QAAQ,UAAU,MAAM,WAAW;AAAA,IACxD;AAAA,IACA,IAAI,cAAc;AACd,aAAO,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,IAC3D;AAAA,IACA,IAAI,YAAY,KAAK;AACjB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,cAAc;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,IAAI,eAAe;AACf,aAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,IAC5D;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,cAAc;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,IAAI,eAAe;AACf,aAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,IAC5D;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,eAAe;AAAA,MACnC;AAAA,IACJ;AAAA,IACA,IAAI,iBAAiB;AACjB,aAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiB;AAAA,IAC9D;AAAA,IACA,IAAI,eAAe,KAAK;AACpB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,iBAAiB;AAAA,MACrC;AAAA,IACJ;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,IACV;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,kBAAkB;AAAA,MACtC;AAAA,IACJ;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,IACV;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,kBAAkB;AAAA,MACtC;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,WAAS,KAAK,UAAU;AACX,aAAA,OAAO,QAAQ,QAAQ;AACvB,aAAA,eAAe,QAAQ,gBAAgB;AAChD,WAAO,KAAK,UAAU,KAAK,EAAE,QAAQ,CAAA2B,YAAU;AAC3C,eAAS,mBAAmBA,SAAQ,UAAU,MAAMA,OAAM,CAAC;AAAA,IAAA,CAC9D;AACD,WAAO,KAAK,iBAAiB,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAClD,eAAS,oBAAoBA,SAAQ,iBAAiB,MAAMA,OAAM,CAAC;AAAA,IAAA,CACtE;AACD,WAAO,KAAK,eAAe,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAChD,eAAS,kBAAkBA,SAAQ,eAAe,MAAMA,OAAM,CAAC;AAAA,IAAA,CAClE;AACD,aAAS,kBAAkB;AAC3B,aAAS,iBAAiB;AAC1B,aAAS,eAAe;AACxB,aAAS,eAAe;AACxB,aAAS,cAAc;AACvB,aAAS,kBAAkB;AAAA,EAC/B;AAlBS;AAmBT,gBAAc,MAAM;AAChB,QAAI,SAAS,SAAS,QAAQ,SAAS,MAAM,SAAS,MAAM;AAClD,YAAA,gBAAgB,eAAe,mCAAmC;AAAA,IAC5E;AAEA,UAAM,WAAY,UAAU,QAAQ,SAAS,MAAM,MAC9C;AACL,QAAI,UAAU,UAAU;AACZ,cAAA,QAAQ,SAAS,OAAO;AAChB,sBAAA,QAAQ,SAAS,eAAe;AACtC,gBAAA,QAAQ,SAAS,SAAS;AACnB,uBAAA,QAAQ,SAAS,gBAAgB;AACnC,qBAAA,QAAQ,SAAS,cAAc;AAAA,eAEzC,cAAc;AACnB,WAAK,QAAQ;AAAA,IACjB;AAAA,EAAA,CACH;AACM,SAAA;AACX;AAvWS;AAwWT,MAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACJ;AACA,MAAM,sBAAsB,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI;AAE5D,SAAS,mBAAmB,KAAK,UAAU;AACjC,QAAA,OAAc,uBAAA,OAAO,IAAI;AAC/B,oBAAkB,QAAQ,CAAQ,SAAA;AAC9B,UAAM,OAAO,OAAO,yBAAyB,UAAU,IAAI;AAC3D,QAAI,CAAC,MAAM;AACD,YAAA,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,UAAM,OAAO,MAAM,KAAK,KAAK,IACvB;AAAA,MACE,MAAM;AACF,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA;AAAA,MAEA,IAAI,KAAK;AACL,aAAK,MAAM,QAAQ;AAAA,MACvB;AAAA,IAAA,IAEF;AAAA,MACE,MAAM;AACK,eAAA,KAAK,OAAO,KAAK,IAAI;AAAA,MAChC;AAAA,IAAA;AAED,WAAA,eAAe,MAAM,MAAM,IAAI;AAAA,EAAA,CACzC;AACG,MAAA,OAAO,iBAAiB,QAAQ;AACpC,sBAAoB,QAAQ,CAAU,WAAA;AAClC,UAAM,OAAO,OAAO,yBAAyB,UAAU,MAAM;AAC7D,QAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AAChB,YAAA,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,WAAO,eAAe,IAAI,OAAO,kBAAkB,IAAI,MAAM,IAAI,IAAI;AAAA,EAAA,CACxE;AACD,QAAM,UAAU,6BAAM;AAEX,WAAA,IAAI,OAAO,iBAAiB;AACnC,wBAAoB,QAAQ,CAAU,WAAA;AAElC,aAAO,IAAI,OAAO,iBAAiB,IAAI,MAAM,EAAE;AAAA,IAAA,CAClD;AAAA,EAAA,GANW;AAQT,SAAA;AACX;AAzCS;AA2CT;AACqB;AACrB;AAEA,IAAI,6BAA6B;AAC7B,0BAAwB,OAAO;AACnC,OACK;AACD,0BAAwB,iBAAiB;AAC7C;AAEA,wBAAwBJ,cAAY;AAEpC,yBAAyB,uBAAuB;AAEhD,IAA+C,2BAA2B;AACtE,QAAM,SAAS;AACf,SAAO,cAAc;AACrB,kBAAgB,OAAO,gCAAgC;AAC3D;AACA,IAAK,MAAwC;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}