{"id": "3ba6fcae-c49e-40dc-8725-12d4cd833fcb", "revision": 0, "last_node_id": 57, "last_link_id": 27, "nodes": [{"id": 43, "type": "SaveImage", "pos": [2047.5806884765625, 1210.6407470703125], "size": [270, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 25}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 41, "type": "StabilityStableImageUltraNode", "pos": [1609.49853515625, 1210.4818115234375], "size": [407.5131530761719, 273.0788879394531], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "shape": 7, "type": "IMAGE", "link": 27}, {"name": "negative_prompt", "shape": 7, "type": "STRING", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [25]}], "properties": {"Node name for S&R": "StabilityStableImageUltraNode"}, "widgets_values": ["Portrait of a young woman with long wavy brown hair in a bun, delicate framed glasses, porcelain-like fair skin, subtle makeup, black silk top, dreamlike soft light halo, misty ethereal atmosphere, soft focus effect, pastel pink tones, clear refined facial details, fairytale-like light spots, minimalist composition, luxurious quality, resembling a Vogue dream cover, <PERSON> photography style, ethereal transcendence, delicate light transitions, strong artistic feel", "1:1", "cinematic", 1985902675, "randomize", 0.5]}, {"id": 57, "type": "LoadImage", "pos": [1318.4957275390625, 1208.7115478515625], "size": [274.080078125, 314], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [27]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["input.png", "image"]}, {"id": 56, "type": "<PERSON>downNote", "pos": [974.457763671875, 1211.5211181640625], "size": [326.5185852050781, 286.05029296875], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[25, 41, 0, 43, 0, "IMAGE"], [27, 57, 0, 41, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.****************, "offset": [-1514.8330328127558, -967.3348689694575]}, "frontendVersion": "1.18.5"}, "version": 0.4}