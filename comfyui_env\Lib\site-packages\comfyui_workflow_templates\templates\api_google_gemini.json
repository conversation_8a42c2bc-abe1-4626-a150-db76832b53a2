{"id": "abd9d397-d971-4580-956e-61ac81383e04", "revision": 0, "last_node_id": 14, "last_link_id": 11, "nodes": [{"id": 8, "type": "LoadImage", "pos": [-7350, 3010], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 0, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 9, "type": "LoadImage", "pos": [-7650, 3010], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 1, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [7]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 10, "type": "LoadImage", "pos": [-7950, 3010], "size": [274.080078125, 314.0001220703125], "flags": {}, "order": 2, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 11, "type": "ImageBatch", "pos": [-7520, 2890], "size": [140, 46], "flags": {}, "order": 8, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 7}, {"name": "image2", "type": "IMAGE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9]}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 12, "type": "ImageBatch", "pos": [-7220, 2890], "size": [140, 46], "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "image1", "type": "IMAGE", "link": 9}, {"name": "image2", "type": "IMAGE", "link": 10}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": null}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 13, "type": "GeminiInputFiles", "pos": [-7040, 3180], "size": [295.72265625, 60], "flags": {}, "order": 3, "mode": 4, "inputs": [{"name": "GEMINI_INPUT_FILES", "shape": 7, "type": "GEMINI_INPUT_FILES", "link": null}], "outputs": [{"name": "GEMINI_INPUT_FILES", "type": "GEMINI_INPUT_FILES", "links": [11]}], "properties": {"Node name for S&R": "GeminiInputFiles"}, "widgets_values": ["input.txt"]}, {"id": 5, "type": "GeminiInputFiles", "pos": [-7040, 3280], "size": [295.72265625, 60], "flags": {}, "order": 9, "mode": 4, "inputs": [{"name": "GEMINI_INPUT_FILES", "shape": 7, "type": "GEMINI_INPUT_FILES", "link": 11}], "outputs": [{"name": "GEMINI_INPUT_FILES", "type": "GEMINI_INPUT_FILES", "links": [4]}], "properties": {"Node name for S&R": "GeminiInputFiles"}, "widgets_values": ["input.txt"]}, {"id": 3, "type": "LoadAudio", "pos": [-7020, 2720], "size": [274.080078125, 136], "flags": {}, "order": 4, "mode": 4, "inputs": [], "outputs": [{"name": "AUDIO", "type": "AUDIO", "links": [2]}], "properties": {"Node name for S&R": "LoadAudio"}, "widgets_values": ["lip-sync-input-blade-runner.mp4", null, null]}, {"id": 4, "type": "LoadVideo", "pos": [-7020, 2910], "size": [274.080078125, 228.1700439453125], "flags": {}, "order": 5, "mode": 4, "inputs": [], "outputs": [{"name": "VIDEO", "type": "VIDEO", "links": [3]}], "properties": {"Node name for S&R": "LoadVideo"}, "widgets_values": ["ComfyUI_00028_.mp4", "image"]}, {"id": 2, "type": "LoadImage", "pos": [-7020, 2360], "size": [274.080078125, 314], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 7, "type": "PreviewAny", "pos": [-6270, 2360], "size": [430, 350], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "source", "type": "*", "link": 6}], "outputs": [], "properties": {"Node name for S&R": "PreviewAny"}, "widgets_values": []}, {"id": 1, "type": "GeminiNode", "pos": [-6690, 2360], "size": [390, 430], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "shape": 7, "type": "IMAGE", "link": 1}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": 2}, {"name": "video", "shape": 7, "type": "VIDEO", "link": 3}, {"name": "files", "shape": 7, "type": "GEMINI_INPUT_FILES", "link": 4}], "outputs": [{"name": "STRING", "type": "STRING", "links": [6]}], "properties": {"Node name for S&R": "GeminiNode"}, "widgets_values": ["- Role: AI Image Analysis and Description Specialist\n- Background: The user requires a prompt that enables AI to analyze images and generate detailed descriptions which can be used as drawing prompts to create similar images. This is essential for tasks like content creation, design inspiration, and artistic exploration.\n- Profile: As an AI Image Analysis and Description Specialist, you possess extensive knowledge in computer vision, image processing, and natural language generation. You are adept at interpreting visual data and translating it into descriptive text that can guide the creation of new images.\n- Skills: Proficiency in image recognition, feature extraction, descriptive language generation, and understanding of artistic elements such as composition, color, and texture.\n- Goals: To analyze the provided image, generate a comprehensive and detailed description that captures the key visual elements, and ensure this description can effectively serve as a drawing prompt for creating similar images.\n- Constrains: The description must be clear, concise, and specific enough to guide the creation of a similar image. It should avoid ambiguity and focus on the most salient features of the image. The output should only contain the drawing prompt.\n- OutputFormat: A detailed text description of the image, highlighting key visual elements such as objects, colors, composition, and any unique features.\n- Workflow:\n  1. Analyze the image to identify key visual elements including objects, colors, and composition.\n  2. Generate a detailed description that captures the essence of the image, ensuring it is specific and actionable.\n  3. Refine the description to ensure clarity and conciseness, making it suitable for use as a drawing prompt.", "gemini-2.5-pro-preview-05-06", 639785196865722, "randomize", "A naive, child-like digital drawing of a whimsical figure. The figure has a very pale peach circular face, large, solid bright blue round eyes, and a simple black curved smile. Dominating its head are two enormous, pointed yellow ears, resembling fennec fox ears or butterfly wings, with a lighter pale yellow inner lining. The figure wears a simple, sleeveless, A-line dress in a medium pink color. Its pale peach arms are simply rendered and outstretched to the sides. Its legs and feet are also simple, pale peach shapes. The figure stands on a vibrant green, gently sloping hill under a bright blue sky. Two small, fluffy white clouds are positioned in the sky, one on either side of the figure's large ears. The style is characterized by flat, solid colors and simple, somewhat blocky shapes, as if drawn with basic digital art tools."], "color": "#432", "bgcolor": "#653"}, {"id": 14, "type": "<PERSON>downNote", "pos": [-7440, 2360], "size": [390, 320], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 2, 0, 1, 0, "IMAGE"], [2, 3, 0, 1, 1, "AUDIO"], [3, 4, 0, 1, 2, "VIDEO"], [4, 5, 0, 1, 3, "GEMINI_INPUT_FILES"], [6, 1, 0, 7, 0, "*"], [7, 9, 0, 11, 0, "IMAGE"], [8, 10, 0, 11, 1, "IMAGE"], [9, 11, 0, 12, 0, "IMAGE"], [10, 8, 0, 12, 1, "IMAGE"], [11, 13, 0, 5, 0, "GEMINI_INPUT_FILES"]], "groups": [{"id": 1, "title": "Multiple images input example", "bounding": [-7960, 2800, 894.080078125, 537.5999755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8954302432553479, "offset": [7696.86877518473, -2164.1744419951]}, "frontendVersion": "1.21.0"}, "version": 0.4}