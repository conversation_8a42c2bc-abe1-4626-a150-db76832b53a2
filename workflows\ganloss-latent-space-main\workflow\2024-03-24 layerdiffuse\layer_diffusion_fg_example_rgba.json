{"last_node_id": 36, "last_link_id": 51, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["beautiful scenery nature glass bottle landscape, , purple galaxy bottle,"]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [5, 479], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [18], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [22], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["juggernautXL_v8Rundiffusion.safetensors"]}, {"id": 14, "type": "VAEDecode", "pos": [1275, 198], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 21}, {"name": "vae", "type": "VAE", "link": 22, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29, 50], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [911, 198], "size": {"0": 315, "1": 262}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 19}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [21, 49], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1029477926308287, "randomize", 20, 8, "euler", "normal", 1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [480, 691], "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 36, "type": "LayeredDiffusionDecodeRGBA", "pos": [1589, 199], "size": {"0": 243.60000610351562, "1": 102}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 49}, {"name": "images", "type": "IMAGE", "link": 50}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [51], "shape": 3}], "properties": {"Node name for S&R": "LayeredDiffusionDecodeRGBA"}, "widgets_values": ["SDXL", 16]}, {"id": 27, "type": "PreviewImage", "pos": [1930, 197], "size": {"0": 289.6058349609375, "1": 299.6588134765625}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 51, "slot_index": 0}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 20, "type": "PreviewImage", "pos": [1570, 365], "size": {"0": 289.6058349609375, "1": 299.6588134765625}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 29}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 13, "type": "LayeredDiffusionApply", "pos": [468, -2], "size": {"0": 327.8314208984375, "1": 106.42147827148438}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 18}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [19], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LayeredDiffusionApply"}, "widgets_values": ["SDXL, Conv Injection", 1]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [18, 4, 0, 13, 0, "MODEL"], [19, 13, 0, 3, 0, "MODEL"], [21, 3, 0, 14, 0, "LATENT"], [22, 4, 2, 14, 1, "VAE"], [29, 14, 0, 20, 0, "IMAGE"], [49, 3, 0, 36, 0, "LATENT"], [50, 14, 0, 36, 1, "IMAGE"], [51, 36, 0, 27, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}