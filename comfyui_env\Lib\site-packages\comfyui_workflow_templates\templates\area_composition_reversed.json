{"last_node_id": 48, "last_link_id": 114, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [1320, 302], "size": [210, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 36}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [49], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1575, 350], "size": [210, 58], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 49}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [175, 496], "size": [425.28, 180.61], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 106}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"]}, {"id": 12, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [834, -246], "size": [342.6, 46], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 63}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 57}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [58], "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 20, "type": "VAELoader", "pos": [1041, 544], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [36, 51], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"]}, {"id": 35, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [873, -705], "size": [342.6, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 61}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 62}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [63], "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 5, "type": "EmptyLatentImage", "pos": [695, 531], "size": [315, 106], "flags": {"collapsed": false}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [704, 1280, 1]}, {"id": 22, "type": "LatentUpscale", "pos": [1412, 79], "size": [315, 130], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 41}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [42], "slot_index": 0}], "properties": {"Node name for S&R": "LatentUpscale"}, "widgets_values": ["nearest-exact", 1088, 1920, "disabled"]}, {"id": 14, "type": "CLIPTextEncode", "pos": [-4, -994], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 110}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [89], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (night:1.3) (darkness) sky (black) (stars:1.2) (galaxy:1.2) (space) (universe)"]}, {"id": 13, "type": "CLIPTextEncode", "pos": [-5, -729], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 109}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [91], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (evening:1.2) (sky:1.2) (clouds) (colorful) (HDR:1.2) (sunset:1.3)\n"]}, {"id": 17, "type": "CLIPTextEncode", "pos": [11, -455], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 108}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [90], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) (daytime:1.2) sky (blue)\n"]}, {"id": 18, "type": "ConditioningSetArea", "pos": [482, -709], "size": [312, 154], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 90}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [62], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 320, 1]}, {"id": 33, "type": "CLIPTextEncode", "pos": [16, -217], "size": [400, 200], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 107}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [92], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) morning sky\n\n"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [152, 265], "size": [422.85, 164.31], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 105}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [93], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece) (best quality) beautiful landscape breathtaking amazing view nature photograph forest mountains ocean (sky) national park scenery"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [885, 136], "size": [315, 262], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 104}, {"name": "positive", "type": "CONDITIONING", "link": 54}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7, 41], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [823155751257884, "randomize", 13, 8.5, "dpmpp_sde", "normal", 1]}, {"id": 11, "type": "ConditioningSetArea", "pos": [479, -454], "size": [314, 154], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 91, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [57], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 512, 1]}, {"id": 19, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [1180, -151], "size": [342.6, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 58}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 94}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [54], "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 10, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [803, -149], "size": [342.6, 46], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 40}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 93}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [94], "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "widgets_values": []}, {"id": 34, "type": "ConditioningSetArea", "pos": [476, -932], "size": [312, 154], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 92, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [61], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 0, 1]}, {"id": 15, "type": "ConditioningSetArea", "pos": [466, -233], "size": [299, 154], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 89}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [40], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningSetArea"}, "widgets_values": [704, 384, 0, 704, 1.5]}, {"id": 44, "type": "CheckpointLoaderSimple", "pos": [-703, 444], "size": [315, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [104], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [111], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Anything-V3.0.ckpt"]}, {"id": 46, "type": "CLIPSetLastLayer", "pos": [-354, 244], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 111, "slot_index": 0}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [105, 106, 107, 108, 109, 110], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": [2220, -398], "size": [315, 262], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 95}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 47}, {"name": "latent_image", "type": "LATENT", "link": 42}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [50], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [418330692116968, "randomize", 14, 7, "dpmpp_2m", "simple", 0.5]}, {"id": 32, "type": "SaveImage", "pos": [2825, -62], "size": [315, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 87}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 31, "type": "VAEDecode", "pos": [2590, -61], "size": [210, 46], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 50}, {"name": "vae", "type": "VAE", "link": 51}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [87], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 26, "type": "CLIPTextEncode", "pos": [1781, -571], "size": [400, 200], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 113}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [46], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(best quality) beautiful (HDR:1.2) (realistic:1.2) landscape breathtaking amazing view nature scenery photograph forest mountains ocean daytime night evening morning, (sky:1.2)\n"]}, {"id": 27, "type": "CLIPTextEncode", "pos": [1787, -317], "size": [400, 200], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 114}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [47], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(hands), text, error, cropped, (worst quality:1.2), (low quality:1.2), normal quality, (jpeg artifacts:1.3), signature, watermark, username, blurry, artist name, monochrome, sketch, censorship, censor, (copyright:1.2), extra legs, (forehead mark) (depth of field) (emotionless) (penis) (pumpkin)"]}, {"id": 47, "type": "CLIPSetLastLayer", "pos": [1407, -402], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 112}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [113, 114], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 45, "type": "CheckpointLoaderSimple", "pos": [1074, -444], "size": [315, 98], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [95], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [112], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": null}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["AbyssOrangeMix2_hard.safetensors"]}, {"id": 48, "type": "<PERSON>downNote", "pos": [-690, 615], "size": [225, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🛈 [Learn more about this workflow](https://comfyanonymous.github.io/ComfyUI_examples/area_composition/)"], "color": "#432", "bgcolor": "#653"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [36, 20, 0, 8, 1, "VAE"], [40, 15, 0, 10, 0, "CONDITIONING"], [41, 3, 0, 22, 0, "LATENT"], [42, 22, 0, 24, 3, "LATENT"], [46, 26, 0, 24, 1, "CONDITIONING"], [47, 27, 0, 24, 2, "CONDITIONING"], [49, 8, 0, 9, 0, "IMAGE"], [50, 24, 0, 31, 0, "LATENT"], [51, 20, 0, 31, 1, "VAE"], [54, 19, 0, 3, 1, "CONDITIONING"], [57, 11, 0, 12, 1, "CONDITIONING"], [58, 12, 0, 19, 0, "CONDITIONING"], [61, 34, 0, 35, 0, "CONDITIONING"], [62, 18, 0, 35, 1, "CONDITIONING"], [63, 35, 0, 12, 0, "CONDITIONING"], [87, 31, 0, 32, 0, "IMAGE"], [89, 14, 0, 15, 0, "CONDITIONING"], [90, 17, 0, 18, 0, "CONDITIONING"], [91, 13, 0, 11, 0, "CONDITIONING"], [92, 33, 0, 34, 0, "CONDITIONING"], [93, 6, 0, 10, 1, "CONDITIONING"], [94, 10, 0, 19, 1, "CONDITIONING"], [95, 45, 0, 24, 0, "MODEL"], [104, 44, 0, 3, 0, "MODEL"], [105, 46, 0, 6, 0, "CLIP"], [106, 46, 0, 7, 0, "CLIP"], [107, 46, 0, 33, 0, "CLIP"], [108, 46, 0, 17, 0, "CLIP"], [109, 46, 0, 13, 0, "CLIP"], [110, 46, 0, 14, 0, "CLIP"], [111, 44, 1, 46, 0, "CLIP"], [112, 45, 1, 47, 0, "CLIP"], [113, 47, 0, 26, 0, "CLIP"], [114, 47, 0, 27, 0, "CLIP"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.79, "offset": [1022.96, -230.7]}}, "version": 0.4, "models": [{"name": "vae-ft-mse-840000-ema-pruned.safetensors", "url": "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors?download=true", "directory": "vae"}]}