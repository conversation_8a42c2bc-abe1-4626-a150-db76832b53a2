{"last_node_id": 15, "last_link_id": 19, "nodes": [{"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 186], "size": [315, 474], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [7]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [355670965427352, "randomize", 20, 8, "euler", "normal", 1, ""]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-11, 307], "size": [315, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [1]}, {"name": "CLIP", "type": "CLIP", "slot_index": 1, "links": [17, 18]}, {"name": "VAE", "type": "VAE", "slot_index": 2, "links": [19]}], "properties": {"Node name for S&R": "CheckpointLoaderSimple", "models": [{"name": "v1-5-pruned-emaonly-fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/stable-diffusion-v1-5-archive/resolve/main/v1-5-pruned-emaonly-fp16.safetensors?download=true", "directory": "checkpoints"}]}, "widgets_values": ["v1-5-pruned-emaonly-fp16.safetensors"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [2]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.8500061035156, 164.30999755859375], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 17}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [4]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["masterpiece best quality girl standing in victorian clothing"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.2799987792969, 180.61000061035156], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 18}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [6]}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["bad hands"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": [210, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 19}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [15]}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1791, 169], "size": [455.989990234375, 553.0900268554688], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 16}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", ""]}, {"id": 13, "type": "UpscaleModelLoader", "pos": [1128, 51], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "slot_index": 0, "links": [14]}], "properties": {"Node name for S&R": "UpscaleModelLoader", "models": [{"name": "RealESRGAN_x4plus.pth", "url": "https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth", "directory": "upscale_models"}]}, "widgets_values": ["RealESRGAN_x4plus.pth"]}, {"id": 14, "type": "ImageUpscaleWithModel", "pos": [1506, 151], "size": [241.8000030517578, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 14}, {"name": "image", "type": "IMAGE", "link": 15}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [16]}], "properties": {"Node name for S&R": "ImageUpscaleWithModel"}, "widgets_values": []}, {"id": 15, "type": "<PERSON>downNote", "pos": [-8, 456], "size": [312, 152], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["### Learn more about this workflow\n\n> [Upscale Models - ComfyUI_examples](https://comfyanonymous.github.io/ComfyUI_examples/upscale_models/) — Overview\n> \n> [ComfyUI Image Upscale - docs.comfy.org](https://docs.comfy.org/tutorials/basic/upscale) — Upscaling step-by-step tutorial"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [14, 13, 0, 14, 0, "UPSCALE_MODEL"], [15, 8, 0, 14, 1, "IMAGE"], [16, 14, 0, 9, 0, "IMAGE"], [17, 4, 1, 6, 0, "CLIP"], [18, 4, 1, 7, 0, "CLIP"], [19, 4, 2, 8, 1, "VAE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}